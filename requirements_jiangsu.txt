aiofiles==23.2.1
altgraph==0.17.4
annotated-types==0.6.0
anyio==4.3.0
APScheduler==3.10.4
blinker==1.7.0
blosc2==2.6.0
certifi==2024.2.2
charset-normalizer==3.3.2
chinese-calendar==1.8.0
clarabel==0.7.1
click==8.1.7
concurrent-log-handler==0.9.20
contourpy==1.2.0
cvxpy==1.4.2
cycler==0.12.1
dataclasses==0.6
deepdiff==6.7.1
dnspython==2.6.1
ecos==2.0.13
ephem==4.1.5
et-xmlfile==1.1.0
exceptiongroup==1.2.0
fastapi==0.110.0
fonttools==4.50.0
greenlet==3.0.3
h11==0.14.0
h5py==3.11.0
httptools==0.6.1
idna==3.6
igraph==0.10.4
importlib_metadata==7.1.0
isort==5.13.2
itsdangerous==2.1.2
Jinja2==3.1.3
joblib==1.3.2
kiwisolver==1.4.5
llvmlite==0.42.0
loguru==0.7.2
LunarCalendar==0.0.9
Mako==1.3.2
Markdown==3.6
MarkupSafe==2.1.5
matplotlib==3.7.1
msgpack==1.0.8
ndindex==1.8
networkx==3.1
numexpr==2.9.0
numpy==1.26.4
openpyxl==3.1.2
ordered-set==4.1.0
orjson==3.10.0
osqp==0.6.4
packaging==24.0
pandapower==2.13.1
pandas==1.5.3
patsy==0.5.6
peewee==3.17.1
pillow==10.3.0
platformdirs==4.2.0
plotly==5.15.0
portalocker==2.8.2
psutil==5.9.4
py-cpuinfo==9.0.0
pyarmor==8.5.8
pyarmor.cli.core==6.5.1
pyarrow==11.0.0
pybind11==2.12.0
pycryptodome==3.16.0
pycryptodomex==3.16.0
pydantic==2.6.4
pydantic_core==2.16.3
pyinstaller==5.12.0
pyinstaller-hooks-contrib==2024.6
pymongo==4.6.3
pyparsing==3.1.2
python-dateutil==2.9.0.post0
python-dotenv==1.0.1
python-igraph==0.10.4
python-multipart==0.0.9
pytz==2024.1
PyYAML==6.0.1
qdldl==0.1.7.post0
rapidfuzz==3.5.2
requests==2.31.0
scipy==1.12.0
scs==3.2.4.post1
seaborn==0.13.0
six==1.16.0
sniffio==1.3.1
starlette==0.36.3
statsmodels==0.14.0
tables==3.9.2
tenacity==8.2.3
texttable==1.7.0
thefuzz==0.22.1
threadpool==1.3.2
threadpoolctl==3.4.0
tinyaes==1.1.0
tomli==2.0.1
tqdm==4.66.2
typing_extensions==4.10.0
urllib3==2.2.1
uvicorn==0.29.0
uvloop==0.19.0
watchfiles==0.21.0
websockets==12.0
Werkzeug==3.0.2
xarray==2023.7.0
xlrd==2.0.1
xlsxtpl==0.3.1
XlsxWriter==3.1.2
xltpl==0.20
xlwt==1.3.0
yapf==0.40.2
zipp==3.18.1
tzdata==2024.1
lightgbm==4.5.0
PYPOWER==5.1.16
pysolar==0.11
scikit_learn==1.4.0
torch==2.3.0
xgboost==2.1.1
filelock==3.16.1
fsspec==2024.10.0
mpmath==1.3.0
nvidia-cublas-cu12==********
nvidia-cuda-cupti-cu12==12.1.105
nvidia-cuda-nvrtc-cu12==12.1.105
nvidia-cuda-runtime-cu12==12.1.105
nvidia-cudnn-cu12==********
nvidia-cufft-cu12==*********
nvidia-curand-cu12==**********
nvidia-cusolver-cu12==**********
nvidia-cusparse-cu12==**********
nvidia-nccl-cu12==2.20.5
nvidia-nvjitlink-cu12==12.6.77
nvidia-nvtx-cu12==12.1.105
orderly-set==5.2.2
scikit-learn==1.4.0
sympy==1.13.3
triton==2.3.0
uv==0.4.24
