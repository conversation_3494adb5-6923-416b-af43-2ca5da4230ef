from dataclasses import dataclass
from dataclasses import field
from typing import Any
from typing import List


@dataclass
class Response:
    code: int = 200
    msg: str = "success"
    data: Any = field(default_factory=dict)


@dataclass
class PageResponse:
    code: int = 200
    msg: str = "success"
    data: Any = field(default_factory=dict)
    page: int = 1  # 当前页码
    size: int = 100  # 每页显示条数
    total: int = 0  # 总记录数
    pages: int = 0  # 总页数