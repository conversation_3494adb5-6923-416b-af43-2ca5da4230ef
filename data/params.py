import datetime as dt
import os
from dataclasses import dataclass
from dataclasses import field
from typing import Dict
from typing import List
from typing import Optional
from typing import Union

import common
import utils


@dataclass
class GuiHuaTaiParams:
    case_id: str
    tab_name: List[str]


# 新增算例
@dataclass
class SaveCaseParams:
    name: str  # 算例名称
    filename: str  # 文件名称
    id: str  # 文件key
    year: int  # 年份
    content_type: str  # 文件类型
    start_time: str  # 开始时间
    size: int = 0  # 文件大小
    is_short: int = 1  # 1: 长期算例，2: 中短期算例，仅限河南
    comment: Union[str, int] = ''  # 备注
    predict: List = field(default_factory=list)  # 预测设置， 仅限河南保供才有此字段

    def __post_init__(self):
        # 此处做参数校验
        for field in self.__dict__.keys():
            v = getattr(self, field)
            if isinstance(v, str):
                v = v.strip()
                setattr(self, field, v)
        if len(self.name) > 128:
            raise Exception("算例名称长度限制128")
        if len(self.filename) > 512:
            raise Exception("文件名称长度限制512")
        # 检查文件是否存在
        file_name = f"{self.id}_{self.filename}"
        filepath = os.path.join(common.file_tmp_storage, file_name)
        if not os.path.isfile(filepath):
            raise Exception(f"文件路径不存在: {filepath}")
        if self.comment and isinstance(self.comment, int):
            self.comment = str(self.comment)


# 更新算例
@dataclass
class UpdateCaseParams:
    case_id: str  # 算例id
    name: str  # 算例名称
    year: int  # 年份
    start_time: str  # 开始时间
    is_short: int = 1  # 1: 长期算例，2: 中短期算例，仅限河南
    comment: Union[str, int] = ""  # 备注
    predict: List = field(default_factory=list)  # 预测设置， 仅限河南保供才有此字段

    def __post_init__(self):
        if self.comment and isinstance(self.comment, int):
            self.comment = str(self.comment)


# 删除算例
@dataclass
class DeleteCaseParams:
    case_id: str  # 算例id
    debug: bool = False
    is_short: int = 1  # 1: 长期算例，2: 中短期算例，仅限河南


@dataclass
class CaseObjParams:
    case_id: str  # 算例id
    is_short: int = 1  # 1: 长期算例，2: 中短期算例，仅限河南


@dataclass
class StatusCaseParams:
    cases: List[CaseObjParams] = field(default_factory=list)  # 算例列表


@dataclass
class GetStatusCaseParams:
    case_ids: List[str]  # 算例id列表
    is_short: int = 1  # 1: 长期算例，2: 中短期算例，仅限河南


# 分析算例
@dataclass
class AnalyzeCaseParams:
    case_id: str  # 算例id
    area: str = 'jiyuan'
    debug: bool = False
    is_short: int = 1  # 1: 长期算例，2: 中短期算例，仅限河南


# 推演参数
@dataclass
class DeducePowerParams:
    calculate_flag: bool  # 是否需要计算
    case_id: str  # 算例id
    timestep: int  # 推演时刻
    device_name: List  # 故障设备
    feedin: float  # 整个feedin的目标值;
    filter_name: str  # 分区名称
    gas: float  # 燃气机组总目标值
    load: float  # 负荷总目标值
    wind: float  # 风电
    solar: float  # 光伏
    phase_shifter_arg: Dict
    add_reactance_arg: Dict = field(default_factory=dict)
    dc_feedin: Dict = field(default_factory=dict)
    grid_fault: bool = False  # 是否有设备故障
    is_short: int = 1


# 获取指定时刻的断面分析
@dataclass
class DeviceInterfaceParams:
    case_id: str  # 算例id
    time_list: List[int] = field(default_factory=list)


# 启用算例/取消算例
@dataclass
class StartCaseParams:
    case_id: str  # 算例id
    status: int  # 算例状态
    is_short: int = 1  # 1: 长期算例，2: 中短期算例，仅限河南

    def __post_init__(self):
        if self.status not in [2, 4]:
            raise Exception("状态参数错误")


# 获取时序数据
@dataclass
class getSequenceTimeParams:
    case_id: str  # 算例id
    time_list: List[int] = field(default_factory=list)  # 时刻列表


# 替换算例
@dataclass
class ReplaceCaseFileParams:
    old_id: str  # 旧算例id
    filename: str  # 文件名称
    new_id: str  # 新算例id
    size: int = 0  # 文件大小
    is_short: int = 1  # 1: 长期算例，2: 中短期算例，仅限河南

    def __post_init__(self):
        # 此处做参数校验
        for field in self.__dict__.keys():
            v = getattr(self, field)
            if isinstance(v, str):
                v = v.strip()
                setattr(self, field, v)
        if len(self.filename) > 512:
            raise Exception("文件名称长度限制512")
        # 检查文件是否存在
        file_name = f"{self.new_id}_{self.filename}"
        filepath = os.path.join(common.file_tmp_storage, file_name)
        if not os.path.isfile(filepath):
            raise Exception(f"文件路径不存在: {filepath}")


# 登陆
@dataclass
class LoginParams:
    username: str  # 用户名
    password: str  # 密码

    def __post_init__(self):
        self.password = self.password.strip()
        if len(self.password) < 6 or len(self.password) > 20:
            raise Exception(f"密码长度限制为6到20位")


# 登陆信息校验
@dataclass
class LoginValidParams:
    token: str  # token


# 获取设备信息
@dataclass
class CurevDeviceInfoParams:
    case_id: str
    device: Dict


# 获取时序潮流断面信息
@dataclass
class GetTimeTideSurfaceInfo:
    case_id: str  # 算例id
    ele_type: str  # "trafo","line","interface","channel"
    ele_dict: Dict  # 带查询设备信息dict
    rate_mk: List = field(default_factory=list)  # 重过载门槛值

    def __post_init__(self):
        if not self.rate_mk:
            self.rate_mk = [0.8, 1.0]


# 获取全省供电缺口时长
@dataclass
class GetPowerSupplyGapTime:
    case_id: str  # 算例id
    func_type: str = "num_count"  # "num_count": 统计个数小时数据,"sum_count";统计和,"max_count"：统计最大值
    value_type: List = field(default_factory=list)

    def __post_init__(self):
        if not self.value_type:
            self.value_type = ['load_curtailment']


# 获取全省储能调用情况
@dataclass
class GetStorageEnergyUseSituation:
    case_id: str  # 算例id
    area: str
    time_list: List  # 2024-12-26 00:00:00
    is_all: bool = False


# 获取最低供电裕度
@dataclass
class GetPowerMinMarginParams:
    case_id: str  # 算例id
    zone_name_list: List = field(default_factory=list)  # such as :["访晋","泰扬北"]
    freq_h: str = "season"  # "分布统计方法","hour": 0-23小时分布; "month":月度分布; "season":季度分布;


# 获取天的直流功率时序数据
@dataclass
class GetPowerDclineSeriesParams:
    case_id: str  # 算例id
    dc_line_idx: List = field(default_factory=list)
    is_all: bool = False
    time_list: List = field(default_factory=list)


# 获取电网拓扑图
@dataclass
class MapMarginDataParams:
    case_id: str  # 算例id
    time_no: Optional[int] = None  # 时刻
    area: str = "全省"


@dataclass
class ZoneAllIndicatorParams:
    case_id: str  # 算例id
    time_no: int  # 时刻
    area: str = "全省"


@dataclass
class ZoneTrafoCapabilityParams:
    case_id: str  # 算例id
    time_no: int  # 时刻
    area: str = "全省"


# 获取关联通道
@dataclass
class ChannelRelationInfoParams:
    case_id: str  # 算例id
    ele_dict: Dict = field(default_factory=dict)


# 获取负荷数据列表
@dataclass
class ListOperateLoadDataParams:
    page: int = 1
    size: int = 20
    area: str = "全省"
    type: str = "全网"  # 类型，全网，网供
    year: Optional[Union[str, int]] = None  # 年份
    month: Optional[Union[str, int]] = None  # 月份
    date: Optional[Union[str, dt.datetime]] = None  # 日期

    def __post_init__(self):
        if self.date and isinstance(self.date, str):
            self.date = dt.datetime.strptime(self.date, "%Y-%m-%d")


# 历史数据-负荷数据-列表
@dataclass
class HistoryLoadDataParams:
    area: str = "全省"  # 区域
    type: str = "全网"  # 类型
    year: int = 2025  # 年份
    stime: Optional[Union[dt.datetime, str]] = None  # 开始时间
    etime: Optional[Union[dt.datetime, str]] = None  # 结束时间
    scene_date: Optional[Union[dt.datetime, str]] = None  # 场景时间
    # 查询类型，query: 负荷曲线图
    action: str = "query"  # 查询类型
    freq: str = "M"  # M: 按月 Y: 按年 D: 按天
    threshold: float = 0.95
    hours: List = field(default_factory=list)
    months: List = field(default_factory=list)
    confidence: float = 1.0
    is_all: int = 0  # 1:查询全部，0:则不是全部

    def __post_init__(self):
        if self.scene_date and isinstance(self.scene_date, str):
            self.scene_date = dt.datetime.strptime(self.scene_date, "%Y-%m-%d")
        if self.stime and isinstance(self.stime, str):
            self.stime = dt.datetime.strptime(self.stime, "%Y-%m-%d %H:%M:%S")
        if self.etime and isinstance(self.etime, str):
            self.etime = dt.datetime.strptime(self.etime, "%Y-%m-%d %H:%M:%S")


# 历史数据-负荷数据-编辑
@dataclass
class EditLoadDataParams:
    id: int
    area: Optional[str] = None
    type: Optional[str] = None
    datetime: Optional[Union[dt.datetime, str]] = None
    value: Optional[float] = None

    def __post_init__(self):
        if self.datetime and isinstance(self.datetime, str):
            self.datetime = dt.datetime.strptime(self.datetime, "%Y-%m-%d %H:%M:%S")


# 历史数据-负荷数据-删除
@dataclass
class DeleteLoadDataParams:
    id: int  # 数据id


# 获取出力数据列表
@dataclass
class ListOperateOutputDataParams:
    page: int = 1
    size: int = 20
    area: str = "全省"
    type: str = "全部"  # 类型，全网，网供
    year: Optional[Union[int, str]] = None  # 年份
    month: Optional[Union[int, str]] = None  # 月份
    date: Optional[Union[str, dt.datetime]] = None  # 日期

    def __post_init__(self):
        if self.date and isinstance(self.date, str):
            self.date = dt.datetime.strptime(self.date, "%Y-%m-%d")


# 历史数据-出力数据-编辑
@dataclass
class EditOutputDataParams:
    id: int
    area: Optional[str] = None
    type: Optional[str] = None
    datetime: Optional[Union[dt.datetime, str]] = None
    value: Optional[float] = None

    def __post_init__(self):
        if self.datetime and isinstance(self.datetime, str):
            self.datetime = dt.datetime.strptime(self.datetime, "%Y-%m-%d %H:%M:%S")


# 历史数据-出力数据-删除
@dataclass
class DeleteOutputDataParams:
    id: int  # 数据id


# 获取装机数据列表
@dataclass
class ListOperateCapacityDataParams:
    page: int = 1
    size: int = 20
    area: str = "全省"
    type: Optional[str] = None  # 字段类型
    year: Optional[Union[str, int]] = None  # 年份
    month: Optional[Union[str, int]] = None  # 月份
    date: Optional[Union[str, dt.datetime]] = None  # 日期

    def __post_init__(self):
        if self.date and isinstance(self.date, str):
            self.date = dt.datetime.strptime(self.date, "%Y-%m-%d")


# 历史数据-装机数据-删除
@dataclass
class DeleteCapacityDataParams:
    id: int  # 数据id


# 历史数据-装机数据-编辑
@dataclass
class EditCapacityDataParams:
    id: int
    area: Optional[str] = None
    type: Optional[str] = None
    date: Optional[Union[dt.datetime, str]] = None
    value: Optional[float] = None

    def __post_init__(self):
        if self.date and isinstance(self.date, str):
            self.date = dt.datetime.strptime(self.date, "%Y-%m-%d")


# 历史数据-断面数据-编辑
@dataclass
class EditInterfaceDataParams:
    id: int
    name: Optional[str] = None
    date: Optional[Union[dt.datetime, str]] = None
    value: Optional[float] = None

    def __post_init__(self):
        if self.date and isinstance(self.date, str):
            self.date = dt.datetime.strptime(self.date, "%Y-%m-%d")


# 历史数据-装机容量
@dataclass
class GetCapacityDataParams:
    year: Optional[int] = None  # 年份
    area: str = "全省"


# 历史数据-发电量占比
@dataclass
class GetUsualPowerDataParams:
    year: Optional[int] = None  # 年份
    area: str = "全省"


# 历史数据-常规机组-出力曲线图
@dataclass
class GetUsualOutputDataParams:
    area: str = "全省"
    gen_type: str = "全部"
    year: Optional[int] = None  # 年份
    scene_date: Optional[Union[dt.datetime, str]] = None

    def __post_init__(self):
        if self.scene_date and isinstance(self.scene_date, str):
            self.scene_date = dt.datetime.strptime(self.scene_date, "%Y-%m-%d %H:%M:%S")
        if self.gen_type in common.field_map2:
            self.gen_type = common.field_map2.get(self.gen_type, "")


# 历史数据-常规机组-出力分布统计图
@dataclass
class GetUsualOutputDistributeParams:
    area: str = "全省"  # 区域
    gen_type: str = '全部'  # 电源类型
    year: Optional[int] = None  # 年份
    confidence: float = 1.0
    months: List = field(default_factory=list)
    hours: List = field(default_factory=list)
    mode: int = 3  # 1: 新能源，2:常规机组，3:电源特性

    def __post_init__(self):
        if self.gen_type in common.field_map2:
            self.gen_type = common.field_map2.get(self.gen_type, "")


# 历史数据-常规机组-拓扑图
@dataclass
class GetUsualMapParams:
    area: str = "全省"  # 区域
    year: Optional[int] = None  # 年份
    mode: int = 1  # 1: 新能源，2:常规机组,3:电源特性


# 历史数据-常规机组-利用小时统计图
@dataclass
class GetUsualHouRateParams:
    area: str = "全省"  # 区域
    year: Optional[int] = None  # 年份
    gen_type: str = '全部'  # 电源类型

    def __post_init__(self):
        if self.gen_type in common.field_map2:
            self.gen_type = common.field_map2.get(self.gen_type, "")


# 历史数据-新能源-出力曲线
@dataclass
class GetNewEnergyOutputDataParams:
    area: str = "全省"  # 区域
    year: Optional[int] = None  # 年份
    gen_type: List = field(default_factory=list)
    scene_date: Optional[Union[dt.datetime, str]] = None

    def __post_init__(self):
        if not self.gen_type:
            self.gen_type = ["wind", "solar", "stogen_energy_storage"]
        else:
            for index, obj in enumerate(self.gen_type):
                if obj in common.field_map2:
                    self.gen_type[index] = common.field_map2.get(obj)
        if self.scene_date and isinstance(self.scene_date, str):
            self.scene_date = dt.datetime.strptime(self.scene_date, "%Y-%m-%d %H:%M:%S")


# 获取交直流-年送电曲线数据
@dataclass
class ListAcDcYearPowerDataParams:
    name: Optional[str] = None  # 曲线名称
    year: Optional[int] = None  # 年份
    mode: int = 0  # 0: 时序模式，1:日最大值模式


# 获取交直流-日送电曲线数据
@dataclass
class ListAcDcDayPowerDataParams:
    name: Optional[str] = None  # 曲线名称
    date: Optional[Union[dt.date, str]] = None  # 年份
    scene_date: Optional[Union[dt.datetime, str]] = None

    def __post_init__(self):
        if self.date and isinstance(self.date, str):
            self.date = dt.datetime.strptime(self.date, "%Y-%m-%d").date()
        if self.scene_date and isinstance(self.scene_date, str):
            self.scene_date = dt.datetime.strptime(self.scene_date, "%Y-%m-%d %H:%M:%S")


# 获取交直流-功率输送值统计
@dataclass
class ListAcDcPowerDataParams:
    name: Optional[str] = None  # 曲线名称
    year: Optional[int] = None  # 年份
    month: Optional[int] = None  # 月份
    months: Optional[List[int]] = field(default_factory=list)  # 多个月份
    day: Optional[int] = None  # 天
    freq: str = "M"  # 间隔， M: 月，S：季度，H：小时，Y：年份


# 历史数据-交直流-出力分布统计图
@dataclass
class GetAcDcPowerDistributeParams:
    name: str = ""  # 曲线名称
    year: Optional[int] = None  # 年份
    months: List = field(default_factory=list)
    hours: List = field(default_factory=list)


# 获取断面数据列表
@dataclass
class ListOperateInterfaceDataParams:
    page: int = 1
    size: int = 20
    name: str = ""  # 断面名称
    year: Optional[Union[str, int]] = None  # 年份
    month: Optional[Union[str, int]] = None  # 月份
    date: Optional[Union[str, dt.datetime]] = None  # 日期

    def __post_init__(self):
        if self.date and isinstance(self.date, str):
            self.date = dt.datetime.strptime(self.date, "%Y-%m-%d")


# 获取交直流数据列表
@dataclass
class ListOperateAcDcDataParams:
    page: int = 1
    size: int = 20
    name: str = ""  # 断面名称
    year: Optional[Union[str, int]] = None  # 年份
    month: Optional[Union[str, int]] = None  # 月份
    date: Optional[Union[str, dt.datetime]] = None  # 日期

    def __post_init__(self):
        if self.date and isinstance(self.date, str):
            self.date = dt.datetime.strptime(self.date, "%Y-%m-%d")


# 历史数据-断面-潮流统计
@dataclass
class GetInterfaceTideDataParams:
    name: str = ""  # 断面名称
    start_time: str = '2025-01-01'  # 开始时间
    end_time: str = '2025-12-31'  # 结束时间


# 历史数据-厂站数据
@dataclass
class HistoryStationDataParams:
    name: str  # 城市或者区域
    mode: int = 3  # 1: 新能源，2：常规机组，3: 电源特性


# 获取厂站数据列表
@dataclass
class ListOperateStationDataParams:
    page: int = 1
    size: int = 20
    name: Optional[str] = None  # 厂站名称
    city: Optional[str] = None  # 所属城市
    zone: Optional[str] = None  # 所属区域
    is_heat: Optional[Union[bool, str]] = None  # 是否供热
    type: Optional[str] = None  # 类型


# 历史数据-断面数据-列表
@dataclass
class HistoryInterfaceQueryDataParams:
    name: str = ""  # 断面名称
    stime: Optional[Union[dt.datetime, str]] = None  # 开始时间
    etime: Optional[Union[dt.datetime, str]] = None  # 结束时间
    scene_date: Optional[Union[dt.datetime, str]] = None  # 场景时间
    # 查询类型，query: 负荷曲线图
    action: str = "query"  # 查询类型
    freq: str = "M"  # M: 按月
    threshold: float = 0.95
    hours: List = field(default_factory=list)
    months: List = field(default_factory=list)
    confidence: float = 1.0

    def __post_init__(self):
        if self.scene_date and isinstance(self.scene_date, str):
            self.scene_date = dt.datetime.strptime(self.scene_date, "%Y-%m-%d %H:%M:%S")


# 历史数据-断面线路
@dataclass
class HistoryInterfaceLineParams:
    name: Optional[str] = None  # 断面名称


# 历史数据-删除厂站数据
@dataclass
class HistoryDeleteStationParams:
    id: int  # 厂站id


# 历史数据-编辑厂站数据
@dataclass
class HistoryEditStationParams:
    id: int  # 厂站id
    name: Optional[str] = None  # 厂站名称
    type: Optional[str] = None  # 厂站类型
    city: Optional[str] = None  # 所属城市
    zone: Optional[str] = None  # 所属区域
    is_heat: Optional[bool] = None  # 是否供热


# 历史数据-断面数据-删除
@dataclass
class DeleteInterfaceDataParams:
    id: int  # 数据id


# 历史数据-交直流数据-删除
@dataclass
class DeleteAcDcDataParams:
    id: int  # 数据id


# 历史数据-交直流数据-编辑
@dataclass
class EditAcDcDataParams:
    id: int
    name: Optional[str] = None
    date: Optional[Union[dt.datetime, str]] = None
    value: Optional[float] = None

    def __post_init__(self):
        if self.date and isinstance(self.date, str):
            self.date = dt.datetime.strptime(self.date, "%Y-%m-%d")


# 历史数据-写入api
@dataclass
class HistoryPushDataParams:
    type_value: str  # load、output、capacity、interface、acdc、station
    data: List[Dict]  # 数据
    is_full: bool = False  # True: 全量数据覆盖，False：增量更新


# 历史数据-负荷趋势曲线图
@dataclass
class HistoryLoadTrendParams:
    area: str = "全省"  # 区域
    type: str = "全网"  # 类型
    stime: Optional[str] = None  # 开始时间
    etime: Optional[str] = None  # 结束时间
    scene_date: Optional[str] = None  # 场景时间


@dataclass
class RealInterfaceParams:
    ele_name: str = "trafo"
    area: str = "全省"
    voltage: int = 2  # 电压等级筛选，1:全部，2:500，3:220
    inf_type: str = None  # line_inf, transformer_inf, zone220_inf, zone220_trafo


# 历史数据-负荷-修改最大负荷点的数据
@dataclass
class HistoryLoadUpdateMaxParams:
    time: Union[dt.datetime, str]  # 时间点
    area: str
    field_key: str  # "load"、"load_grid"
    old_value: float  # 原值
    new_value: float  # 新值

    def __post_init__(self):
        if isinstance(self.time, str):
            self.time = dt.datetime.strptime(self.time, "%Y-%m-%d %H:%M:%S")
        self.new_value *= 10


# 河南保供用户登录
@dataclass
class UserLoginParams:
    username: str  # 用户名
    password: str  # 密码


# 更新典型场景数据
@dataclass
class UpdateScensParams:
    year: int  # 年份


# 历史模块，获取区外交流和区外直流
@dataclass
class HistoryAcDcParams:
    stime: Optional[Union[dt.datetime, str]] = None
    etime: Optional[Union[dt.datetime, str]] = None

    def __post_init__(self):
        if isinstance(self.stime, str):
            self.stime = dt.datetime.strptime(self.stime, "%Y-%m-%d %H:%M:%S")
        else:
            current_year = dt.datetime.now().year
            self.stime = dt.datetime(current_year, 1, 1, 0, 0, 0)
        if isinstance(self.etime, str):
            self.etime = dt.datetime.strptime(self.etime, "%Y-%m-%d %H:%M:%S")
        else:
            self.etime = dt.datetime.now()


# 断面历史时刻功率查询;  输入参数：时刻数据：
@dataclass
class InterfacePowerParams:
    start_time: Union[dt.datetime, str]
    end_time: Union[dt.datetime, str]
    inf_type: str = "line_df"  # line_inf, transformer_inf, zone220_inf, zone220_trafo
    page: int = 1  # 页码，从1开始
    size: int = 100  # 每页显示条数，默认100条

    def __post_init__(self):
        if self.start_time and isinstance(self.start_time, str):
            self.start_time = dt.datetime.strptime(self.start_time, "%Y-%m-%d %H:%M:%S")
        if self.end_time and isinstance(self.end_time, str):
            self.end_time = dt.datetime.strptime(self.end_time, "%Y-%m-%d %H:%M:%S")

        # 验证分页参数
        if self.page < 1:
            self.page = 1
        if self.size < 1:
            self.size = 100


# 区分断面电压-获取断面数据
@dataclass
class ListInterfaceFiffLevelDataParams:
    stime: str
    etime: str
    zone: str = ""