import json
import os
from datetime import datetime
from time import sleep
from typing import Dict
from typing import Optional
from urllib import parse

import requests
from loguru import logger


class ApiHandler:

    # 时序模拟默认参数字典 可传入自定义字典来更新该字典
    default_data_value_dict = {
        "up_reserve_cof": 5,
        "down_reserve_cof": "2",
        "emergency_reserve_cof": "0",
        "wind_reserve_cof": 20,
        "solar_reserve_cof": 20,
        "consider_reserve_flag": True,
        "consider_start_shut_flag": True,
        "consider_ramp_flag": True,
        "consider_branch_flag": True,
        "consider_interface_flag": True,
        "roll_snap": "24",
        "lookahead_snap": "0",
        "mip_gap": "0.5",
        "max_parallel_num_bool": True,
        "max_parallel_num": 32,
        "max_rollback_bool": True,
        "max_rollback": 180,
        "solver_timelimit_bool": True,
        "solver_timelimit": "300",
        "wind_curtail_cof_bool": True,
        "wind_curtail_cof": "5000",
        "load_relax_cof_bool": True,
        "load_relax_cof": "7000",
        "pbr_relax_cof_bool": True,
        "pbr_relax_cof": "6000",
        "overlap_number_bool": True,
        "overlap_number": 120,
        "solar_curtail_cof_bool": True,
        "solar_curtail_cof": "5000",
        "planning_max_seconds_bool": True,
        "planning_max_seconds": 3600,
        "api_address_bool": True,
        "api_address": "",
        "api_token": "",
        "pcs_model": "全社会运行成本最低",
        "consider_transmission_losses": False,
        "zero_beginning_onoff_cost_per_process": False,
        "upscaled_solver_timelimit_bool": False,
        "upscaled_solver_timelimit": 180,
        "hydropower_curtail_cof_bool": True,
        "hydropower_curtail_cof": 6000,
        "max_num_jobs_bool": True,
        "max_num_jobs": 8
    }

    @staticmethod
    def now():
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    def __init__(
        self,
        base_url: str,
        xlsx_file_path: str,
        case_file_note: str = "",
        download_path: Optional[str] = None,
        data_value_dict: Optional[Dict] = None,
    ):
        """
        :param base_url: teap服务api
        :param xlsx_file_path: 算例文件路径
        :param download_path: 结果文件下载位置
        :param case_file_note: 算例备注
        """
        self.base_url = base_url
        logger.info(f"{self.now()}: 服务端地址: {self.base_url}")
        self.xlsx_file_path = xlsx_file_path
        logger.info(f"{self.now()}: 本地算例路径: {self.xlsx_file_path}")
        self.case_file_note = case_file_note
        self.data_value_dict = self.default_data_value_dict
        if data_value_dict is not None:
            logger.info(f"{self.now()}: 检测到自定义计算参数")
            self.data_value_dict.update(data_value_dict)

        self.case_file_name = None
        self.case_id = None
        self.download_path = download_path

    def upload_case_file(self):
        files = {'file': open(self.xlsx_file_path, 'rb')}
        try:
            response_a = requests.post(self.base_url + "/teap_api/upload_case_file/", files=files)
            self.case_file_name = response_a.json().get("file_name")
            task_record_id = response_a.json().get("task_record_id")
            logger.info(f"{self.now()}: 上传成功，文件名：{self.case_file_name}, task_record_id = [{task_record_id}]")
            response_b = requests.post(
                self.base_url + "/teap_api/activate_case_file/",
                headers={'Content-Type': 'application/json'},
                data=json.dumps(
                    {
                        "task_record_id_list": [task_record_id],
                        "job_type_id": 0,
                        "case_file_note": self.case_file_note,
                        "job_input_args": {
                            "data_value_dict": self.data_value_dict
                        }
                    }
                )
            )
            logger.info(f"{self.now()}: 接口返回信息: {response_b.json()}")
        except Exception as e:
            logger.error(f"{self.now()}: upload_case_file error: {e}")

    def start_case_file(self):
        try:
            response_a = requests.post(
                self.base_url + "/teap_api/task_table_view/",
                headers={'Content-Type': 'application/json'},
                data=json.dumps({
                    "finished_data_page_current": 1,
                    "finished_data_page_size": 10
                })
            )
            unfinished_data_list = response_a.json().get("unfinished_data_list")
            unfinished_data_dict = [
                item for item in unfinished_data_list if item["case_file_name"] == self.case_file_name
            ][0]
            self.case_id = unfinished_data_dict["id"]
            logger.info(f"{self.now()}: case_id = [{self.case_id}]")
            response_b = requests.post(
                self.base_url + "/teap_api/start_case_file/",
                headers={'Content-Type': 'application/json'},
                data=json.dumps({"task_record_id_list": [self.case_id]})
            )
            logger.info(f"{self.now()}: 接口返回信息: {response_b.json()}")
        except Exception as e:
            logger.info(f"{self.now()}: start_case_file error: {e}")

    def check_is_finished(self):
        while 1:
            try:
                response_a = requests.post(
                    self.base_url + "/teap_api/task_table_view/",
                    headers={'Content-Type': 'application/json'},
                    data=json.dumps({
                        "finished_data_page_current": -1,
                        "finished_data_page_size": 10
                    })
                )
                unfinished_data_list = [
                    item for item in response_a.json().get("unfinished_data_list") if item["id"] == self.case_id
                ]
                if len(unfinished_data_list) > 0:
                    unfinished_data_dict = unfinished_data_list[0]
                    job_exec_percent = unfinished_data_dict["job_exec_percent"]
                    logger.info(f"{self.now()}: 计算进度 [{job_exec_percent}%]")
                    sleep(5)
                    continue
                else:
                    finished_data_list = [
                        item for item in response_a.json().get("finished_data_list") if item["id"] == self.case_id
                    ]
                    if len(finished_data_list) > 0:
                        logger.info(f"{self.now()}: 计算完成")
                        break
            except Exception as e:
                logger.error(f"{self.now()}: check_is_finished error: {e}")

    def fetch_teap_file(self):
        logger.info(f"{self.now()}: 尝试保存 teap 文件")
        teap_file_name = ""  # 结果文件路径
        while 1:
            try:
                response = requests.post(
                    self.base_url + f"/teap_api/download_task_result/{self.case_id}/",
                )
                if 'Content-Disposition' not in response.headers:
                    logger.info(f"{self.now()}: 接口返回信息: {response.json()}")
                    sleep(5)
                    continue
                content_disposition = response.headers['Content-Disposition']
                teap_file_name = parse.unquote(str(content_disposition).split("filename=")[-1])
                logger.info(f"{self.now()}: teap_file_name = {teap_file_name}")
                if self.download_path:
                    # 如果传入下载路径，则拼接路径
                    teap_file_name = os.path.join(self.download_path, teap_file_name)
                with open(teap_file_name, 'wb') as f:
                    f.write(response.content)
                break
            except Exception as e:
                logger.error(f"{self.now()}: fetch_teap_file error: {e}")
        return teap_file_name


if __name__ == '__main__':

    api_handler = ApiHandler(
        base_url="http://brandy_v2.teap.tode.ltd",
        xlsx_file_path="/root/project/baogong-api/db/origin/336.xlsx",
        case_file_note="brandy test",
        data_value_dict=None,
    )

    api_handler.upload_case_file()
    api_handler.start_case_file()
    api_handler.check_is_finished()
    api_handler.fetch_teap_file()
