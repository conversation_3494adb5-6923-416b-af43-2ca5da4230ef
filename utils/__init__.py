import datetime as dt
import hashlib
import json
import os
import shutil
import time
import uuid
from collections import defaultdict
from decimal import Decimal
from itertools import product
from typing import Dict
from typing import Tuple

import h5py
import numpy as np
import pandapower as pp
import pandas as pd
from loguru import logger
from sqlalchemy import text

import common
from alg.common_utils import data_utils
from alg.ssmetrics_main import TeapAna_main
from data import Response, PageResponse
from database import SessionLocal
from database import redis_obj
from database.models import *
from qs.histime_utils import hisdata_utils
from qs.main_qs_ana import CCaseShorterm
from qs.main_qs_ana import load_config_file
from utils.teap_service import ApiHandler


def response_obj() -> Response:
    return Response()


def page_response_obj() -> PageResponse:
    return PageResponse()


def get_db():
    """
    获取数据库的连接
    :return:
    """
    db = None
    try:
        db = SessionLocal()
        yield db
        db.commit()
    except Exception as e:
        db.rollback()
        raise
    finally:
        if db:
            db.close()


def get_current_datetime() -> str:
    """
    获取当前时间
    :return:
    """
    nowtime = str(dt.datetime.now()).split('.')[0]
    return nowtime


def get_db_data(case_id: Optional[str] = None, is_short: int = 1) -> Dict:
    """
    获取单个算例信息
    :param case_id: 算例id
    :param is_short: 算例id, 1: 长期算例表, 2: 中短期，仅限河南存在
    """
    db_data = defaultdict(dict)
    if is_short == 1:
        db_file = common.db_path
    else:
        db_file = common.db_short_file
    if not os.path.isfile(db_file):
        return db_data
    with open(db_file, 'r') as fp:
        content = fp.read()
        if content:
            db_data = json.loads(content)
    if case_id:
        case = db_data.get(case_id, {})
        return case
    return db_data


def get_token() -> Dict:
    """
    获取token登陆信息
    """
    login_data = {}
    if not os.path.isfile(common.db_login):
        return login_data
    with open(common.db_login, 'r') as fp:
        txt = fp.read()
        if txt:
            login_data = json.loads(txt)
    return login_data


def save_token(data: Dict):
    """
    保存token登陆信息
    """
    with open(common.db_login, 'w') as fp:
        fp.write(json.dumps(data, indent=4, ensure_ascii=False))
    return


def clear_case_files(case_id: str, filename: str):
    """
    删除成功时需要清除之前存储的算例文件
    """
    # 删除成功时需要清除之前存储的算例文件
    file_path = os.path.join(common.file_storage, f"{case_id}_{filename}")
    if os.path.isfile(file_path):
        os.remove(file_path)
    network_file = os.path.join(common.db_network_dir, f"{case_id}.json")
    if os.path.isfile(network_file):
        os.remove(network_file)
    output_file = os.path.join(common.db_output_dir, f"{case_id}.h5")
    if os.path.isfile(output_file):
        os.remove(output_file)
    cal_result_file = os.path.join(common.cal_result_path, f"{case_id}.json")
    if os.path.isfile(cal_result_file):
        os.remove(cal_result_file)
    case_info_file = os.path.join(common.case_info_path, f"{case_id}.json")
    if os.path.isfile(case_info_file):
        os.remove(case_info_file)
    indicator_file = os.path.join(common.indicator_path, f"{case_id}.json")
    if os.path.isfile(indicator_file):
        os.remove(indicator_file)
    ptdf_file = os.path.join(common.db_ptdf_dir, f"{case_id}.h5")
    if os.path.isfile(ptdf_file):
        os.remove(ptdf_file)


def save_db_data(db_data: Dict, is_short: int = 1):
    """
    保存算例数据
    """
    if is_short == 1:
        db_file = common.db_path
    else:
        db_file = common.db_short_file
    with open(db_file, 'w') as fp:
        fp.write(json.dumps(db_data, indent=4, ensure_ascii=False))
    return


def calculate_size(v: int) -> str:
    """
    计算单位
    :param value:
    :return:
    """
    value = v
    if v > 1024:
        value = value / 1024
        v = v // 1024
    else:
        return f"{value}B"
    if v > 1024:
        value = value / 1024
        v = v // 1024
    else:
        return f"{round(value, 2)}KB"
    if v > 1024:
        value = value / 1024
        v = v // 1024
    else:
        return f"{round(value, 2)}MB"
    return f"{round(value, 2)}GB"


class JsonEncoder(json.JSONEncoder):
    """Convert numpy classes to JSON serializable objects."""
    def default(self, obj):
        if isinstance(obj, (np.integer, np.floating, np.bool_)):
            return obj.item()
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, float):
            return Decimal(str(obj))
        else:
            return super(JsonEncoder, self).default(obj)


def callback_alg_save_data(
    case_id: str,
    data: Dict,
    status: int = 2,
    log_file: Optional[str] = None,
    is_short: int = 1,
    is_auto_start: bool = False
):
    """
    算法分析统计数据回调函数,主要用于保存算法统计出来的数据到mongodb
    :param case_id: 算例id
    :param data: 算例数据， base: 基础数据, series: 时序数据, indicator: 指标数据
    :param status:算例分析状态,0: 未分析,1:分析中,2:已分析,3: 分析失败,4:已启用
    :param result: 返回值
    :return
    """
    data['status'] = status
    # 获取锁
    common.lock.acquire()
    db_data = get_db_data(is_short=is_short)
    case = db_data.get(case_id)
    if not case:
        logger.error(f"回调函数中未查询到算例数据")
        return
    try:
        if status == 3:
            case['status'] = 3
            db_data[case_id] = case
            save_db_data(db_data, is_short=is_short)
            return
        case_info = {}
        cal_result = {}
        if 'network' in data:
            network = data.pop("network")
            filename = os.path.join(common.db_network_dir, f"{case_id}.json")
            pp.to_json(network, filename)
        if 'result_output' in data:
            result_output = data.pop('result_output', {})
            filename = os.path.join(common.db_output_dir, f"{case_id}.h5")
            with h5py.File(filename, 'w') as fp:
                for k, v in result_output.items():
                    fp.create_dataset(k, data=v, compression="gzip", compression_opts=9)
        if 'ptdf' in data:
            ptdf = data.pop('ptdf')
            ptdf_filename = os.path.join(common.db_ptdf_dir, f"{case_id}.h5")
            with h5py.File(ptdf_filename, 'w') as fp:
                fp.create_dataset('ptdf', data=ptdf, compression="gzip", compression_opts=9)
        if 'cal_result' in data:
            cal_result = data.pop('cal_result')
            cal_result_value = json.dumps(cal_result, indent=4, ensure_ascii=False, cls=JsonEncoder)
            cal_result_filename = os.path.join(common.cal_result_path, f"{case_id}.json")
            with open(cal_result_filename, 'w') as fp:
                fp.write(cal_result_value)
        if 'case_info' in data:
            case_info = data.pop('case_info')
            case_info_value = json.dumps(case_info, indent=4, ensure_ascii=False, cls=JsonEncoder)
            case_info_filename = os.path.join(common.case_info_path, f"{case_id}.json")
            with open(case_info_filename, 'w') as fp:
                fp.write(case_info_value)
        if 'indicator' in data:
            indicator = data.pop('indicator')
            indicator_value = json.dumps(indicator, indent=4, ensure_ascii=False, cls=JsonEncoder)
            indicator_filename = os.path.join(common.indicator_path, f"{case_id}.json")
            with open(indicator_filename, 'w') as fp:
                fp.write(indicator_value)
        for k, v in data.items():
            if k == 'status':
                case['status'] = v
            else:
                case[k] = json.dumps(v, ensure_ascii=False, cls=JsonEncoder)
        if status == 2:
            # 获取时序安排值
            time_range = case_info.get("time_range", [])
            case['time_value'] = len(time_range)
            # 获取最高负荷值
            extreme_data = data_utils.get_grid_extreme_value_info(
                case_info_dict=case_info, cal_result_dict=cal_result
            )
            max_load_p_value = extreme_data.get("max_load_p", [])
            if max_load_p_value:
                case['max_load'] = max_load_p_value[0]
            else:
                case['max_load'] = 0
            # 获取集中式新能源装机
            allgen_data = data_utils.get_allgen_capacity(case_info_dict=case_info)
            new_energy_zhuangji = 0
            for obj in allgen_data:
                if obj['name'] in ['光伏', '风电']:
                    new_energy_zhuangji += obj['value']
            case['new_energy_zhuangji'] = round(new_energy_zhuangji, 2)
        # 保存数据
        db_data[case_id] = case
        short_predict = case.get("predict", [])
        if is_short == 2 and short_predict:
            s1 = datetime.strptime(short_predict[0], '%Y-%m-%d %H:%M:%S').date()
            # 中短期算例时，需要检查预测开始时间，同一天不可有有两个及以上存在
            for short_case_id in list(db_data.keys()):
                if short_case_id == case_id:
                    continue
                short_case = db_data.get(short_case_id, {})
                other_predict = short_case.get("predict", [])
                if not other_predict:
                    continue
                other_predict_stime = other_predict[0]
                s2 = datetime.strptime(other_predict_stime, '%Y-%m-%d %H:%M:%S').date()
                if s1 == s2:
                    # 若是已加载到内存中，需要删除内存中数据
                    if short_case_id in common.short_case_dict:
                        del common.short_case_dict[short_case_id]
                    # 从算例字典中删除这个重复日期的数据
                    del db_data[short_case_id]
                    logger.info(f"成功删除中短期、重复日期的算例: {short_case.get('name')}")
        save_db_data(db_data, is_short=is_short)
        if is_auto_start:
            try:
                case['status'] = 4
                db_data[case_id] = case
                # 此处，需要把其他已启用的中短期算例均停止，仅启用当前的这个中短期算例
                for case_id_k, case_obj in db_data.items():
                    if case_id_k == case_id:
                        continue
                    if case_obj['status'] == 4:
                        case_obj['status'] = 2
                save_db_data(db_data, is_short=is_short)
                read_case(case_id=case_id, is_network=True, is_output=True, is_short=is_short)
            except Exception as e:
                logger.error(f"自动启用失败: {e}")
    except Exception as e:
        if log_file:
            logger.add(log_file)
        case['status'] = 3
        db_data[case_id] = case
        save_db_data(db_data, is_short=is_short)
        logger.exception(e)
    finally:
        # 释放锁
        common.lock.release()
        logger.info(f"分析算例结束")
    return


def read_case(case_id: str, is_network: bool = False, is_output: bool = False, is_short: int = 1) -> Response:
    """
    读取算例信息，并添加缓存
    :param case_id: 算例id
    :param is_network: 是否读取网络数据
    :param is_output: 是否读取结果集
    :param is_short: 1: 长期算例, 2: 中短期算例，仅限河南
    :return:
    """
    result = Response()
    if is_short == 1:
        db_file = common.db_path
    else:
        db_file = common.db_short_file
    if not os.path.isfile(db_file):
        result.code = 1010
        result.msg = '暂无数据'
        return result
    # 获取锁
    common.lock.acquire()
    try:
        db_data = get_db_data(is_short=is_short)
        case_obj = db_data.get(case_id)
        if not case_obj:
            result.code = 1001
            result.msg = '未查询到算例信息'
            return result
        # 添加缓存并返回算例信息
        if is_network:
            network_file = os.path.join(common.db_network_dir, f"{case_id}.json")
            network = pp.from_json(network_file)
            case_obj['network'] = network
        if is_output:
            filename = os.path.join(common.db_output_dir, f"{case_id}.h5")
            with h5py.File(filename, 'r') as h5_file:
                output = {}
                for key in h5_file.keys():
                    v = h5_file[key]
                    output[key] = np.array(v)
                case_obj['output'] = output
        # 读取ptdf
        ptdf_filename = os.path.join(common.db_ptdf_dir, f"{case_id}.h5")
        if os.path.isfile(ptdf_filename):
            with h5py.File(ptdf_filename, 'r') as ptdf_file:
                case_obj['ptdf'] = ptdf_file['ptdf'][:]
        # 读取cal_result
        cal_result_filename = os.path.join(common.cal_result_path, f"{case_id}.json")
        if os.path.isfile(cal_result_filename):
            with open(cal_result_filename, 'r') as cal_result_file:
                cal_result_content = cal_result_file.read()
                case_obj['cal_result'] = json.loads(cal_result_content)
        # 读取case_info
        case_info_filename = os.path.join(common.case_info_path, f"{case_id}.json")
        if os.path.isfile(case_info_filename):
            with open(case_info_filename, 'r') as case_info_file:
                case_info_content = case_info_file.read()
                case_obj['case_info'] = json.loads(case_info_content)
        # indicator
        indicator_filename = os.path.join(common.indicator_path, f"{case_id}.json")
        if os.path.isfile(indicator_filename):
            with open(indicator_filename, 'r') as indicator_file:
                indicator_content = indicator_file.read()
                case_obj['indicator'] = json.loads(indicator_content)
        result.data = case_obj
        # 获取系统设置的缓存算例的数量
        if not os.path.isfile(common.db_system_path):
            cache_number = 1
        else:
            with open(common.db_system_path, 'r') as fp:
                content = fp.read()
                if not content:
                    cache_number = 1
                else:
                    contnet = json.loads(content)
                    cache_number = contnet.get('cache_number', 1)
        # 内存缓存中仅缓存三个算例信息，超过三个，则剔除一个，再把当前算例加入缓存
        # 短期算例缓存，特殊处理，和正常算例缓存不在一起
        if is_short == 2:
            common.short_case_dict = {case_id: case_obj}
            logger.info(f'中短期算例case_id[{case_id}] 添加缓存成功')
        else:
            if case_id in common.case_dict:
                common.case_dict[case_id] = case_obj
            else:
                if len(common.case_dict) >= cache_number:
                    key = list(common.case_dict.keys())[0]
                    common.case_dict.pop(key)
                common.case_dict[case_id] = case_obj
            logger.info(f'case_id[{case_id}] 添加缓存成功')
    finally:
        # 释放锁
        common.lock.release()
    return result


def valid_token(token: str) -> Response:
    """
    校验token是否有效
    """
    login_data = get_token()
    if token not in login_data:
        return Response(code=1001, msg="尚未登陆")
    login_info = login_data.get(token, {})
    etime = login_info.get("etime")
    if not etime:
        return Response(code=1001, msg="登陆已过期")
    new_etime = dt.datetime.strptime(etime, "%Y-%m-%d %H:%M:%S")
    nowtime = dt.datetime.now()
    if nowtime > new_etime:
        return Response(code=1001, msg="登陆已过期")
    return Response(data=login_info)


def valid_henan_token(token: str) -> Response:
    """
    校验河南token是否有效
    """
    db = SessionLocal()
    try:
        sql = f"""
            SELECT last_login_time
            FROM sys_user_login
            WHERE token = "{token}"
        """
        sql_resp = db.execute(text(sql)).fetchone()
        if not sql_resp:
            return Response(code=1001, msg="尚未登陆")
        last_login_time = sql_resp[0]
        nowtime = dt.datetime.now()
        last_login_time = dt.datetime.strptime(last_login_time, "%Y-%m-%d %H:%M:%S") + dt.timedelta(
            hours=common.user_login_expire_time
        )
        if nowtime > last_login_time:
            return Response(code=1001, msg="登陆已过期")
        return Response()
    finally:
        db.close()


def clear_invalid_token():
    """
    清除已过期的token信息
    """
    login_data = get_token()
    nowtime = dt.datetime.now()
    for token in list(login_data.keys()):
        item = login_data.get(token)
        if not isinstance(item, dict):
            continue
        etime = item.get("etime")
        if not etime:
            del login_data[token]
            continue
        new_etime = dt.datetime.strptime(etime, "%Y-%m-%d %H:%M:%S")
        if nowtime > new_etime:
            del login_data[token]
    save_token(login_data)


def auto_analyze_case():
    """
    自动计算算例
    """
    if common.is_redis_lock in [1, "1"]:
        if not redis_obj.set(common.redis_short_analyze_key, 1, nx=True, ex=300):
            logger.info(f"解析中短期算例未获取到redis锁")
            return
        logger.info(f"解析中短期`算例已获取到redis锁")
    if common.short_qs_analyze_status:
        logger.info(f'存在其他任务正在执行解析中短期qs文件')
        return
    logger.info(f"开始解析中短期qs文件")
    common.short_qs_analyze_status = True
    try:
        # 获取当前日期
        nowtime = dt.datetime.now()
        date = str(nowtime.date())
        year = nowtime.year
        # 检查算例文件是否存在
        target_case_dir = os.path.join(common.teap_case_origin_path, date)
        logger.info(f'target_case_dir====================>{target_case_dir}')
        os.makedirs(target_case_dir, exist_ok=True)  # 创建目录
        qs_config_file = os.path.join(common.base_path, f"qs/{common.realtime_qs_config_yml}")
        config = load_config_file(qs_config_file)
        # 调用qs
        for file in sorted(os.listdir(common.short_qs_dir), reverse=True):
            if not file.endswith((".QS", ".qs")):
                continue
            logger.info(f"解析中短期目标qs文件: {file}")
            short_case = CCaseShorterm(
                qs_path=common.short_qs_dir,
                qs_name=file,
                config=config,
                case_file_path=target_case_dir,
                longterm_case_xlsx_path=common.henan_long_case_dir
            )
            bsuc, _ = short_case.create_teapcase()
            if not bsuc:
                logger.debug(f"{file} 生成算例失败")
                continue
        # 检查目标目录下是否存在新的算例文件
        origin_case_path = ""
        origin_case_filename = ""
        for file in os.listdir(target_case_dir):
            if not file.endswith(".xlsx"):
                continue
            origin_case_path = os.path.join(target_case_dir, file)
            origin_case_filename = file
            break
        if not origin_case_path:
            logger.error(f'{date}未发现原始算例文件')
            return
        # teap服务api地址
        teap_base_url = os.getenv("TEAP_URL")
        if not teap_base_url:
            logger.error(f"未获取到teap的服务地址")
            return
        api_handler = ApiHandler(
            base_url=teap_base_url,
            xlsx_file_path=origin_case_path,
            case_file_note="auto",
            download_path=common.file_tmp_storage
        )
        api_handler.upload_case_file()
        api_handler.start_case_file()
        api_handler.check_is_finished()
        result_file = api_handler.fetch_teap_file()
        logger.info(f"result_file===========================>{result_file}")
        result_file_name = os.path.basename(result_file)
        logger.info(f"result_file_name================================>{result_file_name}")
        result_file_size = os.path.getsize(result_file)  # 结果文件大小
        hash_value = str(uuid.uuid4())
        new_file_name = f"{hash_value}_{result_file_name}"
        new_target_file = os.path.join(common.file_storage, new_file_name)
        logger.info(f"new_target_file===============================>{new_target_file}")
        # 移动文件
        shutil.move(result_file, new_target_file)
        # 清空临时文件夹
        shutil.rmtree(common.file_tmp_storage)
        os.makedirs(common.file_tmp_storage, exist_ok=True)
        # 获取文件名称中的开始时间
        file_date = origin_case_filename.strip(".xlsx").split("_")[-1]
        if file_date and len(file_date) == 8:
            predict_stime = f"{file_date[:4]}-{file_date[4:6]}-{file_date[6:]} 00:00:00"
            stime = dt.datetime.strptime(predict_stime, "%Y-%m-%d %H:%M:%S")
            etime = (stime + dt.timedelta(days=7)).date()
            predict_etime = f"{etime} 23:00:00"
        else:
            # 预测开始时间
            new_date = nowtime.date()
            predict_stime = f"{new_date} 00:00:00"
            # 预测结束时间
            etime = (nowtime + dt.timedelta(days=7)).date()
            predict_etime = f"{etime} 23:00:00"
        new_time = get_current_datetime()

        new_case = {
            "name": result_file_name,
            "filename": result_file_name,
            "id": hash_value,
            "year": year,
            "content_type": "",
            "start_time": date,
            "size": result_file_size,
            "is_short": 2,
            "comment": "系统自动生成",
            "predict": [predict_stime, predict_etime],
            "create_time": new_time,
            "update_time": new_time,
            "is_delete": 0,
            "status": 0  # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
        }
        logger.info(f"new_case==============================>{new_case}")
        db_data = get_db_data(is_short=2)
        db_data[hash_value] = new_case
        save_db_data(db_data=db_data, is_short=2)

        # 开始分析算例
        # 生成分析的日志文件
        log_file = os.path.join(common.case_logs_dir, f'{hash_value}.log')
        args = {
            'case_id': hash_value,
            'case_file': new_target_file,
            'case_address': "henan",
            'tofile': False,
            'case_start_time': predict_stime,
            'func': callback_alg_save_data,
            'log_file': log_file,
            'is_short': 2,
            'is_auto_start': True
        }
        logger.info(f"定时任务-分析算例参数: {args}")
        TeapAna_main(**args)
        try:
            # 解析完成之后删除对应的qs文件
            shutil.rmtree(common.short_qs_dir)
            os.makedirs(common.short_qs_dir, exist_ok=True)
            # 解析完成之后，删除原始算例目录
            shutil.rmtree(target_case_dir)
        except Exception as e:
            logger.exception(e)
        db = SessionLocal()
        try:
            short_qs_obj = db.query(SysShortQs).filter(SysShortQs.is_delete == 0).first()
            if short_qs_obj:
                short_qs_obj.value = 0
            db.commit()
        except Exception as e:
            logger.error(e)
        finally:
            db.close()
        logger.info(f"中短期qs文件解析完成")
    except Exception as e:
        logger.exception(e)
    finally:
        common.short_qs_analyze_status = False
        if common.is_redis_lock in [1, "1"]:
            redis_obj.delete(common.redis_short_analyze_key)
            logger.info(f"解析中短期qs文件释放redis锁成功")
    return


def process_sql_resp(
    df: pd.DataFrame,
    is_field_key: bool = False,
    is_unit: bool = False,
    val_name: str = "value",
    is_filter: bool = False,
    is_display_desc: bool = False,
    is_day_time: bool = False,
    is_columns: List[str] = []
) -> pd.DataFrame:
    """
    is_day_time: true: 表示相同的day_time行，对应的value开头的列相加
    """
    value_list = []
    time_list = []
    field_keys = []
    display_desc_list = []
    if is_filter:
        if is_display_desc:
            df = df[[
                col for col in df.columns
                if col.startswith("value_") or col in (["data_year", "day_time", "display_desc"] + is_columns)
            ]]
        else:
            df = df[[
                col for col in df.columns
                if col.startswith("value_") or col in (["data_year", "day_time"] + is_columns)
            ]]
    if is_day_time and not df.empty:
        value_columns = [col for col in df.columns if col.startswith("value_")]
        df = df.groupby(["data_year", "day_time"])[value_columns].sum().reset_index()
    pending_dict = defaultdict(list)

    def generate_row(row):
        year = row['data_year']
        day = row['day_time']
        for v in row.index:
            if not v.startswith("value_"):
                continue
            t_str = v.split("_")[-1]
            t_full = f"{year}-{day} {t_str[:2]}:{t_str[2:]}:00"
            value = row[v]
            if is_unit:
                value *= 0.1
            value_list.append(value)
            time_list.append(t_full)
            if is_field_key:
                field_keys.append(row['field_key'])
            if is_display_desc:
                display_desc_list.append(row['display_desc'])
            for column in is_columns:
                pending_dict[column].append(row[column])

    df.apply(generate_row, axis=1)
    if is_field_key:
        new_data = {"时间": time_list, val_name: value_list, "field_key": field_keys}
    else:
        new_data = {"时间": time_list, val_name: value_list}
    if is_display_desc:
        new_data["display_desc"] = display_desc_list
    if is_columns:
        new_data.update(pending_dict)
    df = pd.DataFrame(new_data).fillna(0)
    df["时间"] = pd.to_datetime(df['时间'], format='%Y-%m-%d %H:%M:%S')
    df.sort_values(by="时间", inplace=True)
    return df


def safe_merge(df1: pd.DataFrame, df2: pd.DataFrame, on: str = '时间', how: str = 'left'):
    """
    安全地合并两个DataFrame，只有当两个DataFrame都是有效的DataFrame且不为空时才进行合并操作。

    :param df1: 第一个DataFrame
    :param df2: 第二个DataFrame
    :param on: 用于合并的列名，默认为'时间'
    :param how: 合并方式，默认为'outer'
    :return: 合并后的DataFrame，如果不满足条件则返回None
    """
    df1['时间'] = pd.to_datetime(df1['时间'], format='%Y-%m-%d %H:%M:%S')
    df2['时间'] = pd.to_datetime(df2['时间'], format='%Y-%m-%d %H:%M:%S')
    # 检查 df1 是否为空
    if df1.empty and not df2.empty:
        result = df2.copy()
    # 检查 df2 是否为空
    elif df2.empty and not df1.empty:
        result = df1.copy()
    # 两个都不为空，进行合并操作
    else:
        result = pd.merge(df1, df2, on=on, how=how)

    # 用 0 填充缺失值
    result = result.fillna(0)
    return result


def process_scene(year: Optional[int] = None, is_unit: bool = False):
    """
    处理场景数据
    :param is_unit: 是否转换单位, 乘以0.1
    :param year: 年份
    :return:
    """
    db = SessionLocal()
    try:
        if not year:
            year = dt.datetime.now().year
        # 查询分区
        area_sql = f"""
        SELECT
        DISTINCT area_name
        FROM sys_dev_id_map_tab
        WHERE area_name not in ("全省", "豫中东", "豫北", "豫南","豫西", "豫西/豫中东")
        """
        areas = ["全省", "豫中东", "豫北", "豫南", "豫西"]
        area_resp = db.execute(text(area_sql)).fetchall()
        for area in area_resp:
            areas.append(area[0])
        # 查询每个分区对应的field_key的测点id
        field_keys = [
            "load", "wind", "solar", "reserve_up", "reserve_down", "feedin", "wind_curtailment",
            "solar_curtailment"
        ]
        base_dict = defaultdict(str)
        for area in areas:
            if area in ["豫中东", "豫北", "豫南", "豫西"]:
                # load 负荷
                load_sql = f"""
                    SELECT
                    *
                    FROM
                    sys_load_data_tab sload
                    WHERE
                    sload.area_name in (
                    SELECT
                    city_name
                    FROM sys_city_code_tab
                    WHERE area_name = "{area}"
                    )
                    AND sload.dev_id in (
                    SELECT
                    dev_id
                    FROM sys_dev_id_map_tab
                    WHERE area_name in (
                    SELECT
                    city_name
                    FROM sys_city_code_tab
                    WHERE area_name = "{area}"
                    )
                    AND field_key = "load"
                    )
                    AND sload.data_year = {year}
                    ORDER BY sload.day_time ASC
                """
                load_resp = db.execute(text(load_sql)).fetchall()
                load_df = pd.DataFrame(load_resp).fillna(0)
                load_df = process_sql_resp(
                    load_df, is_filter=True, is_unit=is_unit, val_name="load", is_day_time=True
                )
                # wind 风电
                wind_sql = f"""
                SELECT
                *
                FROM
                sys_power_data_tab spower
                WHERE
                spower.area_name in (
                SELECT
                city_name
                FROM sys_city_code_tab
                WHERE area_name = "{area}"
                )
                AND spower.dev_id in (
                SELECT
                dev_id
                FROM sys_dev_id_map_tab
                WHERE area_name in (
                SELECT
                city_name
                FROM sys_city_code_tab
                WHERE area_name = "{area}"
                )
                AND field_key = "wind"
                )
                AND spower.data_year = {year}
                ORDER BY spower.day_time ASC
                """
                wind_resp = db.execute(text(wind_sql)).fetchall()
                wind_df = pd.DataFrame(wind_resp).fillna(0)
                wind_df = process_sql_resp(
                    wind_df, is_filter=True, is_unit=is_unit, val_name="wind", is_day_time=True
                )
                # solar 光伏
                solar_sql = f"""
                SELECT
                *
                FROM
                sys_power_data_tab spower
                WHERE
                spower.area_name in (
                SELECT
                city_name
                FROM sys_city_code_tab
                WHERE area_name = "{area}"
                )
                AND spower.dev_id in (
                SELECT
                dev_id
                FROM sys_dev_id_map_tab
                WHERE area_name in (
                SELECT
                city_name
                FROM sys_city_code_tab
                WHERE area_name = "{area}"
                )
                AND field_key = "solar"
                )
                AND spower.data_year = {year}
                ORDER BY spower.day_time ASC
                """
                solar_resp = db.execute(text(solar_sql)).fetchall()
                solar_df = pd.DataFrame(solar_resp).fillna(0)
                solar_df = process_sql_resp(
                    solar_df, is_filter=True, is_unit=is_unit, val_name="solar", is_day_time=True
                )
                # reserve_up 上旋备
                reserve_up_sql = f"""
                SELECT
                *
                FROM
                sys_power_data_tab spower
                WHERE
                spower.area_name in (
                SELECT
                city_name
                FROM sys_city_code_tab
                WHERE area_name = "{area}"
                )
                AND spower.dev_id in (
                SELECT
                dev_id
                FROM sys_dev_id_map_tab
                WHERE area_name in (
                SELECT
                city_name
                FROM sys_city_code_tab
                WHERE area_name = "{area}"
                )
                AND field_key = "reserve_up"
                )
                AND spower.data_year = {year}
                ORDER BY spower.day_time ASC
                """
                reserve_up_resp = db.execute(text(reserve_up_sql)).fetchall()
                reserve_up_df = pd.DataFrame(reserve_up_resp).fillna(0)
                reserve_up_df = process_sql_resp(
                    reserve_up_df, is_filter=True, is_unit=is_unit, val_name="reserve_up", is_day_time=True
                )
                # reserve_down 下旋备
                reserve_down_sql = f"""
                SELECT
                *
                FROM
                sys_power_data_tab spower
                WHERE
                spower.area_name in (
                SELECT
                city_name
                FROM sys_city_code_tab
                WHERE area_name = "{area}"
                )
                AND spower.dev_id in (
                SELECT
                dev_id
                FROM sys_dev_id_map_tab
                WHERE area_name in (
                SELECT
                city_name
                FROM sys_city_code_tab
                WHERE area_name = "{area}"
                )
                AND field_key = "reserve_down"
                )
                AND spower.data_year = {year}
                ORDER BY spower.day_time ASC
                """
                reserve_down_resp = db.execute(text(reserve_down_sql)).fetchall()
                reserve_down_df = pd.DataFrame(reserve_down_resp).fillna(0)
                reserve_down_df = process_sql_resp(
                    reserve_down_df, is_filter=True, is_unit=is_unit, val_name="reserve_down", is_day_time=True
                )
                # feedin 区外来电
                feedin_sql = f"""
                SELECT
                *
                FROM
                sys_power_data_tab spower
                WHERE
                spower.area_name in (
                SELECT
                city_name
                FROM sys_city_code_tab
                WHERE area_name = "{area}"
                )
                AND spower.dev_id in (
                SELECT
                dev_id
                FROM sys_dev_id_map_tab
                WHERE area_name in (
                SELECT
                city_name
                FROM sys_city_code_tab
                WHERE area_name = "{area}"
                )
                AND field_key = "feedin"
                )
                AND spower.data_year = {year}
                ORDER BY spower.day_time ASC
                """
                feedin_resp = db.execute(text(feedin_sql)).fetchall()
                feedin_df = pd.DataFrame(feedin_resp).fillna(0)
                feedin_df = process_sql_resp(
                    feedin_df, is_filter=True, is_unit=is_unit, val_name="feedin", is_day_time=True
                )
                # wind_curtailment 弃风
                wind_curtailment_sql = f"""
                SELECT
                *
                FROM
                sys_power_data_tab spower
                WHERE
                spower.area_name in (
                SELECT
                city_name
                FROM sys_city_code_tab
                WHERE area_name = "{area}"
                )
                AND spower.dev_id in (
                SELECT
                dev_id
                FROM sys_dev_id_map_tab
                WHERE area_name in (
                SELECT
                city_name
                FROM sys_city_code_tab
                WHERE area_name = "{area}"
                )
                AND field_key = "wind_curtailment"
                )
                AND spower.data_year = {year}
                ORDER BY spower.day_time ASC
                """
                wind_curtailment_resp = db.execute(text(wind_curtailment_sql)).fetchall()
                wind_curtailment_df = pd.DataFrame(wind_curtailment_resp).fillna(0)
                wind_curtailment_df = process_sql_resp(
                    wind_curtailment_df,
                    is_filter=True,
                    is_unit=is_unit,
                    val_name="wind_curtailment",
                    is_day_time=True
                )
                # solar_curtailment 弃光
                solar_curtailment_sql = f"""
                SELECT
                *
                FROM
                sys_power_data_tab spower
                WHERE
                spower.area_name in (
                SELECT
                city_name
                FROM sys_city_code_tab
                WHERE area_name = "{area}"
                )
                AND spower.dev_id in (
                SELECT
                dev_id
                FROM sys_dev_id_map_tab
                WHERE area_name in (
                SELECT
                city_name
                FROM sys_city_code_tab
                WHERE area_name = "{area}"
                )
                AND field_key = "solar_curtailment"
                )
                AND spower.data_year = {year}
                ORDER BY spower.day_time ASC
                """
                solar_curtailment_resp = db.execute(text(solar_curtailment_sql)).fetchall()
                solar_curtailment_df = pd.DataFrame(solar_curtailment_resp).fillna(0)
                solar_curtailment_df = process_sql_resp(
                    solar_curtailment_df,
                    is_filter=True,
                    is_unit=is_unit,
                    val_name="solar_curtailment",
                    is_day_time=True
                )
            else:
                for field_key in field_keys:
                    # 查询测点id
                    dev_id_sql = f"""
                    SELECT
                    dev_id
                    FROM sys_dev_id_map_tab
                    WHERE area_name = "{area}"
                    AND field_key = "{field_key}"
                    """
                    dev_resp = db.execute(text(dev_id_sql)).fetchone()
                    if dev_resp:
                        dev_id = dev_resp[0]
                    else:
                        continue
                    base_dict[field_key] = dev_id
                # load， 负荷
                load_dev_id = base_dict.get("load")
                if load_dev_id:
                    load_sql = f"""
                    SELECT
                    *
                    FROM
                    sys_load_data_tab
                    WHERE
                    area_name = '{area}'
                    AND dev_id = "{load_dev_id}"
                    AND data_year = {year}
                    ORDER BY day_time ASC
                    """
                    load_resp = db.execute(text(load_sql)).fetchall()
                    load_df = pd.DataFrame(load_resp).fillna(0)
                    load_df = process_sql_resp(load_df, is_filter=True, is_unit=is_unit, val_name="load")
                else:
                    load_df = pd.DataFrame(columns=["load", "时间"])
                # wind，风电
                wind_dev_id = base_dict.get("wind")
                if wind_dev_id:
                    wind_sql = f"""
                    SELECT
                    *
                    FROM
                    sys_power_data_tab
                    WHERE
                    area_name = '{area}'
                    AND dev_id = "{wind_dev_id}"
                    AND data_year = {year}
                    ORDER BY day_time ASC;
                    """
                    wind_resp = db.execute(text(wind_sql)).fetchall()
                    wind_df = pd.DataFrame(wind_resp).fillna(0)
                    wind_df = process_sql_resp(wind_df, is_filter=True, is_unit=is_unit, val_name="wind")
                else:
                    wind_df = pd.DataFrame(columns=["wind", "时间"])
                # solar，光电
                solar_dev_id = base_dict.get("solar")
                if solar_dev_id:
                    solar_sql = f"""
                    SELECT
                    *
                    FROM
                    sys_power_data_tab
                    WHERE
                    area_name = '{area}'
                    AND dev_id = "{solar_dev_id}"
                    AND data_year = {year}
                    ORDER BY day_time ASC;
                    """
                    solar_resp = db.execute(text(solar_sql)).fetchall()
                    solar_df = pd.DataFrame(solar_resp).fillna(0)
                    solar_df = process_sql_resp(solar_df, is_filter=True, is_unit=is_unit, val_name="solar")
                else:
                    solar_df = pd.DataFrame(columns=["solar", "时间"])
                # reserve_up， 上旋备
                reserve_up_dev_id = base_dict.get("reserve_up")
                if reserve_up_dev_id:
                    reserve_up_sql = f"""
                    SELECT
                    *
                    FROM
                    sys_power_data_tab
                    WHERE
                    area_name = '{area}'
                    AND dev_id = "{reserve_up_dev_id}"
                    AND data_year = {year}
                    ORDER BY day_time ASC;
                    """
                    reserve_up_resp = db.execute(text(reserve_up_sql)).fetchall()
                    reserve_up_df = pd.DataFrame(reserve_up_resp).fillna(0)
                    reserve_up_df = process_sql_resp(
                        reserve_up_df, is_filter=True, is_unit=is_unit, val_name="reserve_up"
                    )
                else:
                    reserve_up_df = pd.DataFrame(columns=["reserve_up", "时间"])
                # reserve_down，下旋备
                reserve_down_dev_id = base_dict.get("reserve_down")
                if reserve_down_dev_id:
                    reserve_down_sql = f"""
                    SELECT
                    *
                    FROM
                    sys_power_data_tab
                    WHERE
                    area_name = '{area}'
                    AND dev_id = "{reserve_down_dev_id}"
                    AND data_year = {year}
                    ORDER BY day_time ASC;
                    """
                    reserve_down_resp = db.execute(text(reserve_down_sql)).fetchall()
                    reserve_down_df = pd.DataFrame(reserve_down_resp).fillna(0)
                    reserve_down_df = process_sql_resp(
                        reserve_down_df, is_filter=True, is_unit=is_unit, val_name="reserve_down"
                    )
                else:
                    reserve_down_df = pd.DataFrame(columns=["reserve_down", "时间"])
                # feedin，区外来电
                feedin_dev_id = base_dict.get("feedin")
                if feedin_dev_id:
                    feedin_sql = f"""
                    SELECT
                    *
                    FROM sys_feedin_data_tab
                    WHERE display_desc = "受入总加"
                    AND area_name = "{area}"
                    AND dev_id = "{feedin_dev_id}"
                    AND data_year = {year}
                    ORDER BY day_time ASC
                    """
                    feedin_resp = db.execute(text(feedin_sql)).fetchall()
                    feedin_df = pd.DataFrame(feedin_resp).fillna(0)
                    feedin_df = process_sql_resp(feedin_df, is_filter=True, val_name="feedin")
                else:
                    feedin_df = pd.DataFrame(columns=["feedin", "时间"])

                # wind_curtailment, 弃风
                wind_curtailment_dev_id = base_dict.get("wind_curtailment")
                if wind_curtailment_dev_id:
                    wind_curtailment_sql = f"""
                    SELECT
                    *
                    FROM
                    sys_power_data_tab
                    WHERE
                    area_name = '{area}'
                    AND dev_id = "{wind_curtailment_dev_id}"
                    AND data_year = {year}
                    ORDER BY day_time ASC;
                    """
                    wind_curtailment_resp = db.execute(text(wind_curtailment_sql)).fetchall()
                    wind_curtailment_df = pd.DataFrame(wind_curtailment_resp).fillna(0)
                    wind_curtailment_df = process_sql_resp(
                        wind_curtailment_df, is_filter=True, val_name="wind_curtailment"
                    )
                else:
                    wind_curtailment_df = pd.DataFrame(columns=["wind_curtailment", "时间"])

                # solar_curtailment, 弃光
                solar_curtailment_dev_id = base_dict.get("solar_curtailment")
                if solar_curtailment_dev_id:
                    solar_curtailment_sql = f"""
                    SELECT
                    *
                    FROM
                    sys_power_data_tab
                    WHERE
                    area_name = '{area}'
                    AND dev_id = "{solar_curtailment_dev_id}"
                    AND data_year = {year}
                    ORDER BY day_time ASC;
                    """
                    solar_curtailment_resp = db.execute(text(solar_curtailment_sql)).fetchall()
                    solar_curtailment_df = pd.DataFrame(solar_curtailment_resp).fillna(0)
                    solar_curtailment_df = process_sql_resp(
                        solar_curtailment_df, is_filter=True, val_name="solar_curtailment"
                    )
                else:
                    solar_curtailment_df = pd.DataFrame(columns=["solar_curtailment", "时间"])
            # 以下三个数据是来源与其他数据的整合
            # newenergy, 新能源
            if isinstance(wind_df, pd.DataFrame) and isinstance(
                    solar_df, pd.DataFrame) and not wind_df.empty and not solar_df.empty:
                newenergy_df = pd.merge(wind_df, solar_df, on='时间')
                newenergy_df['newenergy'] = newenergy_df['wind'] + newenergy_df['solar']
                newenergy_df.drop(['wind', 'solar'], axis=1, inplace=True)
            else:
                newenergy_df = pd.DataFrame(columns=["newenergy", "时间"])

            # feedin_new, 受入电力+新能源
            if isinstance(newenergy_df, pd.DataFrame) and isinstance(
                    feedin_df, pd.DataFrame) and not newenergy_df.empty and not feedin_df.empty:
                feedin_new_df = pd.merge(feedin_df, newenergy_df, on='时间')
                feedin_new_df['feedin_new'] = feedin_new_df['feedin'] + feedin_new_df['newenergy']
                feedin_new_df.drop(['feedin', 'newenergy'], axis=1, inplace=True)
            else:
                feedin_new_df = pd.DataFrame(columns=["feedin_new", "时间"])
            # new_curtailment, 新能源弃电
            if isinstance(wind_curtailment_df, pd.DataFrame) and isinstance(
                    solar_curtailment_df,
                    pd.DataFrame) and not wind_curtailment_df.empty and not solar_curtailment_df.empty:
                new_curtailment_df = pd.merge(wind_curtailment_df, solar_curtailment_df, on='时间')
                new_curtailment_df['new_curtailment'] = new_curtailment_df[
                    'wind_curtailment'] + new_curtailment_df['solar_curtailment']
                new_curtailment_df.drop(['wind_curtailment', 'solar_curtailment'], axis=1, inplace=True)
            else:
                new_curtailment_df = pd.DataFrame(columns=["new_curtailment", "时间"])

            merge_df = safe_merge(load_df, wind_df)
            if isinstance(merge_df, pd.DataFrame):
                merge_df = safe_merge(merge_df, solar_df)
            if isinstance(merge_df, pd.DataFrame):
                merge_df = safe_merge(merge_df, reserve_up_df)
            if isinstance(merge_df, pd.DataFrame):
                merge_df = safe_merge(merge_df, reserve_down_df)
            if isinstance(merge_df, pd.DataFrame):
                merge_df = safe_merge(merge_df, feedin_df)
            if isinstance(merge_df, pd.DataFrame):
                merge_df = safe_merge(merge_df, wind_curtailment_df)
            if isinstance(merge_df, pd.DataFrame):
                merge_df = safe_merge(merge_df, solar_curtailment_df)
            if isinstance(merge_df, pd.DataFrame):
                merge_df = safe_merge(merge_df, newenergy_df)
            if isinstance(merge_df, pd.DataFrame):
                merge_df = safe_merge(merge_df, feedin_new_df)
            if isinstance(merge_df, pd.DataFrame):
                merge_df = safe_merge(merge_df, new_curtailment_df)
            if not isinstance(merge_df, pd.DataFrame):
                logger.info("merger df not dataframe, 生成场景失败")
                return
            merge_df = merge_df.fillna(0)
            resp = hisdata_utils.get_scenario_list(data_df=merge_df, area_name=area)
            # 保存场景信息
            save_scenes(data=resp)
        logger.info("场景数据加载完成")
    except Exception as e:
        logger.exception(e)
        raise
    finally:
        db.close()


def save_scenes(data: Dict):
    """
    保存典型场景信息
    """
    db = SessionLocal()
    try:
        nowtime = str(dt.datetime.now())
        for name, values in data.items():
            for value in values:
                scenes_id = str(uuid.uuid4())
                date_time = f"{value['day_time']} {value['time_']}:00"
                # 检查是否存在重复的典型数据，若是存在，则删除
                query_sql = F"""
                SELECT
                id
                FROM sys_scenes
                WHERE `name` = "{name}"
                AND area_name = "{value['area_name']}"
                AND `year` = {value['year']}
                """
                query_resp = db.execute(text(query_sql)).fetchall()
                for obj in query_resp:
                    del_sql = f"""
                    DELETE FROM sys_scenes WHERE id = "{obj[0]}"
                    """
                    db.execute(text(del_sql))
                insert_sql = f"""
                INSERT INTO sys_scenes(id, `name`, area_name, `year`, date_time, create_time, update_time)  
                VALUES('{scenes_id}','{name}', '{value["area_name"]}', '{value["year"]}','{date_time}', '{nowtime}', '{nowtime}')
                """
                db.execute(text(insert_sql))
        db.commit()
    except Exception as e:
        logger.exception(e)
        db.rollback()
    finally:
        db.close()


def get_five_years_ago():
    """
    获取往前5年前的时间点

    Returns:
        tuple: 包含当前时间和5年前时间的元组，格式为 (当前时间对象, 5年前时间对象)
    """
    current_time = dt.datetime.now()
    five_years_ago = current_time.replace(year=current_time.year - 5)
    return five_years_ago


def get_dev_id(area: str, field_key: str):
    """
    获取测点id
    """
    db = SessionLocal()
    dev_id = ""
    try:
        dev_sql = f"""
        SELECT
        dev_id
        FROM sys_dev_id_map_tab
        WHERE area_name = "{area}"
        AND field_key = "{field_key}"
        AND data_source = "sys"
        """
        if area == "全省" and field_key in common.display_desc_quansheng_map:
            display_desc = common.display_desc_quansheng_map[field_key]
            dev_sql += f"AND display_desc = '{display_desc}'"
        dev_resp = db.execute(text(dev_sql)).fetchone()
        if not dev_resp or len(dev_resp) == 0:
            # 在查询qs关联的测点id
            dev_sql = f"""
            SELECT
            dev_id
            FROM sys_dev_id_map_tab
            WHERE area_name = "{area}"
            AND field_key = "{field_key}"
            AND data_source = "qs"
            """
            if area == "全省" and field_key in common.display_desc_quansheng_map:
                display_desc = common.display_desc_quansheng_map[field_key]
                dev_sql += f"AND display_desc = '{display_desc}'"
            dev_resp = db.execute(text(dev_sql)).fetchone()
            if not dev_resp or len(dev_resp) == 0:
                return dev_id
            else:
                dev_id = dev_resp[0]
        else:
            dev_id = dev_resp[0]
        return dev_id
    except Exception as e:
        logger.exception(e)
    finally:
        db.close()


def get_citys(area: str):
    """
    查询大区包含的城市
    """
    db = SessionLocal()
    citys = []
    try:
        sql = f"""
        SELECT
        city_name
        FROM sys_city_code_tab
        WHERE area_name = "{area}"
        """
        city_resp = db.execute(text(sql)).fetchall()
        for obj in city_resp:
            citys.append(obj[0])
    except Exception as e:
        logger.exception(e)
    finally:
        db.close()
    return citys


def get_big_area_dev_id_or_citys(area: str, field_key: str, is_citys: bool = False) -> Tuple[str, List]:
    """
    查询大区测点id或者所包含的城市
    """
    db = SessionLocal()
    citys = []
    dev_id = ""
    try:
        dev_sql = f"""
        SELECT
        dev_id
        FROM sys_dev_id_map_tab
        WHERE area_name = "{area}"
        AND field_key = "{field_key}"
        AND data_source = "sys"
        """
        dev_resp = db.execute(text(dev_sql)).fetchone()
        if dev_resp and len(dev_resp) >= 1:
            dev_id = dev_resp[0]
            if not is_citys:
                return dev_id, citys
        sql = f"""
        SELECT
        city_name
        FROM sys_city_code_tab
        WHERE area_name = "{area}"
        """
        city_resp = db.execute(text(sql)).fetchall()
        for obj in city_resp:
            citys.append(obj[0])
    except Exception as e:
        logger.exception(e)
    finally:
        db.close()
    return dev_id, citys


def get_dc_curev_list():
    """
    查询直流曲线列表
    """
    db = SessionLocal()
    curve_list = []
    try:
        sql = f"""
        SELECT DISTINCT
        display_desc 
        FROM
        sys_dev_id_map_tab
        WHERE field_key = "feedin"
        ORDER BY display_desc DESC
        """
        sql_resp = db.execute(text(sql)).fetchall()
        for obj in sql_resp:
            curve_list.append(obj[0])
        curve_list = sorted(curve_list, key=lambda x: common.curve_order_dict.get(x, -1))
    except Exception as e:
        logger.exception(e)
    finally:
        db.close()
    return curve_list


def get_curve_dev_id(curve_name: str):
    """
    获取直流曲线测点id
    """
    db = SessionLocal()
    dev_id = ""
    try:
        dev_sql = f"""
        SELECT
        dev_id
        FROM sys_dev_id_map_tab
        WHERE display_desc = "{curve_name}"
        AND field_key = "feedin"
        AND data_source = "sys"
        """
        dev_resp = db.execute(text(dev_sql)).fetchone()
        if dev_resp and len(dev_resp) >= 1:
            dev_id = dev_resp[0]
        return dev_id
    except Exception as e:
        logger.exception(e)
    finally:
        db.close()


def get_capacity_area(area: str, field_key: str):
    """
    获取区域装机数据
    """
    db = SessionLocal()
    capacity = 0
    try:
        if area == "全省":
            select_str = f" '全省', SUM(capacity) "
            group_by = ""
            city_sql = f""
        elif area in ['豫西', '豫中东', '豫南', '豫北']:
            select_str = f" '{area}', SUM(capacity) "
            group_by = ""
            citys = get_citys(area=area)
            city_sql = f" WHERE city in {tuple(citys)}"
        else:
            select_str = f" city, SUM(capacity) "
            group_by = f" GROUP BY city"
            city_sql = f" WHERE city = '{area}'"
        if field_key == "gen_all":
            field_key_sql = ""
        else:
            if "WHERE" not in city_sql:
                field_key_sql = f" WHERE field_key = '{field_key}'"
            else:
                field_key_sql = f" AND field_key = '{field_key}'"
        if field_key in ['wind', 'solar', 'stogen_energy_storage']:
            sql = f"""
            SELECT
            {select_str}
            FROM sys_newstation_tab
            """
        else:
            sql = f"""
            SELECT
            {select_str}
            FROM sys_station_tab
            """
        sql += city_sql
        sql += field_key_sql
        if field_key not in ['wind', 'solar', 'stogen_energy_storage']:
            if "WHERE" not in sql:
                sql += f" WHERE station_type = 'factory'"
            else:
                sql += f" AND station_type = 'factory'"
        # 拼接分组
        sql += group_by
        sql_resp = db.execute(text(sql)).fetchone()
        if sql_resp and len(sql_resp) >= 1:
            capacity = sql_resp[1]
            if area == "全省" and not capacity:
                if field_key == "gen_all":
                    all_field_key_sql = ""
                else:
                    all_field_key_sql = f"WHERE field_key = '{field_key}'"
                if field_key in ['wind', 'solar', 'stogen_energy_storage']:
                    sql = f"""
                    SELECT
                    {select_str}
                    FROM sys_newstation_tab
                    """
                else:
                    sql = f"""
                    SELECT
                    {select_str}
                    FROM sys_station_tab
                    """
                sql += all_field_key_sql
                sql_resp = db.execute(text(sql)).fetchone()
                if sql_resp and len(sql_resp) >= 1:
                    capacity = sql_resp[1]
    except Exception as e:
        logger.exception(e)
    finally:
        db.close()
    return capacity


def md5_encrypt(value: str):
    # 创建一个 MD5 哈希对象
    md5_hash = hashlib.md5()
    # 将输入字符串编码为字节类型，因为 update 方法需要字节类型的参数
    input_bytes = value.encode('utf-8')
    # 使用 update 方法更新哈希对象的内容
    md5_hash.update(input_bytes)
    # 使用 hexdigest 方法获取十六进制表示的哈希值
    encrypted_string = md5_hash.hexdigest().upper()
    return encrypted_string
