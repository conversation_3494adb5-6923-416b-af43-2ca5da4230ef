# 统计不同电压等级及断面类型分组下各个断面数据的信息
import pandas as pd
import datetime as dt
from sqlalchemy import text
from utils.cache import get_data_with_cache
import tempfile
import os
import math

def calculate_section_statistics(df):
    """
    统计不同断面名称分组下各个断面数据的信息
    
    参数:
        df: 包含断面数据的DataFrame
        
    返回:
        按断面名称分组的统计结果字典
    """
    # 获取所有value_开头的列
    value_columns = [col for col in df.columns if col.startswith('value_')]
    
    # 结果字典
    result = {}
    
    # 按断面名称分组
    grouped = df.groupby(['inf_name'])
    
    for inf_name, group in grouped:
        # 获取该断面的基本信息（取第一行的信息）
        first_row = group.iloc[0]
        
        # 提取基本信息
        section_info = {
            "display_desc": first_row.get('display_desc', ''),
            "dev_id": first_row.get('dev_id', ''),
            "inf_name": inf_name,
            "inf_desc": first_row.get('inf_desc', ''),
            "inf_limit": float(first_row.get('inf_limit', 0)),
            "inf_type": first_row.get('inf_type', ''),
            "sub_type": first_row.get('sub_type', ''),
            "zone": first_row.get('zone', ''),
            "vlevel": float(first_row.get('vlevel', 0)) if pd.notna(first_row.get('vlevel')) else 0,
            "heavy_hours": 0,  # 重载时长（功率大于80%限额的点数）
            "overload_hours": 0,  # 越限时长（功率大于100%限额的点数）
            "max_power_value": 0,  # 将在后面计算最大功率时填充
            "max_power_rate": 0,  # 最大负载率
            "max_power_time": "",  # 将在后面计算最大负载率时刻时填充
            "heavy_days": 0,  # 重载天数
            "overload_days": 0,  # 越限天数
        }
        
        # 收集所有时间点的功率值
        all_power_values = []
        all_time_points = []
        heavy_days_set = set()  # 记录重载的天数
        overload_days_set = set()  # 记录越限的天数
        
        # 遍历每一天的数据
        for _, row in group.iterrows():
            day_time = row.get('day_time', '07-01')
            data_year = row.get('data_year', '2025')
            day_has_heavy = False  # 标记当天是否有重载
            day_has_overload = False  # 标记当天是否有越限
            
            # 获取当天所有时间点的功率值
            day_power_values = []
            day_time_points = []
            
            for col in value_columns:
                if col in row and pd.notna(row[col]):
                    # 解析时间点，例如 value_2330 -> 23:30
                    time_str = col[6:]  # 去掉"value_"前缀
                    hour = int(time_str[:2])
                    minute = int(time_str[2:])
                    
                    # 构建完整的时间字符串
                    full_time_str = f"{data_year}-{day_time} {hour:02d}:{minute:02d}:00"
                    
                    power_value = float(row[col])
                    day_power_values.append(power_value)
                    day_time_points.append((full_time_str, power_value))
            
            # 计算当天的负载率
            if day_power_values and section_info["inf_limit"] > 0:
                day_load_rates = [abs(power) / section_info["inf_limit"] for power in day_power_values]
                
                # 检查当天是否有重载或越限
                if any(rate > 0.8 for rate in day_load_rates):
                    day_has_heavy = True
                if any(rate > 1.0 for rate in day_load_rates):
                    day_has_overload = True
                
                # 记录重载和越限天数
                if day_has_heavy:
                    heavy_days_set.add(day_time)
                if day_has_overload:
                    overload_days_set.add(day_time)
                
                # 添加到总功率值和时间点列表
                all_power_values.extend(day_power_values)
                all_time_points.extend(day_time_points)
        
        # 计算综合统计信息
        if all_power_values and section_info["inf_limit"] > 0:
            # 计算所有时间点的负载率
            all_load_rates = [abs(power) / section_info["inf_limit"] for power in all_power_values]
            
            # 重载总时长（功率大于80%限额的点数）
            section_info["heavy_hours"] = math.ceil(sum(1 for rate in all_load_rates if rate > 0.8) / 12)
            
            # 越限总时长（功率大于100%限额的点数）
            section_info["overload_hours"] = math.ceil(sum(1 for rate in all_load_rates if rate > 1.0) / 12)
            
            # 重载天数
            section_info["heavy_days"] = len(heavy_days_set)
            
            # 越限天数
            section_info["overload_days"] = len(overload_days_set)
            
            # 最大功率和最大负载率
            max_load_rate_idx = all_load_rates.index(max(all_load_rates))
            section_info["max_power_value"] = all_power_values[max_load_rate_idx]
            section_info["max_power_rate"] = max(all_load_rates)
            
            # 最大负载率时刻
            section_info["max_power_time"] = all_time_points[max_load_rate_idx][0]
        
        # 将统计结果添加到结果字典中
        # 使用断面类型和电压等级作为分组键，保持与原接口兼容
        group_key = section_info['inf_type']

        if group_key in ["transformer_inf", "zone220_trafo"]:
            if group_key not in result:
                result[group_key] = {}
            # 对于变压器和220kV断面，按电压等级进一步分组
            if section_info["zone"] not in result[group_key]:
                result[group_key][section_info["zone"]] = []
            result[group_key][section_info["zone"]].append(section_info)
        else:
            if group_key not in result:
                result[group_key] = []
            result[group_key].append(section_info)
    return result

def get_sibling_nodes(data, node_name):
    """
    Args:
        data (dict): 包含节点信息的字典
        node_name (str): 要查找的节点名
        
    Returns:
        tuple: (parent_name, sibling_nodes)
               parent_name: 父节点名，如果找不到则为None
               sibling_nodes: 与指定节点处于同一父节点下的所有子节点名列表（不包括指定节点本身），如果找不到节点则为空列表
    """
    try:
        # 递归查找节点的父节点
        def find_parent_and_siblings(current_data, target_node, parent_path=None):
            if isinstance(current_data, dict):
                # 检查当前层级是否包含目标节点
                if target_node in current_data:
                    if (int(current_data[target_node]['inf_level']) >= 500) and (current_data[target_node]["sub_type"] == "line"):
                        print("当前目标节点为500kV以上线路，跳过")
                        return None, []
                    # 找到目标节点，返回其父节点和所有兄弟节点
                    if parent_path is not None:
                        parent_data = data
                        parent_name = parent_path[-1] if parent_path else None
                        for key in parent_path:
                            parent_data = parent_data[key]
                        return parent_name, list(parent_data.keys())
                    else:
                        print("目标节点是根节点下的直接子节点，没有父节点和兄弟节点")
                        return None, []
                
                # 递归查找子节点
                for key, value in current_data.items():
                    if isinstance(value, dict):
                        new_parent_path = parent_path.copy() if parent_path else []
                        new_parent_path.append(key)
                        result = find_parent_and_siblings(value, target_node, new_parent_path)
                        if result is not None:
                            return result
            
            return None
        
        # 从根节点开始查找
        result = find_parent_and_siblings(data, node_name, [])
        
        if result is not None:
            parent_name, siblings = result
            # 如果找到兄弟节点，排除目标节点本身
            if siblings and node_name in siblings:
                siblings.remove(node_name)
            return parent_name, siblings
        else:
            return None, []
    
    except Exception as e:
        print(f"寻找关联节点出错: {e}")
        return None, []
    

def inferface_analysis(df):
    # 获取所有value_开头的列
    if df.empty:
        return []
    value_columns = [col for col in df.columns if col.startswith('value_')]
    
    # 直接计算每个接口的统计信息
    interface_stats = {}
    heavy_days_dict = {}
    overload_days_dict = {}
    # 新增：存储每个接口的时间和负载率列表
    interface_time_data = {}
    all_interfaces = df['inf_name'].unique()
    for inf_name in all_interfaces:
        # 初始化该接口的统计数据
        interface_stats[inf_name] = {
            "inf_name": inf_name,
            "inf_desc": '',
            "inf_limit": 0,
            "max_power_rate": 0,
            "heavy_hours": 0,
            "overload_hours": 0,
        }
        heavy_days_dict[inf_name] = set()
        overload_days_dict[inf_name] = set()
        # 新增：初始化时间和负载率列表
        interface_time_data[inf_name] = {
            "time_list": [],
            "power_rate_list": []
        }
    
    # 先按照 data_year 和 day_time 对 DataFrame 进行排序
    df_sorted = df.sort_values(by=['data_year', 'day_time'])
    
    # 处理每一行数据
    for _, row in df_sorted.iterrows():
        inf_name = row.get('inf_name', '')
        day_time = row.get('day_time', '')
        data_year = row.get('data_year', '2025')
        inf_limit = float(row.get('inf_limit', 0))
        inf_desc = row.get('inf_desc', '')
        
        # 更新接口基本信息
        if inf_name in interface_stats:
            interface_stats[inf_name]["inf_desc"] = inf_desc
            interface_stats[inf_name]["inf_limit"] = inf_limit
        
        # 初始化重载和越限标志
        has_heavy = False
        has_overload = False
        
        # 处理所有时间点的功率值
        if inf_limit > 0:
            max_load_rate = 0
            
            for col in value_columns:
                if col in row and pd.notna(row[col]):
                    power_value = float(row[col])
                    load_rate = abs(power_value) / inf_limit
                    
                    # 更新最大负载率
                    if load_rate > max_load_rate:
                        max_load_rate = load_rate
                    
                    # 检查重载和越限
                    if load_rate > 1.0:
                        has_overload = True
                        interface_stats[inf_name]["overload_hours"] += 1
                    elif load_rate > 0.8:
                        has_heavy = True
                        interface_stats[inf_name]["heavy_hours"] += 1
                    
                    # 新增：收集时间和负载率数据
                    # 解析时间点，例如 value_2330 -> 23:30
                    time_str = col[6:]  # 去掉"value_"前缀
                    hour = int(time_str[:2])
                    minute = int(time_str[2:])
                    
                    # 构建完整的时间字符串
                    full_time_str = f"{data_year}-{day_time} {hour:02d}:{minute:02d}:00"
                    # 计算数据频率
                    
                    # 添加到时间和负载率列表（负载率保留3位小数）
                    interface_time_data[inf_name]["time_list"].append(full_time_str)
                    interface_time_data[inf_name]["power_rate_list"].append(round(load_rate, 3))
            
            # 更新最大负载率
            if max_load_rate > interface_stats[inf_name]["max_power_rate"]:
                interface_stats[inf_name]["max_power_rate"] = max_load_rate
        
        # 记录重载和越限天数
        if has_heavy:
            heavy_days_dict[inf_name].add(day_time)
        if has_overload:
            overload_days_dict[inf_name].add(day_time)
    
    # 构建最终结果列表
    result_list = []
    
    # 遍历所有接口，添加到结果列表
    for interface in all_interfaces:
        stats = interface_stats.get(interface, {
            "inf_name": interface,
            "inf_desc": '',
            "inf_limit": 0,
            "max_power_rate": 0,
            "heavy_hours": 0,
            "overload_hours": 0,
        })
        
        # 获取该接口的时间和负载数据
        time_data = interface_time_data.get(interface, {
            "time_list": [],
            "power_rate_list": []
        })
        
        result_item = {
            "inf_name": stats["inf_name"],  # 断面名
            "inf_desc": stats["inf_desc"],  # 描述
            "inf_limit": stats["inf_limit"],  # 限额
            "max_power_rate": stats["max_power_rate"],  # 最大负载率
            "heavy_hours": math.ceil(stats["heavy_hours"] / 12),  # 重载时长
            "overload_hours": math.ceil(stats["overload_hours"] / 12),  # 越限时长
            "heavy_days": len(heavy_days_dict.get(interface, set())),  # 重载天数
            "overload_days": len(overload_days_dict.get(interface, set())),  # 越限天数
            # 新增：添加时间和负载率列表
            "time_list": time_data["time_list"],
            "power_rate_list": time_data["power_rate_list"],
        }
        result_list.append(result_item)
    
    return result_list


def get_specified_load_rate(df: pd.DataFrame, _time_name: str) -> dict:
    """
    获取指定时间点的负载率
    Args:
        df (pd.DataFrame): 数据框
        _time_name (str): 时间点名称
    
    Returns:
        dict: 指定时间点的负载率
    """
    res = {
        "line_inf": [],
        "transformer_inf": [],
        "zone220_inf": [],
        "zone220_trafo": []
    }
    for _, row in df.iterrows():
        inf_type = row["inf_type"]
        if inf_type is None:
            continue
        load_rate = abs(row[f"value_{_time_name}"]) / row["inf_limit"]
        res[inf_type].append({
            "inf_name": row.get("inf_name", ""),  # 断面名
            "inf_desc": row["inf_desc"],  # 断面描述
            "vlevel": row["vlevel"],      # 电压等级
            "inf_limit": row["inf_limit"],  # 限额
            "power_value": row[f"value_{_time_name}"],  # 潮流
            "load_rate": load_rate,       # 负载率
        })
    return res


def get_interface_info(db_uri):
    """
    获取断面台账信息
    :param db_uri: 数据库连接字符串
    :return: 断面台账信息的DataFrame
    """
    sql = "SELECT inf_name, vlevel, inf_desc, id, inf_limit, inf_type FROM sys_qs_interface_tab"
    sql_qs_inf = get_data_with_cache(sql, db_uri)
    qs_inf_df = pd.DataFrame(sql_qs_inf)
    return qs_inf_df


def get_interface_history_data(start_time, end_time, qs_inf_df, db_uri):
    """
    获取断面历史数据
    :param start_time: 开始时间
    :param end_time: 结束时间
    :param qs_inf_df: 断面台账信息DataFrame
    :param db_uri: 数据库连接字符串
    :return: 历史数据的DataFrame
    """
    now_time = dt.datetime.now()
    all_power_data_df = pd.DataFrame()
    
    # 查询历史数据
    history_end_date = min(end_time.date(), (now_time - dt.timedelta(days=1)).date())
    if start_time.date() <= history_end_date:
        history_start_time = start_time
        history_end_time = dt.datetime.combine(history_end_date, dt.time.max)

        date_range = pd.date_range(start=history_start_time.date(), end=history_end_date)

        query_groups = {}
        for d in date_range:
            if d.year not in query_groups:
                query_groups[d.year] = []
            query_groups[d.year].append(f"{d.month:02d}-{d.day:02d}")

        history_dfs = []
        for year, days in query_groups.items():
            days_str = "','".join(days)
            value_columns = [f"value_{h:02d}{m:02d}" for h in range(24) for m in range(0, 60, 5)]
            inf_data_sql = f"""
            SELECT dev_id, data_year, day_time, {", ".join(value_columns)}
            FROM sys_qs_inf_data_tab dt
            LEFT JOIN sys_qs_interface_tab ift ON ift.id = dt.dev_id
            WHERE dt.field_key="inf_power" AND dt.data_year = {year} AND dt.day_time IN ('{days_str}')
            AND dt.dev_id IN {tuple(qs_inf_df['id'].tolist())}
            """

            sql_data_inf = get_data_with_cache(inf_data_sql, db_uri)
            if sql_data_inf:
                df = pd.DataFrame(sql_data_inf)
                id_vars = [c for c in df.columns if not c.startswith('value_')]
                long_df = pd.melt(df, id_vars=id_vars, value_vars=value_columns, var_name='time_col',
                                  value_name='power')

                long_df['time_str'] = long_df['data_year'].astype(str) + '-' + long_df['day_time'] + ' ' + long_df[
                    'time_col'].str.slice(6, 8) + ':' + long_df['time_col'].str.slice(8, 10)
                long_df['time'] = pd.to_datetime(long_df['time_str'], format='%Y-%m-%d %H:%M')
                history_dfs.append(long_df)

        if history_dfs:
            all_power_data_df = pd.concat(history_dfs)
            all_power_data_df = all_power_data_df[['dev_id', 'power', 'time']]
    
    return all_power_data_df


def get_interface_realtime_data(start_time, end_time, qs_inf_df, inf_type=None, db=None):
    """
    获取断面实时数据
    :param start_time: 开始时间
    :param end_time: 结束时间
    :param qs_inf_df: 断面台账信息DataFrame
    :param inf_type: 断面类型筛选
    :param db: 数据库连接
    :return: 实时数据的DataFrame
    """
    now_time = dt.datetime.now()
    all_power_data_df = pd.DataFrame()
    
    # 查询实时数据
    if end_time >= now_time.replace(hour=0, minute=0, second=0, microsecond=0):
        real_time_start = max(start_time, now_time.replace(hour=0, minute=0, second=0, microsecond=0))

        inf_data_sql = f"""
        SELECT dit.dev_id, ddt.`value` as power, ddt.time
        FROM sys_real_day_data_tab ddt
        LEFT JOIN sys_real_day_index_tab dit ON dit.id = ddt.power_id
        LEFT JOIN sys_qs_interface_tab ift ON ift.id = dit.dev_id
        WHERE ddt.time >= '{real_time_start}' AND ddt.time <= '{end_time}'
        AND dit.field_key = 'inf_power'
        AND dit.dev_id IN {tuple(qs_inf_df['id'].tolist())}
        """
        if inf_type:
            inf_data_sql += f' AND ift.inf_type = "{inf_type}"'

        real_time_data = db.execute(text(inf_data_sql)).fetchall()
        if real_time_data:
            real_time_df = pd.DataFrame(real_time_data)
            all_power_data_df = real_time_df
    
    return all_power_data_df


def process_interface_data(qs_inf_df, all_power_data_df, start_time, end_time):
    """
    处理断面数据，合并台账信息和功率数据
    :param qs_inf_df: 断面台账信息DataFrame
    :param all_power_data_df: 功率数据DataFrame
    :param start_time: 开始时间
    :param end_time: 结束时间
    :return: 处理后的DataFrame
    """
    if all_power_data_df.empty:
        return pd.DataFrame()
    
    # 过滤最终时间
    all_power_data_df = all_power_data_df[
        (all_power_data_df['time'] >= start_time) & (all_power_data_df['time'] <= end_time)]

    qs_inf_data_df = pd.merge(qs_inf_df, all_power_data_df, left_on="id", right_on="dev_id", how="inner")
    qs_inf_data_df.drop(columns=['dev_id'], inplace=True)
    qs_inf_data_df['power'] = qs_inf_data_df['power'].fillna(value=0)
    qs_inf_data_df['inf_limit'] = qs_inf_data_df['inf_limit'].clip(lower=0.1)
    qs_inf_data_df['rate'] = (abs(qs_inf_data_df['power'] / qs_inf_data_df['inf_limit'])).round(4)

    qs_inf_data_df = qs_inf_data_df.rename(columns={'power': 'value'})
    qs_inf_data_df['time'] = qs_inf_data_df['time'].dt.strftime('%Y-%m-%d %H:%M:%S')
    
    return qs_inf_data_df


def convert_realtime_to_history_format(realtime_df):
    """
    将实时数据转换为与历史数据相同的格式

    参数:
        realtime_df (pd.DataFrame): 实时数据DataFrame，包含以下列:
            - id: 记录ID
            - dev_id: 设备ID
            - inf_name: 断面名称
            - inf_desc: 断面描述
            - inf_limit: 断面限额
            - inf_type: 断面类型
            - vlevel: 电压等级
            - zone: 区域
            - time: 时间戳
            - value_raw: 原始功率值
            - data_date: 数据日期 (YYYY-MM-DD)
            - day_time: 日时间 (MM-DD)
            - data_year: 数据年份
            - time_point: 时间点 (HHMM)

    返回:
        pd.DataFrame: 转换后的DataFrame，格式与历史数据表(sys_qs_inf_data_tab)相同
    """
    import uuid

    if realtime_df.empty:
        # 如果没有数据，创建空的DataFrame，但包含所有必要的列
        all_time_points = [f"{h:02d}{m:02d}" for h in range(24) for m in range(0, 60, 5)]
        return pd.DataFrame(columns=['id', 'dev_id', 'area_name', 'field_key', 'display_desc',
                                     'data_year', 'day_time', 'inf_name', 'inf_desc',
                                     'inf_limit', 'inf_type', 'vlevel', 'zone'] +
                                    [f'value_{time_point}' for time_point in all_time_points])

    # 将实时数据转换为与历史数据相同的格式
    converted_data = []

    # 按接口分组
    for inf_name, group in realtime_df.groupby('inf_name'):
        # 获取接口的基本信息
        inf_info = group.iloc[0]
        row_data = {
            'id': str(uuid.uuid4()),  # 生成新的ID
            'dev_id': inf_info['dev_id'],
            'area_name': '',
            'field_key': 'inf_power',
            'display_desc': inf_info['inf_desc'],
            'data_year': inf_info['data_year'],
            'day_time': inf_info['day_time'],
            'inf_name': inf_info['inf_name'],
            'inf_desc': inf_info['inf_desc'],
            'inf_limit': inf_info['inf_limit'],
            'inf_type': inf_info['inf_type'],
            'vlevel': inf_info['vlevel'],
            'zone': inf_info['zone']
        }

        # 为每个时间点创建value_列
        for _, row in group.sort_values('time_point').iterrows():
            time_point = row['time_point']
            value_col = f'value_{time_point}'
            row_data[value_col] = row['value_raw']

        converted_data.append(row_data)

    # 创建DataFrame
    converted_df = pd.DataFrame(converted_data)

    # 确保所有可能的value_列都存在，如果不存在则设为NaN
    all_time_points = [f"{h:02d}{m:02d}" for h in range(24) for m in range(0, 60, 5)]
    for time_point in all_time_points:
        value_col = f'value_{time_point}'
        if value_col not in converted_df.columns:
            converted_df[value_col] = None

    return converted_df


def get_interface_tide_data(start_time, end_time, inf_type=None, db_uri=None, db=None):
    """
    获取断面历史时刻功率数据
    :param start_time: 开始时间
    :param end_time: 结束时间
    :param inf_type: 断面类型筛选
    :param db_uri: 数据库连接字符串
    :param db: 数据库连接
    :return: 处理后的DataFrame
    """
    # 1. 获取断面台账信息
    qs_inf_df = get_interface_info(db_uri)
    if qs_inf_df.empty:
        return pd.DataFrame()

    all_power_data_df = pd.DataFrame()

    # 2. 查询历史数据
    history_data_df = get_interface_history_data(start_time, end_time, qs_inf_df, db_uri)
    if not history_data_df.empty:
        all_power_data_df = history_data_df

    # 3. 查询实时数据
    if db is not None:
        realtime_data_df = get_interface_realtime_data(start_time, end_time, qs_inf_df, inf_type, db)
        if not realtime_data_df.empty:
            if all_power_data_df.empty:
                all_power_data_df = realtime_data_df
            else:
                all_power_data_df = pd.concat([all_power_data_df, realtime_data_df])

    # 4. 合并和处理数据
    if not all_power_data_df.empty:
        qs_inf_data_df = process_interface_data(qs_inf_df, all_power_data_df, start_time, end_time)
        
        # 如果指定了断面类型，则进行筛选
        if inf_type is not None:
            qs_inf_data_df = qs_inf_data_df[qs_inf_data_df['inf_type'] == inf_type]
            
        return qs_inf_data_df
    else:
        return pd.DataFrame()


def export_interface_data_to_excel(data_df, filename=None):
    """
    将断面数据导出为Excel文件
    :param data_df: 要导出的DataFrame
    :param filename: 文件名，如果为None则使用临时文件
    :return: Excel文件路径
    """
    if data_df.empty:
        return None
    
    # 如果没有提供文件名，创建临时文件
    if filename is None:
        temp_dir = tempfile.gettempdir()
        filename = os.path.join(temp_dir, f"interface_data_{dt.datetime.now().strftime('%Y%m%d%H%M%S')}.xlsx")
    
    # 确保目录存在
    os.makedirs(os.path.dirname(filename), exist_ok=True)
    data_df = data_df[["time", "inf_name", "id", "vlevel", "inf_desc", "inf_limit", "inf_type",	"value", "rate"]]
    
    # 导出到Excel
    data_df.to_excel(filename, index=False, engine='openpyxl')
    
    return filename


if __name__ == "__main__":
    get_interface_tide_data(
        start_time=dt.datetime(2025, 7, 29, 0, 0, 0),
        end_time=dt.datetime(2025, 7, 29, 23, 59, 59),
    )

