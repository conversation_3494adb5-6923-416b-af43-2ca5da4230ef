import functools
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker


@functools.lru_cache(maxsize=64)
def get_data_with_cache(sql: str, db_uri: str):
    """
    执行 SQL 查询并缓存结果
    """
    engine = create_engine(db_uri)
    Session = sessionmaker(bind=engine)
    db = Session()
    try:
        result = db.execute(text(sql)).fetchall()
        return result
    finally:
        db.close()



def cache_by_date_and_db_identity(maxsize=128):
    """
    一个自定义的缓存装饰器，用于缓存数据库查询函数的结果。

    这个装饰器解决了标准 `lru_cache` 无法处理不可哈希的数据库会话
    (sessionmaker) 对象的问题。它通过以下方式工作：
    1. 内部定义一个使用 `lru_cache` 的辅助函数 `_cached_func`。
    2. `_cached_func` 接受普通的可哈希参数 (start_date, end_date) 和
       一个代表数据库会话对象的唯一ID `db_id`。
    3. 原始的数据库会话对象 `db` 通过闭包传递给 `_cached_func`。
    4. 在调用时，装饰器将 `id(db)` 作为 `db_id` 传递给缓存函数，
       从而为每个不同的数据库会话实例维护独立的缓存。

    Args:
        maxsize (int, optional): lru_cache的最大缓存大小. 默认为 128.
    """
    def decorator(func):
        @functools.wraps(func)
        def wrapper(start_date: str, end_date: str, db: sessionmaker):
            
            @functools.lru_cache(maxsize=maxsize)
            def _cached_func(start_date_inner, end_date_inner, db_id):
                # db_id 仅用于缓存key的生成，实际的db对象从闭包中获取
                return func(start_date_inner, end_date_inner, db)

            # 使用 id(db) 作为可哈希的key的一部分
            return _cached_func(start_date, end_date, id(db))
        
        return wrapper
    return decorator
