import pandas as pd
from sqlalchemy.orm import sessionmaker
from sqlalchemy import text
import numpy as np
from functools import lru_cache


@lru_cache()
def get_trafo_history_max_rate(start_date, end_date: str, db: sessionmaker) -> dict:
    """
    从数据库中获取各个变压器在指定时间段内的最大有功负载率.

    Arguments:
        start_date (str): 起始日期, 格式 'YYYY-MM-DD'.
        end_date (str): 结束日期, 格式 'YYYY-MM-DD'.
        db (sessionmaker): 用于数据库交互的SQLAlchemy sessionmaker.

    Returns:
        dict: 一个字典, key为变压器ID (dev_id), value为最大有功负载率.
    """
    value_cols = [f'value_{h:02d}{m:02d}' for h in range(24) for m in range(0, 60, 5)]
    value_cols_str = ", ".join(value_cols)

    # 1. 优化SQL查询, 仅获取所需列, 并优化日期查询
    power_sql = text(f"""
    SELECT dev_id, power_node, {value_cols_str}
    FROM sys_qs_trafo_data_tab
    WHERE
        CAST(CONCAT(data_year, '-', day_time) AS DATE) BETWEEN '{start_date}' AND '{end_date}'
        AND field_key = 'trafo_power_p'
    """)
    try:
        with db.bind.connect() as connection:
            df_power = pd.read_sql(power_sql, connection)
    except Exception as e:
        print(f"Error fetching power data: {e}")
        return {}

    if df_power.empty:
        return {}

    # 2. 获取变压器容量数据
    capacity_sql = text("SELECT transformerid, I_S, K_S, J_S FROM sys_qs_trafo_tab")
    try:
        with db.bind.connect() as connection:
            df_capacity = pd.read_sql(capacity_sql, connection)
    except Exception as e:
        print(f"Error fetching capacity data: {e}")
        return {}

    if df_capacity.empty:
        return {}

    # 3. 数据处理
    # 确保所有value列都存在, 不存在的用NaN填充 (read_sql已处理大部分情况, 此处为保险)
    for col in value_cols:
        if col not in df_power.columns:
            df_power[col] = np.nan

    # 计算每日每个绕组的最大有功功率
    df_power['max_p'] = df_power[value_cols].max(axis=1)

    # 计算整个时间段内每个绕组的最大有功功率
    df_max_p = df_power.groupby(['dev_id', 'power_node'])['max_p'].max().reset_index()

    # 4. 合并容量数据并计算负载率
    df_final = pd.merge(df_max_p, df_capacity, left_on='dev_id', right_on='transformerid')

    # 使用向量化操作np.select替代apply, 提升性能
    conditions = [
        df_final['power_node'] == 'hv',
        df_final['power_node'] == 'mv',
        df_final['power_node'] == 'lv'
    ]
    choices = [
        df_final['I_S'],
        df_final['K_S'],
        df_final['J_S']
    ]
    df_final['rated_capacity'] = np.select(conditions, choices, default=np.nan)

    df_final.dropna(subset=['rated_capacity'], inplace=True)
    df_final = df_final[df_final['rated_capacity'] > 0]

    if df_final.empty:
        return {}

    df_final['rate'] = df_final['max_p'].abs() / df_final['rated_capacity']

    # 5. 最终结果: 每个变压器的最大负载率 (取其所有绕组中的最大值)
    result_df = df_final.groupby('dev_id')['rate'].max()
    res = result_df.to_dict()

    return res


@lru_cache()
def get_line_history_max_rate(start_date, end_date: str, db: sessionmaker) -> dict:
    """
    从数据库中获取各条线路在指定时间段内的最大有功负载率.

    Arguments:
        start_date (str): 起始日期, 格式 'YYYY-MM-DD'.
        end_date (str): 结束日期, 格式 'YYYY-MM-DD'.
        db (sessionmaker): 用于数据库交互的SQLAlchemy sessionmaker.

    Returns:
        dict: 一个字典, key为线路ID, value为最大有功负载率.
    """
    value_cols = [f'value_{h:02d}{m:02d}' for h in range(24) for m in range(0, 60, 5)]
    value_cols_str = ", ".join(value_cols)

    # 1. 优化SQL查询, 仅获取所需列, 并优化日期查询
    power_sql = text(f"""
    SELECT dev_id, {value_cols_str}
    FROM sys_qs_line_data_tab
    WHERE
        CAST(CONCAT(data_year, '-', day_time) AS DATE) BETWEEN '{start_date}' AND '{end_date}'
        AND field_key = 'line_power_p'
    """)
    try:
        with db.bind.connect() as connection:
            df_power = pd.read_sql(power_sql, connection)
    except Exception as e:
        print(f"Error fetching power data: {e}")
        return {}

    if df_power.empty:
        return {}

    # 2. 获取线路容量数据
    capacity_sql = text("SELECT AClineid, volt, Ih FROM sys_qs_line_tab")
    try:
        with db.bind.connect() as connection:
            df_capacity = pd.read_sql(capacity_sql, connection)
    except Exception as e:
        print(f"Error fetching capacity data: {e}")
        return {}

    if df_capacity.empty:
        return {}

    # 3. 数据处理
    # 确保所有value列都存在, 不存在的用NaN填充
    for col in value_cols:
        if col not in df_power.columns:
            df_power[col] = np.nan

    # 计算每日每个绕组的最大有功功率
    df_power['max_p'] = df_power[value_cols].max(axis=1)

    # 计算整个时间段内每个绕组的最大有功功率
    df_max_p = df_power.groupby('dev_id')['max_p'].max().reset_index()

    # 4. 合并容量数据并计算负载率
    df_final = pd.merge(df_max_p, df_capacity, left_on='dev_id', right_on='AClineid')
    
    # 计算额定容量 S = sqrt(3) * V * I (假设功率因数为1)
    df_final['rated_capacity'] = np.sqrt(3) * df_final['volt'] * df_final['Ih'] / 1000 # MW
    df_final.dropna(subset=['rated_capacity'], inplace=True)
    df_final = df_final[df_final['rated_capacity'] > 0]

    if df_final.empty:
        return {}

    df_final['rate'] = df_final['max_p'].abs() / df_final['rated_capacity']

    # 5. 最终结果: 每条线路的最大负载率
    result_df = df_final.set_index('dev_id')['rate']
    res = result_df.to_dict()

    return res


if __name__ == "__main__":
    import sys
    import os
    # 将项目根目录添加到模块搜索路径
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

    from database.connect import SessionLocal
    db_session = SessionLocal()
    try:
        print("--- Testing get_trafo_history_max_rate ---")
        trafo_rates = get_trafo_history_max_rate("2025-08-04", "2025-08-06", db_session)
        print(trafo_rates)
        
        print("\n--- Testing get_line_history_max_rate ---")
        line_rates = get_line_history_max_rate("2025-08-04", "2025-08-06", db_session)
        print(line_rates)
    finally:
        db_session.close()
