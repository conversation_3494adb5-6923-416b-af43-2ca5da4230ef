import datetime as dt
from typing import Optional

import pandas as pd
from loguru import logger
from sqlalchemy import text

import utils
from data import params
from database import SessionLocal
from utils import Response
from utils import process_sql_resp


def get_wind_solar(area: str, year: int, field_key: str, db):
    df = pd.DataFrame({"时间": [], "value": []})
    if area in ['豫西', '豫中东', '豫南', '豫北']:
        dev_id, citys = utils.get_big_area_dev_id_or_citys(area=area, field_key=field_key)
        if dev_id:
            sql = f"""
            SELECT
                * 
            FROM sys_power_data_tab AS spower
            WHERE
            spower.field_key = "{field_key}" 
            AND spower.dev_id = "{dev_id}"
            AND spower.data_year = {year}
            """
            sql_resp = db.execute(text(sql)).fetchall()
            df = pd.DataFrame(sql_resp)
            df = utils.process_sql_resp(df)
            if df is None or (isinstance(df, pd.DataFrame) and df.empty):
                # 查询不到测点id或者，查询结果为空
                # 使用centralized + distribute代替solar
                centralized_dev_id = utils.get_dev_id(area=area, field_key=f"{field_key}_centralized")
                if centralized_dev_id:
                    centralized_sql = f"""
                    SELECT
                        * 
                    FROM sys_power_data_tab AS spower
                    WHERE
                    spower.field_key = "{field_key}_centralized" 
                    AND spower.dev_id = "{centralized_dev_id}"
                    AND spower.data_year = {year}
                    """
                    centralized_resp = db.execute(text(centralized_sql)).fetchall()
                    centralized_df = pd.DataFrame(centralized_resp)
                    centralized_df = utils.process_sql_resp(centralized_df)
                else:
                    centralized_df = pd.DataFrame({"时间": [], "value": []})
                distribute_dev_id = utils.get_dev_id(area=area, field_key=f"{field_key}_distribute")
                if distribute_dev_id:
                    distribute_sql = f"""
                    SELECT
                        * 
                    FROM sys_power_data_tab AS spower
                    WHERE
                    spower.field_key = "{field_key}_distribute" 
                    AND spower.dev_id = "{distribute_dev_id}"
                    AND spower.data_year = {year}
                    """
                    distribute_resp = db.execute(text(distribute_sql)).fetchall()
                    distribute_df = pd.DataFrame(distribute_resp)
                    distribute_df = utils.process_sql_resp(distribute_df)
                else:
                    distribute_df = pd.DataFrame({"时间": [], "value": []})
                if not centralized_df.empty and not distribute_df.empty:
                    df = pd.concat([centralized_df, distribute_df], axis=0, ignore_index=True)
                    df = df.groupby("时间").sum().reset_index()
                elif not centralized_df.empty:
                    df = centralized_df
                elif not distribute_df.empty:
                    df = distribute_df
        else:
            base_df: Optional[pd.DataFrame] = None
            for city in citys:
                dev_id = utils.get_dev_id(area=city, field_key=field_key)
                if not dev_id:
                    continue
                sql = f"""
                SELECT
                    * 
                FROM sys_power_data_tab AS spower
                WHERE
                spower.field_key = "{field_key}" 
                AND spower.dev_id = "{dev_id}"
                AND spower.data_year = {year}
                """
                sql_resp = db.execute(text(sql)).fetchall()
                city_df = pd.DataFrame(sql_resp)
                if city_df.empty:
                    # 使用centralized + distribute代替solar
                    centralized_dev_id = utils.get_dev_id(area=area, field_key=f"{field_key}_centralized")
                    if centralized_dev_id:
                        centralized_sql = f"""
                        SELECT
                            * 
                        FROM sys_power_data_tab AS spower
                        WHERE
                        spower.field_key = "{field_key}_centralized" 
                        AND spower.dev_id = "{centralized_dev_id}"
                        AND spower.data_year = {year}
                        """
                        centralized_resp = db.execute(text(centralized_sql)).fetchall()
                        centralized_df = pd.DataFrame(centralized_resp)
                        centralized_df = utils.process_sql_resp(centralized_df)
                    else:
                        centralized_df = pd.DataFrame({"时间": [], "value": []})
                    distribute_dev_id = utils.get_dev_id(area=area, field_key=f"{field_key}_distribute")
                    if distribute_dev_id:
                        distribute_sql = f"""
                        SELECT
                            * 
                        FROM sys_power_data_tab AS spower
                        WHERE
                        spower.field_key = "{field_key}_distribute" 
                        AND spower.dev_id = "{distribute_dev_id}"
                        AND spower.data_year = {year}
                        """
                        distribute_resp = db.execute(text(distribute_sql)).fetchall()
                        distribute_df = pd.DataFrame(distribute_resp)
                        distribute_df = utils.process_sql_resp(distribute_df)
                    else:
                        distribute_df = pd.DataFrame({"时间": [], "value": []})
                    if not centralized_df.empty and not distribute_df.empty:
                        city_df = pd.concat([centralized_df, distribute_df], axis=0, ignore_index=True)
                        city_df = city_df.groupby("时间").sum().reset_index()
                    elif not centralized_df.empty:
                        city_df = centralized_df
                    elif not distribute_df.empty:
                        city_df = distribute_df
                else:
                    city_df = utils.process_sql_resp(city_df)
                if city_df.empty:
                    continue
                if base_df is None:
                    base_df = city_df
                else:
                    base_df = pd.concat([base_df, city_df], axis=0, ignore_index=True)
            if isinstance(base_df, pd.DataFrame) and not base_df.empty:
                df = base_df.groupby(['时间']).sum().reset_index()
    else:
        dev_id = utils.get_dev_id(area=area, field_key=field_key)
        if dev_id:
            sql = f"""
            SELECT
                * 
            FROM sys_power_data_tab AS spower
            WHERE
            spower.field_key = "{field_key}" 
            AND spower.dev_id = "{dev_id}"
            AND spower.data_year = {year}
            """
            sql_resp = db.execute(text(sql)).fetchall()
            df = pd.DataFrame(sql_resp)
            df = utils.process_sql_resp(df)
        if df is None or (isinstance(df, pd.DataFrame) and df.empty):
            # 查询不到测点id或者，solar查询结果为空
            # 使用solar_centralized + solar_distribute代替solar
            centralized_dev_id = utils.get_dev_id(area=area, field_key=f"{field_key}_centralized")
            if centralized_dev_id:
                centralized_sql = f"""
                SELECT
                    * 
                FROM sys_power_data_tab AS spower
                WHERE
                spower.field_key = "{field_key}_centralized" 
                AND spower.dev_id = "{centralized_dev_id}"
                AND spower.data_year = {year}
                """
                centralized_resp = db.execute(text(centralized_sql)).fetchall()
                centralized_df = pd.DataFrame(centralized_resp)
                centralized_df = utils.process_sql_resp(centralized_df)
            else:
                centralized_df = pd.DataFrame({"时间": [], "value": []})
            distribute_dev_id = utils.get_dev_id(area=area, field_key=f"{field_key}_distribute")
            if distribute_dev_id:
                distribute_sql = f"""
                SELECT
                    * 
                FROM sys_power_data_tab AS spower
                WHERE
                spower.field_key = "{field_key}_distribute" 
                AND spower.dev_id = "{distribute_dev_id}"
                AND spower.data_year = {year}
                """
                distribute_resp = db.execute(text(distribute_sql)).fetchall()
                distribute_df = pd.DataFrame(distribute_resp)
                distribute_df = utils.process_sql_resp(distribute_df)
            else:
                distribute_df = pd.DataFrame({"时间": [], "value": []})
            if not centralized_df.empty and not distribute_df.empty:
                df = pd.concat([centralized_df, distribute_df], axis=0, ignore_index=True)
                df = df.groupby("时间").sum().reset_index()
            elif not centralized_df.empty:
                df = centralized_df
            elif not distribute_df.empty:
                df = distribute_df
    return df


def get_new_energy_power(item: params.GetUsualOutputDistributeParams):
    db = SessionLocal()
    result = Response()
    try:
        # 新能源查询
        # 需要把风电和光伏的数据加在一起
        # 查询光伏数据
        solar_df = get_wind_solar(area=item.area, field_key="solar", year=item.year, db=db)
        wind_df = get_wind_solar(area=item.area, field_key="wind", year=item.year, db=db)
        if not solar_df.empty and not wind_df.empty:
            df = pd.concat([solar_df, wind_df], axis=0, ignore_index=True)
            df = df.groupby("时间").sum().reset_index()
        elif not solar_df.empty:
            df = solar_df
        elif not wind_df.empty:
            df = wind_df
        else:
            stime = f"{item.year}-01-01 00:00:00"
            etime = f"{item.year}-12-31 23:59:59"
            # 生成时间序列数据，频率为5分钟，示例：2024-01-01 00:00:00到2024-12-31 23:59:59
            date_rng = pd.date_range(start=stime, end=etime, freq='5T')
            # 创建一个包含两列的DataFrame，一列是时间列，另一列是value列初始化为0
            df = pd.DataFrame({'时间': date_rng, 'value': [0] * len(date_rng)})
        return df
    except Exception as e:
        logger.exception(e)
    finally:
        db.close()
    return result
