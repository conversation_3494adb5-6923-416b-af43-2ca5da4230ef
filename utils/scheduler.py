import datetime as dt
import os
import shutil
import tarfile
import threading
import time
import uuid
from concurrent.futures import ALL_COMPLETED
from concurrent.futures import wait
from typing import Dict
from typing import Optional

import pandas as pd
from apscheduler.schedulers.background import BackgroundScheduler
from loguru import logger
from sqlalchemy import text

import common
from database import SessionLocal
from database import redis_obj
from database.models import *
from qs.main_qs_ana import CRCaseSta
from qs.main_qs_ana import load_config_file
from utils import process_scene

from . import auto_analyze_case

sched = BackgroundScheduler()


def clear_directory(directory_path: str):
    try:
        # 如果目录不存在，则直接返回
        if not os.path.exists(directory_path):
            return

        # 遍历目录中的所有文件和子目录
        for item in os.listdir(directory_path):
            item_path = os.path.join(directory_path, item)
            # 如果是文件，则删除文件
            if os.path.isfile(item_path):
                os.remove(item_path)
            # 如果是子目录，则递归清空子目录
            elif os.path.isdir(item_path):
                shutil.rmtree(item_path)

        logger.info(f"目录{directory_path}已清空。")
    except Exception as e:
        logger.info(f"清空目录时出现错误：{e}")


@sched.scheduled_job('interval', hours=2)
def cron_job():
    auto_analyze_case()


def real_qs_line_trafo_interface_analysis(
    db: SessionLocal,
    day_time: str,
    closest_time: str,
    is_last: bool = True,
    g_real_case: Optional[CRCaseSta] = None
):
    """
    实时解析qs文件中的交流线路、主变、断面数据
    """
    if is_last:
        qs_device_dict = common.g_real_case.qs_device_dict
    else:
        qs_device_dict = g_real_case.qs_device_dict
    acline = qs_device_dict['ACline']
    acline = acline.drop(columns=['@', 'id'], errors='ignore')
    acline_str_column = ['AClineid', 'name', 'I_node', 'J_node']
    for acline_column in acline.columns:
        if acline_column in acline_str_column:
            continue
        acline[acline_column] = pd.to_numeric(acline[acline_column], errors='coerce')
    transformer = qs_device_dict['Transformer']
    transformer = transformer.drop(columns=['@', 'id', 'station_name', 'neutral_idx'], errors='ignore')
    transformer_str_column = ['transfoermer', 'name', 'I_node', 'K_node', 'J_node']
    for transfoermer_column in transformer.columns:
        if transfoermer_column in transformer_str_column:
            continue
        transformer[transfoermer_column] = pd.to_numeric(transformer[transfoermer_column], errors='coerce')
    # 1. 查询数据库中已存在的 AClineid
    acline_ids_resp = db.execute(text("SELECT AClineid FROM sys_qs_line_tab")).fetchall()
    acline_ids_set = set([obj[0] for obj in acline_ids_resp])

    # 2. 过滤 DataFrame，只保留不存在的 AClineid
    df_to_insert1 = acline[~acline['AClineid'].isin(acline_ids_set)]
    if not df_to_insert1.empty:
        df_to_insert1.to_sql(
            name="sys_qs_line_tab",  # MySQL 中要写入的表名（需提前创建表结构，或让 pandas 自动创建）
            con=db.connection(),  # 数据库连接引擎
            if_exists="append",  # 表存在时的行为：append（追加）、replace（替换表）、fail（报错）
            index=False,  # 不写入 DataFrame 的索引列（除非表有对应的索引字段）
        )
    # 1. 查询数据库中已存在的 transformerid
    transformer_ids_resp = db.execute(text("SELECT transformerid FROM sys_qs_trafo_tab")).fetchall()
    transformer_ids_set = set([obj[0] for obj in transformer_ids_resp])

    # 2. 过滤 DataFrame，只保留不存在的 transformerid
    df_to_insert2 = transformer[~transformer['transformerid'].isin(transformer_ids_set)]
    if not df_to_insert2.empty:
        df_to_insert2.to_sql(
            name="sys_qs_trafo_tab",  # MySQL 中要写入的表名（需提前创建表结构，或让 pandas 自动创建）
            con=db.connection(),  # 数据库连接引擎
            if_exists="append",  # 表存在时的行为：append（追加）、replace（替换表）、fail（报错）
            index=False,  # 不写入 DataFrame 的索引列（除非表有对应的索引字段）
        )
    db.commit()
    # 线路
    map_line_index_tab_sql = f"""
    select dev_id,id from sys_qs_line_real_day_index_tab where field_key = 'line_power_p'
    and day_time = '{day_time}'
    """
    map_line_index_tab_resp = db.execute(text(map_line_index_tab_sql)).fetchall()
    map_line_index_tab_df = pd.DataFrame(map_line_index_tab_resp)
    if map_line_index_tab_df.empty:
        pending_acline_ids = set(acline['AClineid'].tolist())
        map_line_index_tab_dict = {}
    else:
        map_line_index_tab_dict = map_line_index_tab_df.set_index("dev_id")["id"].to_dict()
        pending_acline_ids = set(acline['AClineid'].tolist()) - set(list(map_line_index_tab_dict.keys()))

    # 主变
    map_trafo_index_tab_sql = f"""
    select dev_id,id from sys_qs_trafo_real_day_index_tab where field_key = 'trafo_power_p'
    and day_time = '{day_time}'
    """
    map_trafo_index_tab_resp = db.execute(text(map_trafo_index_tab_sql)).fetchall()
    map_trafo_index_tab_df = pd.DataFrame(map_trafo_index_tab_resp)
    if map_trafo_index_tab_df.empty:
        pending_trafo_ids = set(transformer['transformerid'].tolist())
        map_trafo_index_tab_dict = {}
    else:
        map_trafo_index_tab_dict = map_trafo_index_tab_df.set_index("dev_id")["id"].to_dict()
        pending_trafo_ids = set(transformer['transformerid'].tolist()) - set(list(map_trafo_index_tab_dict.keys()))

    nowtime = str(dt.datetime.now())
    # 添加日数据
    insert_acline = f"""
    insert into sys_qs_line_real_day_data_tab (id,power_id,time,value,create_time,update_time, is_delete)
    values 
    """
    for _, row in acline.iterrows():
        aclineid = row['AClineid']
        name = row['name']
        if aclineid in pending_acline_ids:
            # 新增一条记录
            power_id = str(uuid.uuid4()).replace("-", "")
            sql2 = f"""
            insert into sys_qs_line_real_day_index_tab (id, field_key,power_node,day_time,
            dev_id,display_desc, create_time,update_time, is_delete)
            values ('{power_id}','line_power_p','I','{day_time}','{aclineid}','{name}', '{nowtime}', '{nowtime}', 0)
            """
            db.execute(text(sql2))
            map_line_index_tab_dict[aclineid] = power_id
        # 全量新增日数据
        ac_id = str(uuid.uuid4()).replace("-", "")
        power_id = map_line_index_tab_dict.get(aclineid)
        insert_acline += f"""
        ('{ac_id}','{power_id}','{closest_time}',{row["Pi_meas"]}, '{nowtime}', '{nowtime}', 0),
        """
    # 全量删除线路日数据
    del_line_day_data_sql = f"""
    delete from sys_qs_line_real_day_data_tab where time = '{closest_time}'
    """
    db.execute(text(del_line_day_data_sql))
    logger.info(f"全量删除线路日数据: {closest_time}")
    # 一次性全量插入数据
    insert_acline = insert_acline.strip().strip(",")
    db.execute(text(insert_acline))
    logger.info(f"全量插入线路日数据: {closest_time}")

    insert_transformer = f"""
    insert into sys_qs_trafo_real_day_data_tab (id,power_id,time,value,create_time,update_time, is_delete)
    values 
    """
    for _, row in transformer.iterrows():
        transformerid = row['transformerid']
        name = row['name']
        if transformerid in pending_trafo_ids:
            # 新增一条记录
            power_id = str(uuid.uuid4()).replace("-", "")
            sql5 = f"""
            insert into sys_qs_trafo_real_day_index_tab (id, field_key,power_node,day_time,
            dev_id,display_desc, create_time,update_time, is_delete)
            values ('{power_id}','trafo_power_p','hv','{day_time}','{transformerid}','{name}', '{nowtime}', '{nowtime}', 0)
            """
            db.execute(text(sql5))
            map_trafo_index_tab_dict[transformerid] = power_id
        transformer_id = str(uuid.uuid4()).replace("-", "")
        power_id = map_trafo_index_tab_dict.get(transformerid)
        insert_transformer += f"""
        ('{transformer_id}','{power_id}','{closest_time}','{row["Pi_meas"]}', '{nowtime}', '{nowtime}', 0),
        """
    # 全量删除主变日数据
    del_trafo_day_data_sql = f"""
    delete from sys_qs_trafo_real_day_data_tab where time = '{closest_time}'
    """
    db.execute(text(del_trafo_day_data_sql))
    logger.info(f"全量删除主变日数据: {closest_time}")
    # 一次性全量插入数据
    insert_transformer = insert_transformer.strip().strip(",")
    db.execute(text(insert_transformer))
    db.commit()
    logger.info(f"全量插入主变日数据: {closest_time}")


def real_qs_interface_data(
    db: SessionLocal,
    day_time: str,
    closest_time: str,
    is_last: bool = True,
    g_real_case: Optional[CRCaseSta] = None
):
    """
    从qs文件解析出断面数据
    """
    if is_last:
        inf_dict, inf_power_dict = common.g_real_case.qs_inf_power_cal(common.inf_define_file)
    else:
        inf_dict, inf_power_dict = g_real_case.qs_inf_power_cal(common.inf_define_file)
    nowtime = str(dt.datetime.now())
    # 查询全部断面名称
    all_interface_sql = f"""
    select inf_name, id from sys_qs_interface_tab
    """
    all_interface_resp = db.execute(text(all_interface_sql)).fetchall()
    all_interface_df = pd.DataFrame(all_interface_resp)
    if all_interface_df.empty:
        map_interface_index_tab_dict = {}
    else:
        map_interface_index_tab_dict = all_interface_df.set_index("inf_name")["id"].to_dict()

    # 查询全部断面索引
    map_inf_real_day_index_tab_sql = f"""
    select dev_id,id from sys_real_day_index_tab where field_key = 'inf_power'
    and day_time = '{day_time}'
    """
    map_inf_real_day_index_tab_resp = db.execute(text(map_inf_real_day_index_tab_sql)).fetchall()
    map_inf_real_day_index_tab_df = pd.DataFrame(map_inf_real_day_index_tab_resp)
    if map_inf_real_day_index_tab_df.empty:
        map_inf_real_day_index_tab_dict = {}
    else:
        map_inf_real_day_index_tab_dict = map_inf_real_day_index_tab_df.set_index("dev_id")["id"].to_dict()

    # 插入断面日数据sql
    insert_all_inf_sql = f"""
    insert into sys_real_day_data_tab (id,power_id,time,value,create_time,update_time,is_delete)
    values 
    """
    base_inf_name = set()
    pending_del_day_data_power_ids = set()
    for inf_type, inf_data in inf_dict.items():
        for zone, v1 in inf_data.items():
            for inf_name, v2 in v1.items():
                display_desc = v2["inf_desc"]
                sub_type = v2.get("sub_type", "")
                if inf_name not in map_interface_index_tab_dict:
                    # 新增一条记录
                    inf_id = str(uuid.uuid4()).replace("-", "")
                    member_count = v2['element_num']['col_value']
                    inf_limit = v2['inf_limit']['col_value']
                    sql2 = f"""
                    insert into sys_qs_interface_tab (id,inf_name,inf_desc,inf_limit,in_service,
                    vlevel,member_count,create_time,update_time,is_delete,inf_type,zone, sub_type) values (
                    '{inf_id}','{inf_name}','{display_desc}',{inf_limit},
                    1,{v2["inf_level"]},{member_count},'{nowtime}','{nowtime}',0,
                    '{inf_type}', '{zone}', '{sub_type}'
                    )
                    """
                    db.execute(text(sql2))
                    map_interface_index_tab_dict[inf_name] = inf_id
                # 新增到实时数据表中
                inf_dev_id = map_interface_index_tab_dict.get(inf_name)  # 测点id
                if inf_dev_id not in map_inf_real_day_index_tab_dict:
                    # 新增一条记录
                    real_id = str(uuid.uuid4()).replace("-", "")
                    sql4 = f"""
                    insert into sys_real_day_index_tab (id,field_key,day_time,dev_id,display_desc,
                    create_time,update_time,is_delete) values (
                    '{real_id}','inf_power','{day_time}','{inf_dev_id}',
                    '{display_desc}','{nowtime}','{nowtime}',0
                    )
                    """
                    db.execute(text(sql4))
                    map_inf_real_day_index_tab_dict[inf_dev_id] = real_id
                # 新增数据
                power_id = map_inf_real_day_index_tab_dict.get(inf_dev_id)
                data_id = str(uuid.uuid4()).replace("-", "")
                value = inf_power_dict[inf_type][zone][inf_name]['power']
                insert_all_inf_sql += f"""
                ('{data_id}','{power_id}','{closest_time}','{value}','{nowtime}','{nowtime}',0),
                """
                # 添加到待删除的power_id
                pending_del_day_data_power_ids.add(power_id)
                # 添加断面名称到集合中，以备最后找出需要删除的断面信息
                base_inf_name.add(inf_name)
    # 全量删除日数据
    if pending_del_day_data_power_ids:
        del_real_day_data_sql = f"""
        delete from sys_real_day_data_tab
        where time = '{closest_time}' and power_id in {tuple(pending_del_day_data_power_ids)}
        """
        db.execute(text(del_real_day_data_sql))
        logger.info(f"全量删除断面日数据------>{closest_time}")
    # 一次清全量插入日数据
    insert_all_inf_sql = insert_all_inf_sql.strip().strip(",")
    db.execute(text(insert_all_inf_sql))
    logger.info(f"全量插入断面日数据: {closest_time}")
    # 找出需要删除的断面数据
    pending_delete_inf_names = set(list(map_interface_index_tab_dict.keys())) - base_inf_name
    logger.info(f"需要删除的旧断面数据长度: {len(pending_delete_inf_names)}")
    if pending_delete_inf_names:
        if len(pending_delete_inf_names) == 1:
            del_inf_name_sql = f"""
            delete from sys_qs_interface_tab where inf_name = '{list(pending_delete_inf_names)[0]}'
            """
        else:
            del_inf_name_sql = f"""
            delete from sys_qs_interface_tab where inf_name in {tuple(pending_delete_inf_names)}
            """
        db.execute(text(del_inf_name_sql))
        logger.info(f"删除旧断面数据完成: {len(pending_delete_inf_names)}")
    db.commit()


def _process_line_interface(qs_file_path: str, filename: str, config: Dict):
    db = SessionLocal()
    try:
        logger.info(f"解析实时qs文件-断面数据: {qs_file_path}")
        g_real_case_interface = CRCaseSta(config=config, qs_path=common.realtime_qs_interface, qs_name=filename)
        g_real_case_interface.get_realcase(trans_teap=False)
        case_time = g_real_case_interface.case_time
        day_value, time_value = case_time.split("_")
        day_obj = dt.datetime.strptime(day_value, "%Y%m%d")
        day_value_obj = day_obj.strftime("%Y-%m-%d")
        date_time = f"{day_value}{time_value}"
        date_time_obj = dt.datetime.strptime(date_time, "%Y%m%d%H%M%S")
        day_time_value = date_time_obj.strftime("%Y-%m-%d %H:%M:%S")
        date_list = pd.date_range(start=f'{day_value_obj} 00:00:00', end=f'{day_value_obj} 23:59:59', freq='5T')
        t1 = pd.Timestamp(day_time_value)
        time_diffs = pd.Series((date_list - t1)).apply(lambda x: x.total_seconds()).abs()
        # 找到时间差最小的索引并获取对应的时间
        closest_index = time_diffs.idxmin()
        closest_time = date_list[closest_index]
        closest_day = closest_time.date()
        closest_time = closest_time.strftime('%Y-%m-%d %H:%M:%S')
        real_qs_line_trafo_interface_analysis(
            db=db,
            day_time=str(closest_day),
            closest_time=closest_time,
            is_last=False,
            g_real_case=g_real_case_interface
        )
        real_qs_interface_data(
            db=db,
            day_time=str(closest_day),
            closest_time=closest_time,
            is_last=False,
            g_real_case=g_real_case_interface
        )
        # 删除文件
        os.remove(qs_file_path)
        logger.info(f"解析qs文件获取断面数据完成-------->{qs_file_path}")
    except Exception as e:
        db.rollback()
        logger.exception(e)
    finally:
        db.close()


@sched.scheduled_job('interval', minutes=3)
def real_analysis_interface_from_qs():
    """
    从qs文件解析断面数据
    """
    # 执行任务的内容，例如打印当前时间
    logger.info(f"开始解析实时qs文件断面、线路、主变数据")
    full_path_real_case_config = os.path.join(common.base_path, f"qs/{common.realtime_qs_config_yml}")
    if not os.path.isfile(full_path_real_case_config):
        logger.exception("qs的配置文件不存在")
        return
    config = load_config_file(common.realtime_qs_config_yml)
    # 获取目录下的所有文件列表
    for file in sorted(os.listdir(common.realtime_qs_interface)):
        if not file.endswith((".QS", ".qs")):
            continue
        qs_file_path = os.path.join(common.realtime_qs_interface, file)
        args = {"qs_file_path": qs_file_path, "filename": file, "config": config}
        logger.info(f"args----------------->{args}")
        _process_line_interface(**args)


# 监听实时数据，每分钟执行的定时任务
@sched.scheduled_job('interval', minutes=3)
def realtime_handle():
    if common.is_redis_lock in [1, "1"]:
        if not redis_obj.set(common.redis_realtime_analyze_key, 1, nx=True, ex=300):
            logger.info(f"解析实时qs文件未获取到redis锁")
            return
        logger.info(f"解析实时qs文件已获取到redis锁")
    db = SessionLocal()
    file_path = ""
    try:
        # 执行任务的内容，例如打印当前时间
        logger.info(f"开始解析实时qs文件数据")
        full_path_real_case_config = os.path.join(common.base_path, f"qs/{common.realtime_qs_config_yml}")
        if not os.path.isfile(full_path_real_case_config):
            logger.exception("qs的配置文件不存在")
            return
        config = load_config_file(common.realtime_qs_config_yml)
        # 获取目录下的所有文件列表
        files = []
        for file in os.listdir(common.realtime_qs_dir):
            if not file.endswith((".QS", ".qs")):
                continue
            if os.path.isfile(os.path.join(common.realtime_qs_dir, file)):
                files.append(os.path.join(common.realtime_qs_dir, file))
        if not files:
            logger.info(f"实时态数据目录下未发现qs文件")
            return
        # 使用sorted函数按照修改时间倒序排序，os.path.getmtime用于获取文件修改时间
        sorted_files = sorted(files, reverse=True)
        file_path = sorted_files[0]
        qs_file_name = os.path.basename(file_path)
        qs_file_path = file_path
        logger.info(f"解析实时qs文件: {file_path}")
        common.g_real_case = CRCaseSta(config=config, qs_path=common.realtime_qs_dir, qs_name=qs_file_name)
        common.g_real_case.get_realcase()
        # # 解析完成qs文件之后，把数据写入实时的表中
        area_list = ["全省"] + list(common.g_real_case.realcase.area_ele_detail.keys())
        power_dict = common.g_real_case.get_realcase_power_info(area_list=area_list)
        case_time = power_dict.get("case_time")
        day_value, time_value = case_time.split("_")
        day_obj = dt.datetime.strptime(day_value, "%Y%m%d")
        day_value_obj = day_obj.strftime("%Y-%m-%d")
        date_time = f"{day_value}{time_value}"
        date_time_obj = dt.datetime.strptime(date_time, "%Y%m%d%H%M%S")
        day_time_value = date_time_obj.strftime("%Y-%m-%d %H:%M:%S")
        date_list = pd.date_range(start=f'{day_value_obj} 00:00:00', end=f'{day_value_obj} 23:59:59', freq='5T')
        t1 = pd.Timestamp(day_time_value)
        time_diffs = pd.Series((date_list - t1)).apply(lambda x: x.total_seconds()).abs()
        # 找到时间差最小的索引并获取对应的时间
        closest_index = time_diffs.idxmin()
        closest_time = date_list[closest_index]
        closest_day = closest_time.date()
        closest_time = closest_time.strftime('%Y-%m-%d %H:%M:%S')
        # 查询测点id
        area_dev_list = db.query(MapField).filter(MapField.data_source == "qs").all()
        dev_dict = {}
        for obj in area_dev_list:
            k = f"{obj.field_key}_{obj.area_name}"
            dev_dict[k] = obj.dev_id
        for area, data in power_dict.items():
            if area == "case_time":
                continue
            for k, v in data.items():
                new_k = f"{k}_{area}"
                if new_k not in dev_dict:
                    continue
                dev_id = dev_dict[new_k]
                power_obj = db.query(PowerData).filter(
                    PowerData.area_name == area, PowerData.field_key == k, PowerData.day_time == closest_day,
                    PowerData.dev_id == dev_id
                ).first()
                if not power_obj:
                    power_obj = PowerData(area_name=area, field_key=k, day_time=closest_day, dev_id=dev_id)
                    db.add(power_obj)
                    db.flush()
                power_time_obj = db.query(PowerDataTime).filter(
                    PowerDataTime.is_delete == 0, PowerDataTime.power_id == power_obj.id,
                    PowerDataTime.time == closest_time
                ).first()
                if not power_time_obj:
                    power_time_obj = PowerDataTime(power_id=power_obj.id, time=closest_time, value=v)
                    db.add(power_time_obj)
                else:
                    power_time_obj.value = v
                db.commit()
        # 清空realtime_last_qs目录下所有文件
        clear_directory(common.realtime_last_qs)
        # 拷贝最新的qs文件到realtime_last_qs目录下，以备程序重启时可以加载到最新的qs文件数据
        move_target_file_path = os.path.join(common.realtime_last_qs, qs_file_name)
        shutil.copy2(qs_file_path, move_target_file_path)
        # 清空中短期QS目录
        clear_directory(common.short_qs_dir)
        # 拷贝qs文件到short_qs_dir
        short_target_qs_file_path = os.path.join(common.short_qs_dir, qs_file_name)
        shutil.copy2(qs_file_path, short_target_qs_file_path)
        # 拷贝qs文件到realtime_qs_interface目录，以备解析qs文件数据，获取断面数据
        # 主要是考虑到那些未被解析的qs文件数据
        if len(sorted_files) > 1:
            for new_qs_file in sorted_files[1:]:
                new_qs_filename = os.path.basename(new_qs_file)
                interface_qs_file_path = os.path.join(common.realtime_qs_interface, new_qs_filename)
                shutil.copy2(new_qs_file, interface_qs_file_path)
        # 以下逻辑，每天定时解析的实时qs文件，若是解析时间超过了当天的5点之后
        # 则把qs文件拷贝到中短期qs目录下，当前时间若是整小时，则把qs文件拷贝过去，否则不拷贝
        short_qs_obj = db.query(SysShortQs).filter(SysShortQs.is_delete == 0).first()
        if short_qs_obj and short_qs_obj.value == 1:
            # 立刻解析中短期qs文件
            th = threading.Thread(target=auto_analyze_case, daemon=True)
            th.start()
        # 把剩下的所有qs文件移动到备份目录下
        nowtime = dt.datetime.now()
        year = nowtime.year
        month = nowtime.month
        day = nowtime.day
        now_backup_dir = os.path.join(common.realtime_qs_backup, str(year), str(month), str(day))
        if not os.path.isdir(now_backup_dir):
            os.makedirs(now_backup_dir, exist_ok=True)
        for f in sorted_files:
            f_name = os.path.basename(f)
            mv_file = os.path.join(now_backup_dir, f_name)
            shutil.move(f, mv_file)
        real_qs_line_trafo_interface_analysis(db=db, day_time=str(closest_day), closest_time=closest_time)
        real_qs_interface_data(db=db, day_time=str(closest_day), closest_time=closest_time)
        logger.info("实时态数据解析完成")
    except Exception as e:
        db.rollback()
        logger.exception(e)
        logger.error(f"qs file=========================>{file_path}")
    finally:
        db.close()
        if common.is_redis_lock in [1, "1"]:
            # 释放reids锁
            redis_obj.delete(common.redis_realtime_analyze_key)
            logger.info(f"解析实时qs文件释放redis锁成功")


def load_realtime_data():
    """
    启动时加载实时态数据到内存
    """
    if not os.path.isdir(common.realtime_last_qs):
        logger.info("实时态备份目录不存在")
        return
    qs_file_name = ""
    for file in os.listdir(common.realtime_last_qs):
        if not file.endswith((".QS", ".qs")):
            continue
        qs_file_name = file
        break
    if not qs_file_name:
        logger.info(f"备份目录未发现最新的qs文件")
        return
    config = load_config_file(common.realtime_qs_config_yml)
    common.g_real_case = CRCaseSta(config=config, qs_path=common.realtime_last_qs, qs_name=qs_file_name)
    common.g_real_case.get_realcase()
    logger.info("加载最新时刻的实时态数据成功")


# 把实时数据备份目录下的qs文件整体打包成压缩包
@sched.scheduled_job('cron', hour=0, minute=30)
def realtime_backup():
    logger.info(f"开始把昨天的qs备份目录打包")
    nowtime = dt.datetime.now()
    nowtime = nowtime - dt.timedelta(days=1)
    year = nowtime.year
    month = nowtime.month
    day = nowtime.day
    yeaterday_realtime_backup_dir = os.path.join(common.realtime_qs_backup, str(year), str(month), str(day))
    if not os.path.isdir(yeaterday_realtime_backup_dir):
        logger.info(f"{year}_{month}_{day} qs备份目录不存在，则不做打包处理")
        return
    # 打包目录
    for root, dirs, files in os.walk(yeaterday_realtime_backup_dir):
        for file in files:
            file_path = os.path.join(root, file)
            arcname = os.path.relpath(file_path, yeaterday_realtime_backup_dir)
            out_tar_file = os.path.join(
                common.realtime_qs_backup, str(year), str(month), str(day), f"{file}.tar.gz"
            )
            with tarfile.open(out_tar_file, "w:gz") as tar:
                tar.add(file_path, arcname=arcname)
            # 打包完成之后，把源文件删除
            os.remove(file_path)


# 把实时数据备份目录下的qs文件整体打包成压缩包
@sched.scheduled_job('cron', hour=1, minute=0)
def update_scense():
    """
    每天晚上1点，更新典型场景数据
    """
    logger.info(f"开始更新典型场景数据")
    process_scene()
    logger.info(f"更新典型场景数据完成")
