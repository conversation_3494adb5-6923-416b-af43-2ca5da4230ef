import json
import os
import shutil
import threading
from concurrent.futures import ThreadPoolExecutor

base_path = os.path.dirname(os.path.dirname(__file__))
# 文件存放路径
file_storage = os.path.join(base_path, 'db/files/')
# 临时文件存放路径
file_tmp_storage = os.path.join(file_storage, 'tmp')

# 数据库---h5文件的key
db_key = 'baogong'
# 数据库文件路径（算例）
db_path = os.path.join(base_path, 'db/data.json')
# 数据库文件(中短期算例，仅限河南存在)
db_short_file = os.path.join(base_path, 'db/short.json')
# network数据存储路径
db_network_dir = os.path.join(base_path, 'db/network')
# 创建network目录
if not os.path.isdir(db_network_dir):
    os.makedirs(db_network_dir)

# 系统配置文件路径
db_system_path = os.path.join(base_path, 'db/system.json')

# result_output数据存储路径
db_output_dir = os.path.join(base_path, 'db/output')
# 创建output目录
if not os.path.isdir(db_output_dir):
    os.makedirs(db_output_dir)

# ptdf数据存储路径
db_ptdf_dir = os.path.join(base_path, 'db/ptdf')
# 创建output目录
if not os.path.isdir(db_ptdf_dir):
    os.makedirs(db_ptdf_dir)

# 检查目录files是否存在，若不存在，则提前创建
if not os.path.isdir(file_storage):
    os.makedirs(file_storage)
# 检查files目录下的tmp目录是否存在，若不存在，则提前创建
if not os.path.isdir(file_tmp_storage):
    os.makedirs(file_tmp_storage)

# 算例分析日志文件目录
case_logs_dir = os.path.join(base_path, 'db/logs/')
# 创建算例分析日志文件目录
if not os.path.isdir(case_logs_dir):
    os.makedirs(case_logs_dir)

# cal_result
cal_result_path = os.path.join(base_path, 'db/cal_result/')
if not os.path.isdir(cal_result_path):
    os.makedirs(cal_result_path)
# case_info
case_info_path = os.path.join(base_path, 'db/case_info/')
if not os.path.isdir(case_info_path):
    os.makedirs(case_info_path)

# indicator
indicator_path = os.path.join(base_path, 'db/indicator/')
if not os.path.isdir(indicator_path):
    os.makedirs(indicator_path)

case_dict = {}  # 算例缓存对象信息
short_case_dict = {}  # 短期算例缓存对象信息，仅限河南
case_year_dict = []  # 年份结果集

# 创建线程池
pool = ThreadPoolExecutor(max_workers=10)
# 创建一把线程锁
lock = threading.RLock()

JIANGSU_TAG = 'jiangsu'
HENAN_TAG = 'henan'
HUNAN_TAG = 'hunan'
JIYUAN_TAG = 'jiyuan'
GANSU_TAG = 'gansu'

# 配置文件存储路径
db_config_path = os.path.join(base_path, 'db/config')
# 创建配置文件目录
if not os.path.isdir(db_config_path):
    os.makedirs(db_config_path)

# 配置登陆信息文件
db_login = os.path.join(base_path, 'db/login.json')
# 创建登陆信息文件
if not os.path.isfile(db_login):
    with open(db_login, 'w') as fp:
        fp.write(json.dumps({}, indent=4, ensure_ascii=False))

LOGIN_USER = 'admin'
LOGIN_PASSWORD = 'baogong@)@$'
LOGIN_EXPIRE = 1  # 用户登陆过期时间，默认位1天，也就是次日凌晨1点

allow_api = [
    '/api/v1/common/login', '/api/v1/common/case/download', '/api/v1/common/auto/analyze/case',
    '/api/v1/henan/user/login', '/api/v1/henan/now/generate/case', '/api/v1/henan/set/generate/case',
    '/api/v1/henan/system/log'
]

teap_case_origin_path = os.path.join(base_path, "db/teap_case")  # 算例原文件路径
if not os.path.isdir(teap_case_origin_path):
    os.makedirs(teap_case_origin_path, exist_ok=True)

henan_long_case_file = os.path.join(base_path, "240703henan_v4.20.xlsx")  # 河南长期基础算例
henan_long_case_dir = os.path.join(base_path, "db/long_case_dir")  # 河南长期算例目录
if not os.path.isdir(henan_long_case_dir):
    os.makedirs(henan_long_case_dir, exist_ok=True)
# 检查河南长期算例目录下是否存在xls文件，若是不存在，则把根目录下的240703henan_v4.20.xlsx文件拷贝过去
is_henan_long_exist = False
for f in os.listdir(henan_long_case_dir):
    if f.endswith(".xlsx"):
        is_henan_long_exist = True
        break
if not is_henan_long_exist:
    shutil.copy2(henan_long_case_file, henan_long_case_dir)

# 河南实时态数据处理
realtime_qs_dir = os.path.join(base_path, "db/realtime_qs_dir")
if not os.path.isdir(realtime_qs_dir):
    os.makedirs(realtime_qs_dir, exist_ok=True)
# qs 文件最近的一个文件
realtime_last_qs = os.path.join(base_path, "db/realtime_last_qs")
if not os.path.isdir(realtime_last_qs):
    os.makedirs(realtime_last_qs, exist_ok=True)
# qs 文件备份目录
realtime_qs_backup = os.path.join(base_path, "db/realtime_qs_backup")
if not os.path.isdir(realtime_qs_backup):
    os.makedirs(realtime_qs_backup, exist_ok=True)
# 河南-qs文件备份解析断面数据
realtime_qs_interface = os.path.join(base_path, "db/realtime_qs_interface")
if not os.path.isdir(realtime_qs_interface):
    os.makedirs(realtime_qs_interface, exist_ok=True)
# 中短期qs文件存在位置
short_qs_dir = os.path.join(base_path, "db/short_qs_dir")
if not os.path.isdir(short_qs_dir):
    os.makedirs(short_qs_dir)

realtime_qs_config_yml = "config.yml"

g_real_case = None
g_last_datetime = ""
g_real_case_interface = None
alembic_ini_path = os.path.join(base_path, "alembic.ini")

# 新能源预测数据文件
new_energy_dir = os.path.join(base_path, "db/new_energy_dir")
if not os.path.isdir(new_energy_dir):
    os.makedirs(new_energy_dir)

field_map = {
    "load": "负荷",
    "solar": "光伏",
    "wind": "风电",
    "gen_coal": "煤电",
    "gen_gas": "燃气",
    "solar_distribute": "分布式光伏发电",
    "gen_hydro": "水电",
    "stogen_energy_storage": "储能",
    "wind_curtailment": "弃风",
    "reserve_down": "下旋备",
    "reserve_up": "上旋备",
    "wind_distribute": "分布式风电",
    "solar_centralized": "统调光伏",
    'stogen_pump_hydro': "抽蓄",
    "feedin": "区外来电",
    "gen_self": "自备电厂",
    "solar_curtailment": "弃光",
    "gen_all": "全网发电",
    "gen_heat": "供热煤电",
    "gen_coal_noheat": "非供热机组"
}

field_map2 = {
    "负荷": "load",
    "光伏发电": "solar",
    "光伏": "solar",
    "风力发电": "wind",
    "风电": "wind",
    "煤电": "gen_coal",
    "燃气": "gen_gas",
    "分布式光伏发电": "solar_distribute",
    "水利发电": "gen_hydro",
    "水电": "gen_hydro",
    "储能": "stogen_energy_storage",
    "弃风": "wind_curtailment",
    "下旋备": "reserve_down",
    "上旋备": "reserve_up",
    "分布式风电": "wind_distribute",
    "统调光伏": "solar_centralized",
    "抽蓄": "stogen_pump_hydro",
    "区外来电": "feedin",
    "自备电厂": "gen_self",
    "弃光": "solar_curtailment",
    "全网发电": "gen_all",
    "全部": "gen_all",
    "供热": "gen_heat",
    "供热机组": "gen_heat",
    "供热煤电": "gen_heat",
    "非供热机组": "gen_coal_noheat",
    "非供热煤电": "gen_coal_noheat"
}

gen_all_keys = [
    "gen_coal", "gen_gas", "gen_heat", "gen_self", "gen_hydro", "stogen_pump_hydro", "wind", "solar",
    "stogen_energy_storage"
]

short_qs_analyze_status = False

display_desc_quansheng_map = {"feedin": "受入总加", "load": "全网用电负荷", "wind": "全网风电总加(集中式+分散式)"}

# 常规机组页面中的gen_type类型
usual_gen_all_keys_two = ["gen_coal", "gen_gas", "gen_heat", "gen_hydro", "stogen_pump_hydro"]
usual_gen_all_keys_one = ["gen_coal", "gen_gas", "gen_heat", "gen_hydro", "stogen_pump_hydro", "wind", "solar"]
usual_gen_all_keys_three = ["gen_coal", "gen_gas", "gen_heat", "gen_hydro", "stogen_pump_hydro"]

# 电压等级筛选映射
voltage_level_map = {1: 0, 2: 500, 3: 220}
# 直流曲线排序
curve_order_dict = {"受入总加": 1, "全省直流有功总和": 2, "青豫本侧": 3, "天中直流": 4, "灵宝直流": 5, "长南特高压功率": 6, "南荆线": 7, "豫武线": 8}

# 河南用户登录过期时间, 8小时
user_login_expire_time = 8
# redis中解析实时qs文件锁
redis_realtime_analyze_key = "hn_realtime_qs"
# redis中解析中短期qs文件锁
redis_short_analyze_key = "hn_short_qs"
# 0: 不使用redis锁，1:使用redis锁
is_redis_lock = os.getenv("is_redis_lock", 0)
# 断面定义数据
inf_define_file = os.path.join(base_path, "common", "inf_define.yml")
