## 本模块有两部分功能:

### (1)保供预统计与分析功能: 入口:ssmetrics_main.py,TeapAna_main()

### (2)推演分析计算功能:入口:common_utils/calculator_view.py,run_calculator_func()

## 配置文件: conf/config_extern.py

## TIPS:

(1) 本模块非常驻运行程序，若有新的算例传入，调用本模块；
(2) 执行新算例的统计与分析功能;待统计与分析完成后，生成算例相关统计分析结果文件
(3) 针对不同应用场景，需要调整配置参数文件：conf/config_extern.py文件
单位:
存储和输出至前端页面的所有电力,均为万千瓦;
存储和输出至前端页面的电量日均使用万千瓦时，月/年均使用亿千瓦时;

计算上、下调节能力和调峰缺口时，teap2中备用约束无法松弛,因此，上述的值计算都考虑了备用量
例如：下调节能力 = on_p - on_p_min - load_reserve_need
调峰缺口 = on_min + load_reserve_need + 新能源出力 + stogen出力 + 水电出力 + feedin - 负荷有功，需要加上:
load_reserve_need

## 推演计算分析功能逻辑：

### 全网：

支持负荷、新能源、燃气机组、区外直流进行调整，并支持调整选择分配的分区;
支持设备N-1，添加移相器、串抗设备;

### 分区：

只支持分区负荷、分区的风光出力调整，别的不支持调整;

## 基于TEAP的case算例的保供计算算例文件的变动:

(1) bus:
name vn_kv zone in_service heat_zone area lon lat type dispname
增加:
'zone_name': 必须要有,用于展示分区的显示名称
lon: 经度
lat: 纬度
type: 类型 station/gas/dc....
dispname: 节点展示真实名称
zone_name: 分区展示名称
area_name: 区域展示名称
备注：
目前支持:zone—area两级分区结构:
zone：对应最小供电分区
area: 对应： 苏南/苏北分区, 豫西/豫中东等分区;area--由供电分区组成

gen:
dispname:  代替原'fullname'

line:
'stable_limit_mw':
dispname: 线路显示名称

trafo:
'stable_limit_mw':
dispname: 主变显示名称

wind/solar:
city: 站点所在城市--方便统计，如果缺此列,默认获取bus关联的zone名称

## 江苏保供添加智能推演子仓库

当前代码在主仓库中引入了若干子仓库，如（tsa）等，在首次git clone整个仓库后，子仓库需要先初始化，在主仓库的目录下执行如下命令：

```bash
git submodule update --init --recursive
```

初始化完成后，可以进入子仓库目录进行各种常规git操作，如拉取代码、切换分支等，同时子仓库改动不会影响主仓库。更新子仓库代码的简单方式：

```bash
git submodule update --remote --merge
```