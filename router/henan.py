import copy
import datetime as dt
import os
import zipfile
from collections import defaultdict
from dataclasses import asdict
from typing import Union
import functools
import uuid

import numpy as np
import pandas as pd
import yaml
from fastapi import APIRouter
from fastapi import Depends
from loguru import logger
from sqlalchemy import text
from starlette.responses import FileResponse

import common
import data
import utils
from alg.common_utils import calculator_view
from alg.common_utils import data_utils
from alg.common_utils import ultra_time_data_utils
from data import params
from database.models import *
from qs.histime_utils import hisdata_utils
from utils import history_service
from utils.interface_tool import *
from utils.cache import get_data_with_cache
from database.connect import url as db_uri

router = APIRouter()


@router.get('/case/all', tags=[common.HENAN_TAG], response_model=data.Response)
def all_case(
        is_short: int = 1, page: int = 1, size: int = 20, result: data.Response = Depends(utils.response_obj)
):
    """
    获取所有算例
    :param is_short: 1: 长期算例, 2: 中短期算例
    :param result: 返回值
    :return:
    """
    if is_short == 1:
        db_file = common.db_path
    else:
        db_file = common.db_short_file
    if not os.path.isfile(db_file):
        result.data = []
        return result
    # 读取h5数据集
    db_data = utils.get_db_data(is_short=is_short)
    case_list = []
    for case_id, case in db_data.items():
        if case.get('status') != 4:
            continue
        new_case = {
            'id': case_id,
            'name': case.get('name', ''),
            'filename': case.get("filename", ''),
            'year': case.get('year', 0),
            'status': case.get('status', 0),  # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
            'create_time': case.get('create_time'),
            'update_time': case.get('update_time'),
            'size': utils.calculate_size(case.get("size", 0)),
            'content_type': case.get("content_type", ''),
            'comment': case.get("comment", ''),
            'time_value': case.get("time_value", 0),
            'max_load': case.get("max_load", 0),
            'new_energy_zhuangji': case.get("new_energy_zhuangji", 0)
        }
        case_list.append(new_case)
    start_index = (page - 1) * size
    end_index = start_index + size
    result.data = case_list[start_index:end_index]
    return result


# 装机容量
@router.get("/zhuangji/capacity", tags=[common.HENAN_TAG], response_model=data.Response)
def zhuangji_capacity(case_id: str, area: str = "全省", result: data.Response = Depends(utils.response_obj)):
    """
    获取装机容量
    :param result:
    :return:
    """
    if case_id in common.case_dict:
        case = common.case_dict.get(case_id, {})
    else:
        read_resp = utils.read_case(case_id=case_id)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result
    case_info = case.get('case_info', {})
    data = data_utils.get_allgen_capacity(case_info_dict=case_info, area_name=area)
    result.data = data
    return result


# 结果时序数据
@router.get("/time/sequence", tags=[common.HENAN_TAG], response_model=data.Response)
def time_sequence(
        case_id: str,
        is_short: Union[int, str] = 1,
        area: str = "全省",
        result: data.Response = Depends(utils.response_obj)
):
    """
    获取时序数据
    :param case_id: 算例id
    :param result: 返回值
    :return:
    """
    if isinstance(is_short, str):
        is_short = int(is_short)
    if is_short == 1:
        cache_dict = common.case_dict
    else:
        cache_dict = common.short_case_dict
    if case_id in cache_dict:
        case = cache_dict.get(case_id, {})
    else:
        read_resp = utils.read_case(case_id=case_id, is_short=is_short)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result
    cal_result = case.get("cal_result", {})
    case_info = case.get('case_info', {})
    if area == '全省':
        data = data_utils.get_power_balance_data_curve(cal_result_dict=cal_result, area_name=area)
    else:
        output = case.get("output", {})
        config = case_info.get("config", {})
        network = case.get("network")
        data = data_utils.get_zone_balance_data_curve(
            net=network,
            config=config,
            result_dict=output,
            area_name=area,
            area_details_dict=case_info['zone']['device_relay'],
        )
    times = case_info.get("time_range", [])
    result.data = {"value": data, "time": times}
    return result


@router.get("/map/network/data", tags=[common.HENAN_TAG], response_model=data.Response)
def map_network_data(
        case_id: str,
        time_no: Optional[int] = None,
        is_short: Union[int, str] = 1,
        area: str = "全省",
        result: data.Response = Depends(utils.response_obj)
):
    """
    电网拓扑地图及分区
    :param result:
    :return:
    """
    if isinstance(is_short, str):
        is_short = int(is_short)
    if is_short == 1:
        cache_dict = common.case_dict
    else:
        cache_dict = common.short_case_dict
    if case_id in cache_dict:
        case = cache_dict.get(case_id, {})
    else:
        read_resp = utils.read_case(case_id=case_id, is_short=is_short)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result
    output = case.get("output", {})
    case_info = case.get('case_info', {})
    if time_no:
        data = data_utils.get_network_topo(
            case_info_dict=case_info, time_no=time_no, result_dict=output, area_name=area
        )
    else:
        data = data_utils.get_network_topo(case_info_dict=case_info, result_dict=output, area_name=area)
    result.data = data
    return result


@router.get("/annual/extreme/data", tags=[common.HENAN_TAG], response_model=data.Response)
def annual_extreme_data(case_id: str, area: str = "全省", result: data.Response = Depends(utils.response_obj)):
    """
    全年极值信息，示例：最大负荷, 最大峰谷差, 最大供电缺口...
    :param result:
    :return:
    """
    if case_id in common.case_dict:
        case = common.case_dict.get(case_id, {})
    else:
        read_resp = utils.read_case(case_id=case_id)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result
    case_info = case.get('case_info', {})
    cal_result = case.get('cal_result', {})
    data = data_utils.get_grid_extreme_value_info(case_info_dict=case_info, cal_result_dict=cal_result)
    # 时间序列
    time_range = case_info.get("time_range", [])
    new_data = {}
    for k, value_list in data.items():
        if isinstance(value_list[1], int):
            new_data[k] = {"value": value_list[0], "time": time_range[value_list[1]]}
        else:
            new_data[k] = {"value": value_list[0], "unit": value_list[1]}
    result.data = new_data
    return result


@router.get("/device/reload/list", tags=[common.HENAN_TAG], response_model=data.Response)
def device_reload_list(
        case_id: str,
        is_short: Union[int, str] = 1,
        area: str = "全省",
        result: data.Response = Depends(utils.response_obj)
):
    """
    重载风险设备列表
    :param result:
    :return:
    """
    if isinstance(is_short, str):
        is_short = int(is_short)
    if is_short == 1:
        cache_dict = common.case_dict
    else:
        cache_dict = common.short_case_dict
    if case_id in cache_dict:
        case = cache_dict.get(case_id, {})
    else:
        read_resp = utils.read_case(case_id=case_id, is_short=is_short)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result
    cal_result = case.get('cal_result', {})
    case_info = case.get('case_info', {})
    data = data_utils.get_all_device_loadratio_info(
        cal_result_dict=cal_result, area_name=area, area_details=case_info['zone']['device_relay']
    )
    trafo = data.get("trafo", {})  # 关键主变负载分析
    new_data = {}
    trafo_len = len(trafo.get("index", []))
    keys = list(trafo.keys())
    trafo_list = []
    for i in range(trafo_len):
        new_dict = {}
        for k in keys:
            new_dict[k] = trafo[k][i]
        trafo_list.append(new_dict)
    new_data['trafo'] = trafo_list
    line = data.get("line", {})  # 关键线路负载分析
    line_len = len(line.get("index", []))
    keys = list(line.keys())
    line_list = []
    for i in range(line_len):
        new_dict = {}
        for k in keys:
            new_dict[k] = line[k][i]
        line_list.append(new_dict)
    new_data['line'] = line_list
    interface = data.get("interface", {})  # 关键断面负载分析
    interface_len = len(interface.get("index", []))
    keys = list(interface.keys())
    interface_list = []
    for i in range(interface_len):
        new_dict = {}
        for k in keys:
            new_dict[k] = interface[k][i]
        interface_list.append(new_dict)
    new_data['interface'] = interface_list
    result.data = new_data
    return result


@router.get("/energy/xiaona/rate", tags=[common.HENAN_TAG], response_model=data.Response)
def energy_xiaona_rate(case_id: str, area: str = "全省", result: data.Response = Depends(utils.response_obj)):
    """
    获取能源消纳率数据
    :param result:
    :return:
    """
    if case_id in common.case_dict:
        case = common.case_dict.get(case_id, {})
    else:
        read_resp = utils.read_case(case_id=case_id)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result
    case_info = case.get('case_info', {})
    cal_result = case.get('cal_result', {})
    config = case_info.get("config", {})
    data = data_utils.get_allgen_electric_hours(
        cal_result_dict=cal_result, area_name=area, freq_h='season', config=config
    )
    result.data = data
    return result


@router.get("/power/balance/data", tags=[common.HENAN_TAG], response_model=data.Response)
def power_balance_data(
        case_id: str,
        time_no: int,
        is_short: Union[int, str] = 1,
        area: str = "全省",
        result: data.Response = Depends(utils.response_obj)
):
    """
    获取电力平衡数据最大值和最小值
    :param case_id: 算例id
    :param result: 返回值
    :return:
    """
    if isinstance(is_short, str):
        is_short = int(is_short)
    if is_short == 1:
        cache_dict = common.case_dict
    else:
        cache_dict = common.short_case_dict
    if case_id in cache_dict:
        case = cache_dict.get(case_id, {})
    else:
        read_resp = utils.read_case(case_id=case_id, is_short=is_short)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result
    boundary_data = case.get('indicator', {}).get('boundary_data', {})
    data = data_utils.get_simulation_power_boundry(
        boundary_original=boundary_data, area_name=area, timestep=time_no
    )
    result.data = data
    return result


@router.get("/annual/typical/time", tags=[common.HENAN_TAG], response_model=data.Response)
def annual_typical_time(case_id: str, area: str = "全省", result: data.Response = Depends(utils.response_obj)):
    """
    获取全年典型方式时刻/特殊工况时刻列表
    :param case_id: 算例id
    :param result: 返回值
    :return:
    """
    if case_id in common.case_dict:
        case = common.case_dict.get(case_id, {})
    else:
        read_resp = utils.read_case(case_id=case_id)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result
    indicator = case.get('indicator', {})
    data = data_utils.get_typical_run_mod(indicator_dict=indicator)
    result.data = data
    return result


@router.get("/zone/all/indicator", tags=[common.HENAN_TAG], response_model=data.Response)
def zone_all_indicator(
        case_id: str,
        time_no: Union[str, int],
        is_short: Union[int, str] = 1,
        area: str = "全省",
        result: data.Response = Depends(utils.response_obj)
):
    """
    获取全网的分区所有指标
    :param case_id: 算例id
    :param time_no: 时刻
    :param result: 返回值
    :return:
    """
    if isinstance(is_short, str):
        is_short = int(is_short)
    if is_short == 1:
        cache_dict = common.case_dict
    else:
        cache_dict = common.short_case_dict
    if case_id in cache_dict:
        case = cache_dict.get(case_id, {})
    else:
        read_resp = utils.read_case(case_id=case_id, is_short=is_short)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result
    if isinstance(time_no, str) and time_no.isdigit():
        time_no = int(time_no)
    indicator = case.get('indicator', {})
    case_info = case.get("case_info", {})
    output = case.get("output", {})
    data = data_utils.get_allzone_indicators(
        indicator_rlt_dict=indicator,
        time_no=time_no,
        area_name=area,
        area_details=case_info['zone']['device_relay'],
        result_dict=output
    )
    result.data = data
    return result


@router.get("/device/laodratio/timestep", tags=[common.HENAN_TAG], response_model=data.Response)
def device_laodratio_timestep(
        case_id: str,
        time_no: int,
        is_short: Union[int, str] = 1,
        area: str = "全省",
        result: data.Response = Depends(utils.response_obj)
):
    """
    获取指定时刻的所有设备的有功、负载率、限额数据, 主变/线路/断面
    :param case_id: 算例id
    :param result: 返回值
    :return:
    """
    if isinstance(is_short, str):
        is_short = int(is_short)
    if is_short == 1:
        cache_dict = common.case_dict
    else:
        cache_dict = common.short_case_dict
    if case_id in cache_dict:
        case = cache_dict.get(case_id, {})
    else:
        read_resp = utils.read_case(case_id=case_id, is_short=is_short)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result
    output = case.get('output', {})
    network = case.get("network")
    case_info = case.get("case_info", {})
    config = case_info.get("config", {})
    data = data_utils.get_device_loadratio_timestep(
        net=network,
        result_dict=output,
        time_no=time_no,
        config=config,
        area_name=area,
        area_detail=case_info['zone']['device_relay']
    )
    result.data = data
    return result


@router.post("/deduce/power/data", tags=[common.HENAN_TAG], response_model=data.Response)
def deduce_power_data(item: params.DeducePowerParams, result: data.Response = Depends(utils.response_obj)):
    """
    推演接口
    :param item: 参数
    :param result: 返回值
    :return:
    """
    user_input_data = asdict(item)
    if item.is_short == 1:
        cache_dict = common.case_dict
    else:
        cache_dict = common.short_case_dict
    if item.case_id in cache_dict:
        case = cache_dict.get(item.case_id, {})
    else:
        read_resp = utils.read_case(case_id=item.case_id, is_short=item.is_short)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result
    # result_output
    output = case.get('output', {})
    network = case.get("network")
    case_info = case.get("case_info", {})
    config = case_info.get("config", {})
    indicator = case.get("indicator", {})
    area_details = case_info['zone']['device_relay']
    data = calculator_view.run_calculator_func(
        user_input_data=user_input_data,
        timestep=item.timestep,
        case_result_dict=output,
        case_net=network,
        config=config,
        indicator_rlt_dict=indicator,
        area_details=area_details
    )
    result.data = data
    return result


@router.get("/case/years", tags=[common.HENAN_TAG], response_model=data.Response)
def case_years(result: data.Response = Depends(utils.response_obj)):
    """
    获取算例所有年份
    :param result: 返回值
    :return:
    """
    if not os.path.isfile(common.db_path):
        result.code = 1010
        result.msg = '暂无数据'
        return result
    df = pd.read_hdf(path_or_buf=common.db_path, key=common.db_key)
    df_status = df.loc[df['status'] == 4, ['id', 'year']]
    if len(df_status) == 0:
        result.code = 1011
        result.msg = '暂无已完成分析的算例数据'
        return result
    years = []
    for obj in df_status.values:
        years.append({"year": obj[1], 'case_id': obj[0]})
    result.data = years
    common.case_year_dict = years
    return result


@router.get("/zone/load/sufficiencey", tags=[common.HENAN_TAG], response_model=data.Response)
def zone_load_sufficiencey(
        case_id: str,
        bload_series: bool = False,
        bzone_detail: bool = False,
        bdetail: bool = False,
        area: str = "全省",
        result: data.Response = Depends(utils.response_obj)
):
    """
    获取所有分区的负荷最大缺口及总缺负荷时长;缺负荷明细;
    :param case_id: 算例id
    :param bload_series: 是否需要负荷时序
    :param bzone_detail: 全网时是否需要分区的负荷削减情况
    :param bdetail: 是否需要负荷削减的具体明细
    :param result: 返回值
    :return:
    """
    if case_id in common.case_dict:
        case = common.case_dict.get(case_id, {})
    else:
        read_resp = utils.read_case(case_id=case_id)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result
    output = case.get('output', {})
    case_info = case.get("case_info", {})
    data = data_utils.get_zone_loadinsufficiency_data(
        result_dict=output,
        bload_series=bload_series,
        bzone_detail=bzone_detail,
        bdetail=bdetail,
        area_name=area,
        area_details_dict=case_info['zone']['device_relay'],
        indicator_rlt_dict=case.get("indicator", {}),
        bpsm=True
    )
    result.data = data
    return result


@router.get("/zone/trafo/capability", tags=[common.HENAN_TAG], response_model=data.Response)
def zone_trafo_capability(
        case_id: str,
        time_no: Union[str, int],
        area: str = "全省",
        result: data.Response = Depends(utils.response_obj)
):
    """
    分区推演时用于获取分区主变受电能力和以变电站为单位的主变负载率初值
    :param case_id: 算例id
    :param time_no: 时刻
    :param area: 区域
    :param result: 返回值
    :return:
    """
    if case_id in common.case_dict:
        case = common.case_dict.get(case_id, {})
    else:
        read_resp = utils.read_case(case_id=case_id)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result
    output = case.get('output', {})
    network = case.get("network")
    case_info = case.get("case_info", {})
    config = case_info.get("config", {})
    if isinstance(time_no, str) and time_no.isdigit():
        time_no = int(time_no)
    ptdf = case.get("ptdf")
    data = data_utils.get_zone_trafo_capability_timestep(
        net=network,
        config=config,
        result_dict=output,
        timestep=time_no,
        net_ptdf=ptdf,
        area_name=area,
        area_details=case_info['zone']['device_relay']
    )
    result.data = data
    return result


@router.get("/zone/all/psm", tags=[common.HENAN_TAG], response_model=data.Response)
def zone_all_psm(case_id: str, result: data.Response = Depends(utils.response_obj)):
    """
    获取全省/所有分区的最小供电裕度/平均供电裕度, 有供电缺口时长/供电紧张时长
    :param case_id: 算例id
    :param result: 返回值
    :return:
    """
    if case_id in common.case_dict:
        case = common.case_dict.get(case_id, {})
    else:
        read_resp = utils.read_case(case_id=case_id)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result
    output = case.get('output', {})
    indicator = case.get("indicator", {})
    case_info = case.get("case_info", {})
    cal_result = case.get("cal_result", {})
    data = data_utils.get_allzone_psm_value(
        indicator_rlt_dict=indicator,
        result_dict=output,
        area_details=case_info['zone']['device_relay'],
        ana_result=cal_result['consump_rate']['zone']
    )
    result.data = data
    return result


@router.get("/curve/device/list", tags=[common.HENAN_TAG], response_model=data.Response)
def curve_device_list(case_id: str, result: data.Response = Depends(utils.response_obj)):
    """
    获取设备类型列表
    :param case_id: 算例id
    :param result: 返回值
    :return:
    """
    if case_id in common.short_case_dict:
        case = common.short_case_dict.get(case_id, {})
    else:
        read_resp = utils.read_case(case_id=case_id, is_short=2)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result
    output = case.get('output', {})
    network = case.get('network', {})
    data = ultra_time_data_utils.get_curve_device_list(net=network, result_dict=output)
    result.data = data
    return result


@router.post("/curve/device/info", tags=[common.HENAN_TAG], response_model=data.Response)
def curve_device_info(item: params.CurevDeviceInfoParams, result: data.Response = Depends(utils.response_obj)):
    """
    获取单个设备信息
    :param case_id: 算例id
    :param result: 返回值
    :return:
    """
    if item.case_id in common.short_case_dict:
        case = common.short_case_dict.get(item.case_id, {})
    else:
        read_resp = utils.read_case(case_id=item.case_id, is_short=2)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result
    output = case.get('output', {})
    data = ultra_time_data_utils.get_device_curve_info(result_dict=output, device_select_dict=item.device)
    result.data = data
    return result


@router.get("/curve/series/data", tags=[common.HENAN_TAG], response_model=data.Response)
def curve_series_data(case_id: str, key_tab: str, result: data.Response = Depends(utils.response_obj)):
    """
    :param case_id: 算例id
    :param key_tab: gen_state
    :param result: 返回值
    :return:
    """
    if case_id in common.short_case_dict:
        case = common.short_case_dict.get(case_id, {})
    else:
        read_resp = utils.read_case(case_id=case_id, is_short=2)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result
    output = case.get('output', {})
    network = case.get('network', {})
    data = data_utils.get_curve_series_data(result_dict=output, case_net=network, key_tab=key_tab)
    result.data = data
    return result


@router.get("/case/series/data", tags=[common.HENAN_TAG], response_model=data.Response)
def case_series_data(
        case_id: str, series_type: str, ele_name: str, result: data.Response = Depends(utils.response_obj)
):
    """
    :param case_id: 算例id
    :param series_type: maintenance
    :param ele_name: gen
    :param result: 返回值
    :return:
    """
    if case_id in common.short_case_dict:
        case = common.short_case_dict.get(case_id, {})
    else:
        read_resp = utils.read_case(case_id=case_id, is_short=2)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result
    network = case.get('network', {})
    case_info = case.get("case_info", {})
    config = case_info.get("config", {})
    data = data_utils.get_case_series_data(
        config=config, case_net=network, ele_name=ele_name, series_type=series_type
    )
    result.data = data
    return result


@router.get("/power/output/capacity", tags=[common.HENAN_TAG], response_model=data.Response)
def power_output_capacity(
        area: str = "全省", result: data.Response = Depends(utils.response_obj), db=Depends(utils.get_db)
):
    """
    获取电源出力数据
    :param result:
    :return:
    """
    nowtime = dt.datetime.now()
    nowdate = nowtime.date()
    stime = dt.datetime.combine(nowdate, dt.time.min)
    etime = dt.datetime.combine(nowdate, dt.time.max)
    data_dict = {}
    field_keys = db.query(PowerData.field_key).distinct(PowerData.field_key).all()
    df = None
    max_time = None
    # gen_common_all,gen_gas,gen_heat,gen_hydro,gen_self,load,load_grid,newenergy,reserve_down,
    # reserve_up,solar,solar_centralized,solar_curtailment,solar_distribute, stogen_energy_storage,
    # stogen_pump_hydro, wind,wind_centralized,wind_curtailment,wind_distribute
    for obj in field_keys:
        field_key = obj[0]
        if area == "全省":
            # 查询测点id
            dev_sql = f"""
            SELECT
            dev_id
            FROM sys_dev_id_map_tab
            WHERE area_name = "全省"
            AND field_key = "{field_key}"
            AND data_source = "sys"
            """
            if field_key in common.display_desc_quansheng_map:
                display_desc = common.display_desc_quansheng_map[field_key]
                dev_sql += f"AND display_desc = '{display_desc}'"
            dev_resp = db.execute(text(dev_sql)).fetchone()
            if not dev_resp or len(dev_resp) == 0:
                # 在查询qs关联的测点id
                dev_sql = f"""
                SELECT
                dev_id
                FROM sys_dev_id_map_tab
                WHERE area_name = "全省"
                AND field_key = "{field_key}"
                AND data_source = "qs"
                """
                if field_key in common.display_desc_quansheng_map:
                    display_desc = common.display_desc_quansheng_map[field_key]
                    dev_sql += f"AND display_desc = '{display_desc}'"
                dev_resp = db.execute(text(dev_sql)).fetchone()
                if not dev_resp or len(dev_resp) == 0:
                    continue
                else:
                    dev_id = dev_resp[0]
            else:
                dev_id = dev_resp[0]
            # 用测点id查询实时出力数据
            real_sql = f"""
            SELECT
            rdd.`value`,rdd.time,
            rdi.field_key
            FROM sys_real_day_data_tab rdd 
            LEFT JOIN sys_real_day_index_tab rdi on rdi.id = rdd.power_id
            WHERE rdd.is_delete = 0
            AND rdd.time >= "{stime}"
            AND rdd.time <= "{etime}"
            AND rdi.dev_id = "{dev_id}"
            AND rdi.field_key = "{field_key}"
            ORDER BY rdd.time ASC
            """
            real_resp = db.execute(text(real_sql)).fetchall()
            df = pd.DataFrame(real_resp, columns=['value', 'time', 'field_key']).fillna(0)
            if not df.empty:
                max_time = df['time'].max()
                data_dict[field_key] = (df['value'] * 0.1).tolist()
                data_dict["time"] = df['time']
            else:
                data_dict[field_key] = []
                data_dict["time"] = []
        elif area in ['豫西', '豫中东', '豫南', '豫北']:
            # 查询包含的城市
            city_sql = f"""
            SELECT
            city_name
            FROM sys_city_code_tab
            WHERE area_name = "{area}"
            """
            city_resp = db.execute(text(city_sql)).fetchall()
            base_df: Optional[pd.DataFrame] = None
            for obj in city_resp:
                city = obj[0]
                # 查询这个城市对应的测点id
                dev_sql = f"""
                SELECT
                dev_id
                FROM sys_dev_id_map_tab
                WHERE area_name = "{city}"
                AND field_key = "{field_key}"
                AND data_source = "sys"
                """
                dev_resp = db.execute(text(dev_sql)).fetchone()
                if not dev_resp or len(dev_resp) == 0:
                    # 在查询qs关联的测点id
                    dev_sql = f"""
                    SELECT
                    dev_id
                    FROM sys_dev_id_map_tab
                    WHERE area_name = "{city}"
                    AND field_key = "{field_key}"
                    AND data_source = "qs"
                    """
                    dev_resp = db.execute(text(dev_sql)).fetchone()
                    if not dev_resp or len(dev_resp) == 0:
                        continue
                    else:
                        dev_id = dev_resp[0]
                else:
                    dev_id = dev_resp[0]
                # 用测点id查询实时出力数据
                real_sql = f"""
                SELECT
                rdd.`value`,rdd.time,
                rdi.field_key
                FROM sys_real_day_data_tab rdd 
                LEFT JOIN sys_real_day_index_tab rdi on rdi.id = rdd.power_id
                WHERE rdd.is_delete = 0
                AND rdd.time >= "{stime}"
                AND rdd.time <= "{etime}"
                AND rdi.dev_id = "{dev_id}"
                AND rdi.field_key = "{field_key}"
                ORDER BY rdd.time ASC
                """
                real_resp = db.execute(text(real_sql)).fetchall()
                big_df = pd.DataFrame(real_resp, columns=['value', 'time', 'field_key']).fillna(0)
                if not isinstance(base_df, pd.DataFrame):
                    base_df = big_df
                else:
                    base_df = pd.concat([base_df, big_df], axis=0, ignore_index=True)
            if isinstance(base_df, pd.DataFrame) and not base_df.empty:
                df = base_df.groupby(['time', 'field_key']).sum().reset_index()
                max_time = df['time'].max()
                data_dict[field_key] = (df['value'] * 0.1).tolist()
                if len(data_dict.get("time", [])) == 0:
                    data_dict['time'] = df['time']
            else:
                data_dict[field_key] = []
                if "time" not in data_dict:
                    data_dict['time'] = []
        else:
            # 查询测点id
            dev_sql = f"""
            SELECT
            dev_id
            FROM sys_dev_id_map_tab
            WHERE area_name = "{area}"
            AND field_key = "{field_key}"
            AND data_source = "sys"
            """
            dev_resp = db.execute(text(dev_sql)).fetchone()
            if not dev_resp or len(dev_resp) == 0:
                # 在查询qs关联的测点id
                dev_sql = f"""
                SELECT
                dev_id
                FROM sys_dev_id_map_tab
                WHERE area_name = "{area}"
                AND field_key = "{field_key}"
                AND data_source = "qs"
                """
                dev_resp = db.execute(text(dev_sql)).fetchone()
                if not dev_resp or len(dev_resp) == 0:
                    continue
                else:
                    dev_id = dev_resp[0]
            else:
                dev_id = dev_resp[0]
            # 用测点id查询实时出力数据
            real_sql = f"""
            SELECT
            rdd.`value`,rdd.time,
            rdi.field_key
            FROM sys_real_day_data_tab rdd 
            LEFT JOIN sys_real_day_index_tab rdi on rdi.id = rdd.power_id
            WHERE rdd.is_delete = 0
            AND rdd.time >= "{stime}"
            AND rdd.time <= "{etime}"
            AND rdi.dev_id = "{dev_id}"
            AND rdi.field_key = "{field_key}"
            ORDER BY rdd.time ASC
            """
            real_resp = db.execute(text(real_sql)).fetchall()
            df = pd.DataFrame(real_resp, columns=['value', 'time', 'field_key']).fillna(0)
            if not df.empty:
                max_time = df['time'].max()
                data_dict[field_key] = (df['value'] * 0.1).tolist()
                data_dict['time'] = df['time']
            else:
                data_dict[field_key] = []
                data_dict['time'] = []
    if not max_time:
        max_time = utils.get_current_datetime()
    time_list = pd.date_range(start=stime, end=max_time, freq="5T")
    wind_list = data_dict.get("wind", [])
    if len(wind_list) == 0:
        # 未查询到wind数据时，使用wind_centralized + wind_distribute相加代替wind
        wind_centralized = data_dict.get("wind_centralized", [])
        wind_distribute = data_dict.get("wind_distribute", [])
        new_wind = []
        if wind_centralized and wind_distribute:
            for i in range(len(wind_centralized)):
                v = wind_centralized[i] + wind_distribute[i]
                new_wind.append(v)
        elif wind_centralized:
            data_dict['wind'] = wind_centralized
        elif wind_distribute:
            data_dict['wind'] = wind_distribute
        else:
            data_dict['wind'] = []
    solar_list = data_dict.get("solar", [])
    if len(solar_list) == 0:
        # 未查询到solar数据时，使用solar_centralized + solar_distribute相加代替solar
        solar_centralized = data_dict.get("solar_centralized", [])
        solar_distribute = data_dict.get("solar_distribute", [])
        new_solar = []
        if solar_distribute and solar_centralized:
            for i in range(len(solar_distribute)):
                v = solar_distribute[i] + solar_centralized[i]
                new_solar.append(v)
        elif solar_distribute:
            data_dict['solar'] = solar_distribute
        elif solar_centralized:
            data_dict['solar'] = solar_centralized
        else:
            data_dict['solar'] = []
    time_range = time_list.astype(str).tolist()
    df_time = pd.DataFrame(time_range, columns=['time'])
    df_time["time"] = df_time["time"].astype(str)
    df_data = pd.DataFrame({col: pd.Series(values) for col, values in data_dict.items()})
    df_data.fillna(0, inplace=True)
    df_data["time"] = df_data["time"].astype(str)
    new_df = pd.merge(df_time, df_data, on='time', how="left").fillna(0)
    for column in new_df.columns:
        if column == "time":
            continue
        data_dict[column] = new_df[column].tolist()
    data_dict["time_range"] = time_range
    data_dict['last_time'] = str(max_time)
    del data_dict['time']
    result.data = data_dict
    return result


@router.get("/power/capacity/indicator", tags=[common.HENAN_TAG], response_model=data.Response)
def power_capacity_indicator(area: str = "全省", result: data.Response = Depends(utils.response_obj)):
    """
    获取电网实时保供能力评价指标
    :param result:
    :return:
    """
    if not common.g_real_case:
        result.msg = "暂无数据"
        result.code = 1100
        return result
    zone_list = ["全省"] + list(common.g_real_case.realcase.area_ele_detail.keys())
    power_info = common.g_real_case.get_realcase_power_info(area_list=zone_list)
    data = common.g_real_case.get_realcase_indicators_dict(area_name=zone_list, power_dict=power_info)
    result.data = data
    return result


@router.get("/power/tide/map", tags=[common.HENAN_TAG], response_model=data.Response)
def power_tide_map(area: str = "全省", result: data.Response = Depends(utils.response_obj)):
    """
    获取电网实时潮流拓扑图
    :param result:
    :return:
    """
    if not common.g_real_case:
        result.msg = "暂无数据"
        result.code = 1100
        return result
    data = common.g_real_case.get_realcase_topo(vn_kv=500, area_name=area)
    result.data = data
    return result


@router.get("/power/interface/map", tags=[common.HENAN_TAG], response_model=data.Response)
def inferface_map(
    area: str = "全省", start_date: str = "2025-08-01", end_date: str = "2025-08-06", db=Depends(utils.get_db),
    result: data.Response = Depends(utils.response_obj)
):
    """
    获取电网实时断面拓扑图
    :param result:
    :return:
    """
    if not common.g_real_case:
        result.msg = "暂无数据"
        result.code = 1100
        return result
    from utils.topo_utils import get_trafo_history_max_rate, get_line_history_max_rate
    interface_data = {
        "trafo": get_trafo_history_max_rate(start_date=start_date, end_date=end_date, db=db),
        "line": get_line_history_max_rate(start_date=start_date, end_date=end_date, db=db)
    }
    data = common.g_real_case.get_realcase_interface_topo(interface_data=interface_data, area_name=area)
    result.data = data
    return result



@router.post("/power/interface/line/trafo", tags=[common.HENAN_TAG], response_model=data.Response)
def power_interface_line_trafo(
        item: params.RealInterfaceParams, result: data.Response = Depends(utils.response_obj)
):
    """
    获取电网实时断面/设备重过载列表数据
    :param result:
    :return:
    """
    if not common.g_real_case:
        result.msg = "暂无数据"
        result.code = 1100
        return result
    data_info = common.g_real_case.get_realcase_loadrate_info(
        ele_name=item.ele_name, area_name=item.area, inf_type=item.inf_type
    )
    new_detail = {"index": [], "name": [], "power": [], "limit": [], 'rate': [], "vlevel": []}

    data_scope = {
        "0": 0,
        "30": 0,
        "50": 0,
        "80": 0,
        "100": 0,
    }
    data_df = pd.DataFrame(data_info).sort_values("rate", ascending=False)
    if item.ele_name != "interface":
        if item.voltage == 1:
            data_df = data_df[data_df['v_level'] >= (220 * 0.95)]
        elif item.voltage == 2:
            data_df = data_df[data_df['v_level'] >= (500 * 0.95)]
        elif item.voltage == 3:
            data_df = data_df[data_df['v_level'].between(220 * 0.95, 220 * 1.05)]
        new_detail = data_df.to_dict(orient="list")
    else:
        new_detail = data_info

    p_rate = np.array(new_detail['rate'])
    data_scope = {
        "0": int(np.sum(p_rate < 0.3)),
        "30": int(np.sum((p_rate >= 0.3) & (p_rate < 0.5))),
        "50": int(np.sum((p_rate >= 0.5) & (p_rate < 0.8))),
        "80": int(np.sum((p_rate >= 0.8) & (p_rate < 1.))),
        "100": int(np.sum(p_rate >= 1.))
    }
    result.data = {"scope": data_scope, "detail": new_detail}
    return result


@router.get("/history/load/base_time", tags=[common.HENAN_TAG], response_model=data.Response)
def history_load_base_time(result: data.Response = Depends(utils.response_obj), db=Depends(utils.get_db)):
    """
    获取电网运行数据-负荷特征分析数据
    :param result:
    :return:
    """
    sql1 = f"""
    SELECT
    DISTINCT(data_year)
    FROM sys_load_data_tab
    """
    sql2 = f"""
    SELECT
    data_year, day_time
    FROM sys_load_data_tab
    """
    sql1_resp = db.execute(text(sql1)).fetchall()
    years = []
    for obj in sql1_resp:
        years.append(obj[0])
    sql2_resp = db.execute(text(sql2)).fetchall()
    df = pd.DataFrame(sql2_resp, columns=["year", "day"])
    df['date'] = df['year'].astype(str) + "-" + df['day']
    df['date'] = pd.to_datetime(df['date'], format='%Y-%m-%d')
    stime = np.min(df['date'])
    etime = np.max(df['date'])
    result.data = {"years": years, "stime": str(stime), "etime": str(etime)}
    return result


@router.post("/history/load/data", tags=[common.HENAN_TAG], response_model=data.Response)
def history_load_data(
        item: params.HistoryLoadDataParams,
        result: data.Response = Depends(utils.response_obj),
        db=Depends(utils.get_db)
):
    """
    获取电网运行数据-负荷特征分析数据
    :param result:
    :return:
    """
    # ========== 确定查询的时间范围 ============== #
    nowtime = dt.datetime.now()
    if item.type == "全网":
        field_key = "load"
    else:
        field_key = "load_grid"
    # 查询最近年份
    late_year_sql = f"""
    SELECT
    data_year
    FROM sys_load_data_tab
    ORDER BY data_year DESC
    LIMIT 1
    """
    late_year_resp = db.execute(text(late_year_sql)).fetchone()
    if late_year_resp and len(late_year_resp) >= 1:
        late_year = late_year_resp[0]
    if late_year < item.year:
        result.code = 1100
        result.msg = f"查询的年份 {item.year} 大于数据库中最新的年份 {late_year}，请检查数据库数据是否更新到最新！"
        return result
    late_year = min(late_year, item.year)
    if not item.stime:
        syear = late_year
        sdate = "01-01"
    else:
        syear = item.stime.year
        sdate = "-".join(str(item.stime.date()).split("-")[1:])
    if not item.etime:
        eyear = late_year
        edate = "12-31"
    else:
        eyear = item.etime.year
        edate = "-".join(str(item.etime.date()).split("-")[1:])

    if not item.scene_date:
        item.scene_date = dt.datetime.strptime(f"{late_year}-01-01", "%Y-%m-%d")
        item.freq = "Y"

    option = {
        "options": item.action,
        "freq": item.freq,
        "threshold": item.threshold,
        "time_start": item.stime,
        "time_end": item.etime,
        "hours": item.hours,
        "months": item.months,
        "confidence": item.confidence
    }
    df: Optional[pd.DataFrame] = None

    # ============== 处理 '豫西', '豫中东', '豫南', '豫北' 区域级数据 =============== #
    if item.area in ['豫西', '豫中东', '豫南', '豫北'] and item.action != "max":
        city_sql = f"""
        SELECT
        city_name
        FROM sys_city_code_tab
        WHERE area_name = "{item.area}"
        """
        base_df: Optional[pd.DataFrame] = None
        city_resp = db.execute(text(city_sql)).fetchall()
        for obj in city_resp:
            city = obj[0]
            # 查询测点id
            dev_id = ""
            dev_sql = f"""
            SELECT
            dev_id
            FROM sys_dev_id_map_tab
            WHERE field_key = '{field_key}' 
            AND area_name = '{city}'
            AND data_source = "sys"
            """
            dev_resp = db.execute(text(dev_sql)).fetchone()
            if not dev_resp or len(dev_resp) == 0:
                # 在查询qs关联的测点id
                dev_sql = f"""
                SELECT
                dev_id
                FROM sys_dev_id_map_tab
                WHERE area_name = "{city}"
                AND field_key = "{field_key}"
                AND data_source = "qs"
                """
                dev_resp = db.execute(text(dev_sql)).fetchone()
                if dev_resp and len(dev_resp) >= 1:
                    dev_id = dev_resp[0]
            else:
                dev_id = dev_resp[0]
            if dev_id:
                load_sql = f"""
                SELECT
                *
                FROM sys_load_data_tab
                WHERE area_name = "{city}"
                AND field_key = "{field_key}"
                AND dev_id = '{dev_id}'
                AND data_year >= {syear}
                AND data_year <= {eyear}
                AND day_time >= '{sdate}'
                AND day_time <= '{edate}'
                """
                load_resp = db.execute(text(load_sql)).fetchall()
                load_df = pd.DataFrame(load_resp)
                load_df = utils.process_sql_resp(load_df)
                if base_df is None:
                    base_df = load_df
                else:
                    base_df = pd.concat([base_df, load_df], axis=0, ignore_index=True)
        if isinstance(base_df, pd.DataFrame) and not base_df.empty:
            df = base_df.groupby(['时间']).sum().reset_index()

    # ================== 处理全省或地市数据 ================= #
    else:
        if item.action != "max":
            # 查询测点id
            dev_id = ""
            dev_sql = f"""
            SELECT
            dev_id
            FROM sys_dev_id_map_tab
            WHERE field_key = '{field_key}' 
            AND area_name = '{item.area}'
            AND data_source = "sys"
            """
            if field_key == "load" and item.area == "全省" and field_key in common.display_desc_quansheng_map:
                display_desc = common.display_desc_quansheng_map[field_key]
                dev_sql += f"AND display_desc = '{display_desc}'"
            dev_resp = db.execute(text(dev_sql)).fetchone()
            if not dev_resp or len(dev_resp) == 0:
                # 在查询qs关联的测点id
                dev_sql = f"""
                SELECT
                dev_id
                FROM sys_dev_id_map_tab
                WHERE area_name = "{item.area}"
                AND field_key = "{field_key}"
                AND data_source = "qs"
                """
                if field_key == "load" and item.area == "全省" and field_key in common.display_desc_quansheng_map:
                    display_desc = common.display_desc_quansheng_map[field_key]
                    dev_sql += f"AND display_desc = '{display_desc}'"
                dev_resp = db.execute(text(dev_sql)).fetchone()
                if dev_resp and len(dev_resp) >= 1:
                    dev_id = dev_resp[0]
            else:
                dev_id = dev_resp[0]
            if dev_id:
                load_sql = f"""
                SELECT
                *
                FROM sys_load_data_tab
                WHERE area_name = "{item.area}"
                AND field_key = "{field_key}"
                AND dev_id = '{dev_id}'
                AND data_year >= {syear}
                AND data_year <= {eyear}
                AND day_time >= '{sdate}'
                AND day_time <= '{edate}'
                """
                load_resp = db.execute(text(load_sql)).fetchall()
                df = pd.DataFrame(load_resp)
                df = utils.process_sql_resp(df)
    if item.action != "max" and df is None or (isinstance(df, pd.DataFrame) and df.empty):
        result.code = 1100
        result.msg = "暂无数据"
        return result
    if item.is_all == 1:
        data = {}
        actions = ["max", "peak", "most", "count", "confi"]  # 摒弃query，目前返回的数据太多
        for action in actions:
            option['options'] = action
            if action == "count":
                option['bpower_rate'] = False
                resp, max_value = hisdata_utils.get_load_series(df=df, options_dict=option)
            elif action == "confi":
                v_load, p_load, v_underP, v_load_max = hisdata_utils.get_load_series(df=df, options_dict=option)
                resp = {"v_load": v_load, "p_load": p_load, "v_underP": v_underP, "v_load_max": v_load_max}
            elif action == "max":
                if item.area in ['豫西', '豫中东', '豫南', '豫北']:
                    city_sql = f"""
                    SELECT
                    city_name
                    FROM sys_city_code_tab
                    WHERE area_name = "{item.area}"
                    """
                    base_df: Optional[pd.DataFrame] = None
                    city_resp = db.execute(text(city_sql)).fetchall()
                    for obj in city_resp:
                        city = obj[0]
                        # 查询测点id
                        dev_id = ""
                        dev_sql = f"""
                        SELECT
                        dev_id
                        FROM sys_dev_id_map_tab
                        WHERE field_key = '{field_key}' 
                        AND area_name = '{city}'
                        AND data_source = "sys"
                        """
                        dev_resp = db.execute(text(dev_sql)).fetchone()
                        if not dev_resp or len(dev_resp) == 0:
                            # 在查询qs关联的测点id
                            dev_sql = f"""
                            SELECT
                            dev_id
                            FROM sys_dev_id_map_tab
                            WHERE area_name = "{city}"
                            AND field_key = "{field_key}"
                            AND data_source = "qs"
                            """
                            dev_resp = db.execute(text(dev_sql)).fetchone()
                            if dev_resp and len(dev_resp) >= 1:
                                dev_id = dev_resp[0]
                        else:
                            dev_id = dev_resp[0]
                        if dev_id:
                            max_sql = f"""
                            SELECT
                            *
                            FROM sys_load_data_tab
                            WHERE area_name = "{city}"
                            AND field_key = "{field_key}"
                            AND dev_id = '{dev_id}'
                            AND data_year = '{late_year}'
                            """
                            max_resp = db.execute(text(max_sql)).fetchall()
                            max_df = pd.DataFrame(max_resp)
                            max_df = utils.process_sql_resp(max_df)
                            if base_df is None:
                                base_df = max_df
                            else:
                                base_df = pd.concat([base_df, max_df], axis=0, ignore_index=True)
                    if isinstance(base_df, pd.DataFrame) and not base_df.empty:
                        max_df = base_df.groupby(['时间']).sum().reset_index()
                        max_option = copy.deepcopy(option)
                        max_option ["time_start"] =  max_df['时间'].min()
                        max_option["time_end"] = max_df['时间'].max()
                        max_option = hisdata_utils.modify_option_dict(max_option, scene_date=item.scene_date, freq=item.freq)
                        resp = hisdata_utils.get_load_series(df=max_df, options_dict=max_option)
                    else:
                        resp = {}
                else:
                    # 查询测点id
                    dev_id = ""
                    dev_sql = f"""
                    SELECT
                    dev_id
                    FROM sys_dev_id_map_tab
                    WHERE field_key = '{field_key}' 
                    AND area_name = '{item.area}'
                    AND data_source = "sys"
                    """
                    if field_key == "load" and item.area == "全省" and field_key in common.display_desc_quansheng_map:
                        display_desc = common.display_desc_quansheng_map[field_key]
                        dev_sql += f"AND display_desc = '{display_desc}'"
                    dev_resp = db.execute(text(dev_sql)).fetchone()
                    if not dev_resp or len(dev_resp) == 0:
                        # 在查询qs关联的测点id
                        dev_sql = f"""
                        SELECT
                        dev_id
                        FROM sys_dev_id_map_tab
                        WHERE area_name = "{item.area}"
                        AND field_key = "{field_key}"
                        AND data_source = "qs"
                        """
                        if field_key == "load" and item.area == "全省" and field_key in common.display_desc_quansheng_map:
                            display_desc = common.display_desc_quansheng_map[field_key]
                            dev_sql += f"AND display_desc = '{display_desc}'"
                        dev_resp = db.execute(text(dev_sql)).fetchone()
                        if dev_resp and len(dev_resp) >= 1:
                            dev_id = dev_resp[0]
                    else:
                        dev_id = dev_resp[0]
                    if dev_id:
                        max_sql = f"""
                        SELECT
                        *
                        FROM sys_load_data_tab
                        WHERE area_name = "{item.area}"
                        AND field_key = "{field_key}"
                        AND dev_id = '{dev_id}'
                        AND data_year = '{late_year}'
                        """
                        max_resp = db.execute(text(max_sql)).fetchall()
                        max_df = pd.DataFrame(max_resp)
                        max_df = utils.process_sql_resp(max_df)
                        max_option = copy.deepcopy(option)
                        max_option['time_start'] = max_df['时间'].min()
                        max_option['time_end'] = max_df['时间'].max()
                        max_option = hisdata_utils.modify_option_dict(max_option, scene_date=item.scene_date,
                                                                      freq=item.freq)
                        resp = hisdata_utils.get_load_series(df=max_df, options_dict=max_option)
                    else:
                        resp = {}
            elif action == "most":
                most_option = copy.deepcopy(option)
                most_option['freq'] = "M"  # 特性分析固定统计分组为 按 月
                resp = hisdata_utils.get_load_series(df=df, options_dict=most_option)
            else:
                resp = hisdata_utils.get_load_series(df=df, options_dict=option)
            if isinstance(resp, pd.DataFrame):
                if "时间" in list(resp.columns):
                    resp.sort_values("时间", inplace=True)
                    resp["时间"] = resp['时间'].astype(str)
                for column in resp.columns:
                    if column in ["时间"]:
                        continue
                    if column.endswith("_time"):
                        resp[column] = resp[column].astype(str)
                        continue
                    resp[column] = resp[column].round(2)
                resp_dict = resp.to_dict("list")
                if action == "count":
                    resp_dict["max_value"] = max_value
                data[action] = resp_dict
            else:
                data[action] = resp
        result.data = data
        return result
    elif item.action == "count":
        option['bpower_rate'] = False
        resp, max_value = hisdata_utils.get_load_series(df=df, options_dict=option)
    elif item.action == "confi":
        v_load, p_load, v_underP, v_load_max = hisdata_utils.get_load_series(df=df, options_dict=option)
        resp = {"v_load": v_load, "p_load": p_load, "v_underP": v_underP,"max_value": v_load_max}
    elif item.action == "max":
        if item.area in ['豫西', '豫中东', '豫南', '豫北']:
            city_sql = f"""
            SELECT
            city_name
            FROM sys_city_code_tab
            WHERE area_name = "{item.area}"
            """
            base_df: Optional[pd.DataFrame] = None
            city_resp = db.execute(text(city_sql)).fetchall()
            for obj in city_resp:
                city = obj[0]
                # 查询测点id
                dev_id = ""
                dev_sql = f"""
                SELECT
                dev_id
                FROM sys_dev_id_map_tab
                WHERE field_key = '{field_key}' 
                AND area_name = '{city}'
                AND data_source = "sys"
                """
                dev_resp = db.execute(text(dev_sql)).fetchone()
                if not dev_resp or len(dev_resp) == 0:
                    # 在查询qs关联的测点id
                    dev_sql = f"""
                    SELECT
                    dev_id
                    FROM sys_dev_id_map_tab
                    WHERE area_name = "{city}"
                    AND field_key = "{field_key}"
                    AND data_source = "qs"
                    """
                    dev_resp = db.execute(text(dev_sql)).fetchone()
                    if dev_resp and len(dev_resp) >= 1:
                        dev_id = dev_resp[0]
                else:
                    dev_id = dev_resp[0]
                if dev_id:
                    max_sql = f"""
                    SELECT
                    *
                    FROM sys_load_data_tab
                    WHERE area_name = "{city}"
                    AND field_key = "{field_key}"
                    AND dev_id = '{dev_id}'
                    AND data_year == {late_year}
                    """
                    max_resp = db.execute(text(max_sql)).fetchall()
                    max_df = pd.DataFrame(max_resp)
                    max_df = utils.process_sql_resp(max_df)
                    if not isinstance(base_df, pd.DataFrame):
                        base_df = max_df
                    else:
                        base_df = pd.concat([base_df, max_df], axis=0, ignore_index=True)
            if isinstance(base_df, pd.DataFrame) and not base_df.empty:
                max_df = base_df.groupby(['时间']).sum().reset_index()
                max_option = copy.deepcopy(option)
                max_option['time_start'] = max_df['时间'].min()
                max_option['time_end'] = max_df['时间'].max()
                max_option = hisdata_utils.modify_option_dict(max_option, scene_date=item.scene_date, freq=item.freq)
                resp = hisdata_utils.get_load_series(df=max_df, options_dict=max_option)
            else:
                resp = {}
        else:
            # 查询测点id
            dev_id = ""
            dev_sql = f"""
            SELECT
            dev_id
            FROM sys_dev_id_map_tab
            WHERE field_key = '{field_key}' 
            AND area_name = '{item.area}'
            AND data_source = "sys"
            """
            if field_key == "load" and item.area == "全省" and field_key in common.display_desc_quansheng_map:
                display_desc = common.display_desc_quansheng_map[field_key]
                dev_sql += f"AND display_desc = '{display_desc}'"
            dev_resp = db.execute(text(dev_sql)).fetchone()
            if not dev_resp or len(dev_resp) == 0:
                # 在查询qs关联的测点id
                dev_sql = f"""
                SELECT
                dev_id
                FROM sys_dev_id_map_tab
                WHERE area_name = "{item.area}"
                AND field_key = "{field_key}"
                AND data_source = "qs"
                """
                if field_key == "load" and item.area == "全省" and field_key in common.display_desc_quansheng_map:
                    display_desc = common.display_desc_quansheng_map[field_key]
                    dev_sql += f"AND display_desc = '{display_desc}'"
                dev_resp = db.execute(text(dev_sql)).fetchone()
                if dev_resp and len(dev_resp) >= 1:
                    dev_id = dev_resp[0]
            else:
                dev_id = dev_resp[0]
            if dev_id:
                max_sql = f"""
                SELECT
                *
                FROM sys_load_data_tab
                WHERE area_name = "{item.area}"
                AND field_key = "{field_key}"
                AND dev_id = '{dev_id}'
                AND data_year = '{late_year}'
                """
                max_resp = db.execute(text(max_sql)).fetchall()
                max_df = pd.DataFrame(max_resp)
                max_df = utils.process_sql_resp(max_df)
                max_option = copy.deepcopy(option)
                max_option['time_start'] = max_df['时间'].min()
                max_option['time_end'] = max_df['时间'].max()
                max_option = hisdata_utils.modify_option_dict(max_option, scene_date=item.scene_date, freq=item.freq)
                resp = hisdata_utils.get_load_series(df=max_df, options_dict=max_option)
            else:
                resp = {}
    elif action == "most":
        most_option = copy.deepcopy(option)
        most_option['freq'] = "M"  # 特性分析固定统计分组为 按 月
        resp = hisdata_utils.get_load_series(df=df, options_dict=most_option)
    else:
        resp = hisdata_utils.get_load_series(df=df, options_dict=option)
    if isinstance(resp, pd.DataFrame):
        if "时间" in list(resp.columns):
            resp.sort_values("时间", inplace=True)
            resp["时间"] = resp['时间'].astype(str)
        for column in resp.columns:
            if column in ["时间"]:
                continue
            if column.endswith("_time"):
                resp[column] = resp[column].astype(str)
                continue
            resp[column] = resp[column].round(2)
        resp_dict = resp.to_dict("list")
        if item.action == "count":
            resp_dict['max_value'] = max_value
        result.data = resp_dict
    else:
        result.data = resp
    return result


@router.get("/history/scene/list", tags=[common.HENAN_TAG], response_model=data.Response)
def history_scene_list(result: data.Response = Depends(utils.response_obj), db=Depends(utils.get_db)):
    """
    获取电网运行数据-场景列表
    :param result:
    :return:
    """
    scene_dict = defaultdict(list)
    sql = f"""
    SELECT
    `name`,area_name, `year`, date_time
    FROM sys_scenes
    """
    sql_resp = db.execute(text(sql)).fetchall()
    for obj in sql_resp:
        year_val = obj[2]
        if year_val in scene_dict:
            scene_dict[year_val].append({"area_name": obj[1], "date_time": str(obj[3]), "name": obj[0]})
        else:
            scene_dict[year_val] = [{"area_name": obj[1], "date_time": str(obj[3]), "name": obj[0]}]
    result.data = {"scenes": scene_dict}
    return result


@router.get("/history/update/scenes", tags=[common.HENAN_TAG], response_model=data.Response)
def history_update_scenes(item: params.UpdateScensParams, result: data.Response = Depends(utils.response_obj)):
    """
    更新典型场景数据
    :param result:
    :return:
    """
    utils.process_scene(year=item.year)
    return result


@router.get("/history/power/year/list", tags=[common.HENAN_TAG], response_model=data.Response)
def history_power_year_list(result: data.Response = Depends(utils.response_obj), db=Depends(utils.get_db)):
    """
    获取电网运行数据-电源类型列表和年份列表
    :param result:
    :return:
    """
    sql1 = f"""
    SELECT
    DISTINCT data_year
    FROM sys_power_data_tab
    """
    sql2 = f"""
    SELECT
        DISTINCT city 
    FROM
        sys_newstation_tab 
    """
    sql3 = f"""
    SELECT 
        DISTINCT area_name
    FROM sys_city_code_tab
    """
    sql1_resp = db.execute(text(sql1)).fetchall()
    year_list = []
    for obj in sql1_resp:
        year_list.append(obj[0])
    power_list = []
    sql3_resp = db.execute(text(sql3)).fetchall()
    for obj in sql3_resp:
        power_list.append(obj[0])
    sql2_resp = db.execute(text(sql2)).fetchall()
    for obj in sql2_resp:
        power_list.append(obj[0])
    result.data = {"powers": power_list, "years": year_list}
    return result


@router.post("/history/usual/capacity/data", tags=[common.HENAN_TAG], response_model=data.Response)
def history_usual_capacity_data(
        item: params.GetCapacityDataParams,
        result: data.Response = Depends(utils.response_obj),
        db=Depends(utils.get_db)
):
    """
    获取电网运行数据-装机容量占比
    :param result:
    :return:
    """
    # 如果装机数据表中没有数据，则从厂站信息表中读取装机数据
    field_keys = list(common.field_map.keys())
    data_dict = {}
    if item.area in ['豫西', '豫中东', '豫南', '豫北']:
        query_city_sql = f"""
        SELECT city_name
        FROM sys_city_code_tab
        WHERE area_name = '{item.area}'
        """
        query_city_resp = db.execute(text(query_city_sql)).fetchall()
        citys = []
        for obj in query_city_resp:
            citys.append(obj[0])
        area_sql = f"city in {tuple(citys)}"
        area_filed_sql = f"'{item.area}', SUM(capacity)"
    else:
        area_sql = f"city = '{item.area}'"
        area_filed_sql = f"city, SUM(capacity)"
    for field_key in field_keys:
        new_key = common.field_map.get(field_key)
        if not new_key:
            continue
        if field_key in ['wind', 'solar']:
            # 风电、光伏
            if item.area == "全省":
                sql = f"""
                SELECT
                '全省', SUM(capacity)
                FROM sys_newstation_tab
                WHERE field_key = '{field_key}'
                """
            else:
                sql = f"""
                SELECT
                {area_filed_sql}
                FROM sys_newstation_tab
                WHERE field_key = '{field_key}'
                AND {area_sql}
                """
        else:
            # 非风和光
            if item.area in ['全省']:
                if field_key == "gen_heat":
                    # 如果是供热机组，则或者关系查询isheat = 1的数据
                    sql = f"""
                    SELECT
                    '全省', SUM(capacity)
                    FROM sys_station_tab
                    WHERE (field_key = '{field_key}' or isheat = 1)
                    AND station_type = "factory"
                    """
                elif field_key == "gen_coal":
                    # 如果是煤电，则或者关系查询isheat = 0的数据
                    sql = f"""
                    SELECT
                    '全省', SUM(capacity)
                    FROM sys_station_tab
                    WHERE field_key = '{field_key}' and isheat = 0
                    AND station_type = "factory"
                    """
                else:
                    sql = f"""
                    SELECT
                    '全省', SUM(capacity)
                    FROM sys_station_tab
                    WHERE field_key = '{field_key}'
                    AND station_type = "factory"
                    """
            else:
                if field_key == 'gen_heat':
                    # 如果是供热机组，则或者关系查询isheat = 1的数据
                    sql = f"""
                    SELECT
                    {area_filed_sql}
                    FROM sys_station_tab
                    WHERE (field_key = '{field_key}' or isheat = 1)
                    AND {area_sql}
                    AND station_type = "factory"
                    """
                elif field_key == "gen_coal":
                    # 如果是煤电，则或者关系查询isheat = 0的数据
                    sql = f"""
                    SELECT
                    {area_filed_sql}
                    FROM sys_station_tab
                    WHERE field_key = '{field_key}' and isheat = 0
                    AND {area_sql}
                    AND station_type = "factory"
                    """
                else:
                    sql = f"""
                    SELECT
                    {area_filed_sql}
                    FROM sys_station_tab
                    WHERE field_key = '{field_key}'
                    AND {area_sql}
                    AND station_type = "factory"
                    """
        sql_resp = db.execute(text(sql)).fetchone()
        if sql_resp and len(sql_resp) >= 1 and sql_resp[1] is not None:
            data_dict[new_key] = round(sql_resp[1] * 0.1, 2)
        else:
            data_dict[new_key] = 0
    result.data = data_dict
    return result


@router.post("/history/usual/power/data", tags=[common.HENAN_TAG], response_model=data.Response)
def history_usual_power_data(
        item: params.GetUsualPowerDataParams,
        result: data.Response = Depends(utils.response_obj),
        db=Depends(utils.get_db)
):
    """
    获取电网运行数据-发电量占比
    :param result:
    :return:
    """
    if not item.year:
        max_year_sql = f"""
            SELECT 
            DISTINCT data_year
            FROM sys_power_data_tab
            ORDER BY data_year DESC
            LIMIT 1
        """
        max_year_resp = db.execute(text(max_year_sql)).fetchone()
        if max_year_resp and len(max_year_resp) >= 1:
            item.year = max_year_resp[0]
        else:
            item.year = dt.datetime.now().year
    df = pd.DataFrame({"时间": [], "power": [], "gen_type": []})
    data_dict = {}
    args = {
        "options": "using_hours",
        "freq": "Y",
        "time_start": f"{item.year}-01-01 00:00:00",
        "time_end": f"{item.year}-12-31 23:59:59",
        "b_electric": True
    }
    if item.area in ['豫西', '豫中东', '豫南', '豫北']:
        for gen_type in common.usual_gen_all_keys_one:
            new_key = common.field_map.get(gen_type)
            if not new_key:
                continue
            dev_id, citys = utils.get_big_area_dev_id_or_citys(area=item.area, field_key=gen_type, is_citys=True)
            if dev_id:
                sql = f"""
                SELECT
                    * 
                FROM
                    sys_power_data_tab 
                WHERE
                    area_name = "{item.area}"
                    AND dev_id = "{dev_id}"
                    AND data_year = {item.year}
                """
            else:
                sql = f"""
                SELECT
                    * 
                FROM
                    sys_power_data_tab 
                WHERE
                    area_name in {tuple(citys)}
                    AND data_year = {item.year}
                """
            sql_resp = db.execute(text(sql)).fetchall()
            df = pd.DataFrame(sql_resp)
            df = utils.process_sql_resp(df, val_name="power")
            if gen_type in ["stogen_pump_hydro", "stogen_energy_storage"]:
                df.loc[df['power'] < 0, "power"] = 0
            if df.empty:
                data_dict[new_key] = 0
                continue
            df['gen_type'] = new_key
            resp = hisdata_utils.get_load_series(df=df, options_dict=args)
            if isinstance(resp, pd.DataFrame):
                data_dict[new_key] = round(resp.iloc[0]['usinghour'], 2)
            else:
                data_dict[new_key] = 0
    else:
        for gen_type in common.usual_gen_all_keys_one:
            new_key = common.field_map.get(gen_type)
            if not new_key:
                continue
            # 查询测点id
            dev_id = utils.get_dev_id(area=item.area, field_key=gen_type)
            if not dev_id:
                data_dict[new_key] = 0
                continue
            sql = f"""
            SELECT
                * 
            FROM
                sys_power_data_tab 
            WHERE
                area_name = "{item.area}"
                AND dev_id = "{dev_id}"
                AND data_year = {item.year}
            """
            sql_resp = db.execute(text(sql)).fetchall()
            df = pd.DataFrame(sql_resp)
            df = utils.process_sql_resp(df, val_name="power")
            df['gen_type'] = new_key
            if gen_type in ["stogen_pump_hydro", "stogen_energy_storage"]:
                df.loc[df['power'] < 0, "power"] = 0
            if df.empty:
                data_dict[new_key] = 0
                continue
            resp = hisdata_utils.get_load_series(df=df, options_dict=args)
            if isinstance(resp, pd.DataFrame):
                data_dict[new_key] = round(resp.iloc[0]['usinghour'], 2)
            else:
                data_dict[new_key] = 0
    result.data = data_dict
    return result


@router.get("/history/usual/area/list", tags=[common.HENAN_TAG], response_model=data.Response)
def history_usual_area_list(result: data.Response = Depends(utils.response_obj), db=Depends(utils.get_db)):
    """
    获取电网运行数据-常规机组-区域列表
    :param result:
    :return:
    """
    sql1 = f"""
    SELECT
        DISTINCT city 
    FROM
        sys_newstation_tab 
    """
    sql2 = f"""
    SELECT 
        DISTINCT area_name
    FROM sys_city_code_tab
    """
    sql1_resp = db.execute(text(sql1)).fetchall()
    area_list = []
    for obj in sql1_resp:
        area_list.append(obj[0])
    sql2_resp = db.execute(text(sql2)).fetchall()
    for obj in sql2_resp:
        area_list.append(obj[0])
    result.data = area_list
    return result


@router.post("/history/usual/output/data", tags=[common.HENAN_TAG], response_model=data.Response)
def history_usual_output_data(
        item: params.GetUsualOutputDataParams,
        result: data.Response = Depends(utils.response_obj),
        db=Depends(utils.get_db)
):
    """
    获取电网运行数据-常规机组-出力曲线图
    :param result:
    :return:
    """
    if not item.year:
        max_year_sql = f"""
            SELECT 
            DISTINCT data_year
            FROM sys_power_data_tab
            ORDER BY data_year DESC
            LIMIT 1
        """
        max_year_resp = db.execute(text(max_year_sql)).fetchone()
        if max_year_resp and len(max_year_resp) >= 1:
            item.year = max_year_resp[0]
        else:
            item.year = dt.datetime.now().year
    if isinstance(item.scene_date, dt.datetime):
        item.year = item.scene_date.year
        date = "-".join(str(item.scene_date.date()).split("-")[1:])
    else:
        date = ""
    if item.area == "全省":
        if item.gen_type == "gen_all":
            base_df: Optional[pd.DataFrame] = None
            for gen_type in common.usual_gen_all_keys_one:
                # 查询测点
                dev_id = ""
                dev_sql = f"""
                SELECT
                dev_id
                FROM sys_dev_id_map_tab
                WHERE area_name = "{item.area}"
                AND field_key = "{gen_type}"
                AND data_source = "sys"
                """
                if item.gen_type in common.display_desc_quansheng_map:
                    display_desc = common.display_desc_quansheng_map[item.gen_type]
                    dev_sql += f"AND display_desc = '{display_desc}'"
                dev_resp = db.execute(text(dev_sql)).fetchone()
                if not dev_resp or len(dev_resp) == 0:
                    # 查询测点id-qs
                    dev_sql = f"""
                    SELECT
                    dev_id
                    FROM sys_dev_id_map_tab
                    WHERE area_name = "{item.area}"
                    AND field_key = "{gen_type}"
                    AND data_source = "qs"
                    """
                    if item.gen_type in common.display_desc_quansheng_map:
                        display_desc = common.display_desc_quansheng_map[item.gen_type]
                        dev_sql += f"AND display_desc = '{display_desc}'"
                    dev_resp = db.execute(text(dev_sql)).fetchone()
                    if dev_resp and len(dev_resp) >= 1:
                        dev_id = dev_resp[0]
                else:
                    dev_id = dev_resp[0]
                if not dev_id:
                    continue
                power_sql = f"""
                SELECT
                *
                FROM sys_power_data_tab
                WHERE dev_id = "{dev_id}"
                AND data_year = {item.year}
                AND field_key = "{gen_type}"
                """
                if date:
                    power_sql += f"AND day_time = '{date}'"
                power_resp = db.execute(text(power_sql)).fetchall()
                df = pd.DataFrame(power_resp)
                df = utils.process_sql_resp(df, val_name=gen_type, is_unit=True)
                if base_df is None:
                    base_df = df
                else:
                    base_df = pd.concat([base_df, df], axis=0, ignore_index=True)
            if isinstance(base_df, pd.DataFrame) and not base_df.empty:
                df = base_df.groupby(['时间']).sum().reset_index()
        else:
            # 查询测点id
            dev_id = ""
            dev_sql = f"""
            SELECT
            dev_id
            FROM sys_dev_id_map_tab
            WHERE area_name = "{item.area}"
            AND field_key = "{item.gen_type}"
            AND data_source = "sys"
            """
            if item.gen_type in common.display_desc_quansheng_map:
                display_desc = common.display_desc_quansheng_map[item.gen_type]
                dev_sql += f"AND display_desc = '{display_desc}'"
            dev_resp = db.execute(text(dev_sql)).fetchone()
            if not dev_resp or len(dev_resp) == 0:
                # 查询测点id-qs
                dev_sql = f"""
                SELECT
                dev_id
                FROM sys_dev_id_map_tab
                WHERE area_name = "{item.area}"
                AND field_key = "{item.gen_type}"
                AND data_source = "qs"
                """
                if item.gen_type in common.display_desc_quansheng_map:
                    display_desc = common.display_desc_quansheng_map[item.gen_type]
                    dev_sql += f"AND display_desc = '{display_desc}'"
                dev_resp = db.execute(text(dev_sql)).fetchone()
                if dev_resp and len(dev_resp) >= 1:
                    dev_id = dev_resp[0]
            else:
                dev_id = dev_resp[0]
            if dev_id:
                power_sql = f"""
                SELECT
                *
                FROM sys_power_data_tab
                WHERE dev_id = "{dev_id}"
                AND data_year = {item.year}
                """
                if date:
                    power_sql += f"AND day_time = '{date}'"
                power_resp = db.execute(text(power_sql)).fetchall()
                df = pd.DataFrame(power_resp)
                df = utils.process_sql_resp(df, val_name=item.gen_type, is_unit=True)
    elif item.area in ['豫西', '豫中东', '豫南', '豫北']:
        city_sql = f"""
        SELECT city_name
        FROM sys_city_code_tab
        WHERE area_name = '{item.area}'
        """
        city_resp = db.execute(text(city_sql)).fetchall()
        base_df: Optional[pd.DataFrame] = None
        if item.gen_type == "gen_all":
            for gen_type in common.usual_gen_all_keys_two:
                for obj in city_resp:
                    city = obj[0]
                    dev_id = ""
                    # 查询测点id
                    dev_id = ""
                    dev_sql = f"""
                    SELECT
                    dev_id
                    FROM sys_dev_id_map_tab
                    WHERE area_name = "{city}"
                    AND field_key = "{gen_type}"
                    AND data_source = "sys"
                    """
                    dev_resp = db.execute(text(dev_sql)).fetchone()
                    if not dev_resp or len(dev_resp) == 0:
                        # 查询测点id-qs
                        dev_sql = f"""
                        SELECT
                        dev_id
                        FROM sys_dev_id_map_tab
                        WHERE area_name = "{city}"
                        AND field_key = "{gen_type}"
                        AND data_source = "qs"
                        """
                        dev_resp = db.execute(text(dev_sql)).fetchone()
                        if dev_resp and len(dev_resp) >= 1:
                            dev_id = dev_resp[0]
                    else:
                        dev_id = dev_resp[0]
                    if not dev_id:
                        continue
                    power_sql = f"""
                    SELECT
                    *
                    FROM sys_power_data_tab
                    WHERE dev_id = "{dev_id}"
                    AND data_year = {item.year}
                    AND field_key = "{gen_type}"
                    """
                    if date:
                        power_sql += f"AND day_time = '{date}'"
                    power_resp = db.execute(text(power_sql)).fetchall()
                    df = pd.DataFrame(power_resp)
                    df = utils.process_sql_resp(df, val_name=gen_type, is_unit=True)
                    if base_df is None:
                        base_df = df
                    else:
                        base_df = pd.concat([base_df, df], axis=0, ignore_index=True)
            if isinstance(base_df, pd.DataFrame) and not base_df.empty:
                df = base_df.groupby(['时间']).sum().reset_index()
        else:
            for obj in city_resp:
                city = obj[0]
                dev_id = ""
                # 查询测点id
                dev_id = ""
                dev_sql = f"""
                SELECT
                dev_id
                FROM sys_dev_id_map_tab
                WHERE area_name = "{city}"
                AND field_key = "{item.gen_type}"
                AND data_source = "sys"
                """
                dev_resp = db.execute(text(dev_sql)).fetchone()
                if not dev_resp or len(dev_resp) == 0:
                    # 查询测点id-qs
                    dev_sql = f"""
                    SELECT
                    dev_id
                    FROM sys_dev_id_map_tab
                    WHERE area_name = "{city}"
                    AND field_key = "{item.gen_type}"
                    AND data_source = "qs"
                    """
                    dev_resp = db.execute(text(dev_sql)).fetchone()
                    if dev_resp and len(dev_resp) >= 1:
                        dev_id = dev_resp[0]
                else:
                    dev_id = dev_resp[0]
                if not dev_id:
                    continue
                power_sql = f"""
                SELECT
                *
                FROM sys_power_data_tab
                WHERE dev_id = "{dev_id}"
                AND data_year = {item.year}
                AND field_key = "{item.gen_type}"
                """
                if date:
                    power_sql += f"AND day_time = '{date}'"
                power_resp = db.execute(text(power_sql)).fetchall()
                df = pd.DataFrame(power_resp)
                df = utils.process_sql_resp(df, val_name=item.gen_type, is_unit=True)
                if base_df is None:
                    base_df = df
                else:
                    base_df = pd.concat([base_df, df], axis=0, ignore_index=True)
            if isinstance(base_df, pd.DataFrame) and not base_df.empty:
                df = base_df.groupby(['时间']).sum().reset_index()
    else:
        if item.gen_type == "gen_all":
            base_df: Optional[pd.DataFrame] = None
            for gen_type in common.usual_gen_all_keys_two:
                # 查询测点
                dev_id = ""
                dev_sql = f"""
                SELECT
                dev_id
                FROM sys_dev_id_map_tab
                WHERE area_name = "{item.area}"
                AND field_key = "{gen_type}"
                AND data_source = "sys"
                """
                dev_resp = db.execute(text(dev_sql)).fetchone()
                if not dev_resp or len(dev_resp) == 0:
                    # 查询测点id-qs
                    dev_sql = f"""
                    SELECT
                    dev_id
                    FROM sys_dev_id_map_tab
                    WHERE area_name = "{item.area}"
                    AND field_key = "{item.gen_type}"
                    AND data_source = "qs"
                    """
                    dev_resp = db.execute(text(dev_sql)).fetchone()
                    if dev_resp and len(dev_resp) >= 1:
                        dev_id = dev_resp[0]
                else:
                    dev_id = dev_resp[0]
                if not dev_id:
                    continue
                power_sql = f"""
                SELECT
                *
                FROM sys_power_data_tab
                WHERE dev_id = "{dev_id}"
                AND data_year = {item.year}
                AND field_key = "{gen_type}"
                """
                if date:
                    power_sql += f"AND day_time = '{date}'"
                power_resp = db.execute(text(power_sql)).fetchall()
                df = pd.DataFrame(power_resp)
                df = utils.process_sql_resp(df, val_name=gen_type, is_unit=True)
                if base_df is None:
                    base_df = df
                else:
                    base_df = pd.concat([base_df, df], axis=0, ignore_index=True)
            if isinstance(base_df, pd.DataFrame) and not base_df.empty:
                df = base_df.groupby(['时间']).sum().reset_index()
        else:
            # 查询测点id
            dev_id = ""
            dev_sql = f"""
            SELECT
            dev_id
            FROM sys_dev_id_map_tab
            WHERE area_name = "{item.area}"
            AND field_key = "{item.gen_type}"
            AND data_source = "sys"
            """
            dev_resp = db.execute(text(dev_sql)).fetchone()
            if not dev_resp or len(dev_resp) == 0:
                # 查询测点id-qs
                dev_sql = f"""
                SELECT
                dev_id
                FROM sys_dev_id_map_tab
                WHERE area_name = "{item.area}"
                AND field_key = "{item.gen_type}"
                AND data_source = "qs"
                """
                dev_resp = db.execute(text(dev_sql)).fetchone()
                if dev_resp and len(dev_resp) >= 1:
                    dev_id = dev_resp[0]
            else:
                dev_id = dev_resp[0]
            if dev_id:
                power_sql = f"""
                SELECT
                *
                FROM sys_power_data_tab
                WHERE dev_id = "{dev_id}"
                AND data_year = {item.year}
                AND field_key = "{item.gen_type}"
                """
                if date:
                    power_sql += f"AND day_time = '{date}'"
                power_resp = db.execute(text(power_sql)).fetchall()
                df = pd.DataFrame(power_resp)
                df = utils.process_sql_resp(df, val_name=item.gen_type, is_unit=True)
    data_dict = {}
    for column in df.columns:
        if column == "时间":
            data_dict[column] = df[column].astype(str).tolist()
        else:
            data_dict[column] = df[column].tolist()
    result.data = data_dict
    return result


@router.post("/history/usual/output/distribute", tags=[common.HENAN_TAG], response_model=data.Response)
def history_usual_output_distribute(
        item: params.GetUsualOutputDistributeParams,
        result: data.Response = Depends(utils.response_obj),
        db=Depends(utils.get_db)
):
    """
    获取电网运行数据-常规机组-出力统计分布图
    :param result:
    :return:
    """
    if not item.year:
        max_year_sql = f"""
            SELECT
            spower.data_year 
            FROM sys_power_data_tab AS spower
            WHERE
            spower.field_key = "{item.gen_type}" 
            AND spower.area_name = "{item.area}" 
            ORDER BY spower.data_year DESC
            LIMIT 1
        """
        max_year_resp = db.execute(text(max_year_sql)).fetchone()
        if max_year_resp and len(max_year_resp) >= 1:
            item.year = max_year_resp[0]
        else:
            item.year = dt.datetime.now().year
    df: Optional[pd.DataFrame] = None
    if item.mode == 1:
        # 查询装机
        wind_capacity = utils.get_capacity_area(area=item.area, field_key="wind")
        solar_capacity = utils.get_capacity_area(area=item.area, field_key="solar")
        capacity = wind_capacity + solar_capacity
        new_result = history_service.get_new_energy_power(item=item)
        if isinstance(new_result, pd.DataFrame):
            df = new_result
            df.rename(columns={"value": "power"}, inplace=True)
        else:
            return new_result
    else:
        capacity = utils.get_capacity_area(area=item.area, field_key=item.gen_type)
        # 查询："gen_coal", "gen_gas", "gen_heat", "gen_hydro", "stogen_pump_hydro"
        if item.mode == 3:
            # 电源特性
            all_keys = common.usual_gen_all_keys_one
        else:
            # 1:新能源 2: 常规机组，3: 电源特性
            all_keys = common.usual_gen_all_keys_two
        base_df: Optional[pd.DataFrame] = None
        for gen_type in all_keys:
            if item.gen_type != "gen_all" and gen_type != item.gen_type:
                continue
            if item.area in ['豫西', '豫中东', '豫南', '豫北']:
                dev_id, citys = utils.get_big_area_dev_id_or_citys(
                    area=item.area, field_key=gen_type, is_citys=True
                )
                if dev_id:
                    # 查询出力数据
                    power_sql = f"""
                    SELECT
                    *
                    FROM sys_power_data_tab spower
                    WHERE spower.dev_id = '{dev_id}'
                    AND spower.area_name = "{item.area}"
                    AND spower.data_year = {item.year}
                    """
                    power_resp = db.execute(text(power_sql)).fetchall()
                    df_power = pd.DataFrame(power_resp)
                    df_power = utils.process_sql_resp(df_power)
                    if base_df is None:
                        base_df = df_power
                    else:
                        base_df = pd.concat([base_df, df_power], axis=0, ignore_index=True)
                else:
                    for city in citys:
                        dev_id = utils.get_dev_id(area=city, field_key=gen_type)
                        if not dev_id:
                            continue
                        # 查询出力数据
                        power_sql = f"""
                        SELECT
                        *
                        FROM sys_power_data_tab spower
                        WHERE spower.dev_id = '{dev_id}'
                        AND spower.area_name = "{city}"
                        AND spower.data_year = {item.year}
                        """
                        power_resp = db.execute(text(power_sql)).fetchall()
                        df_power = pd.DataFrame(power_resp)
                        df_power = utils.process_sql_resp(df_power)
                        if base_df is None:
                            base_df = df_power
                        else:
                            base_df = pd.concat([base_df, df_power], axis=0, ignore_index=True)
            else:
                # 查询测点id
                dev_id = utils.get_dev_id(area=item.area, field_key=gen_type)
                if not dev_id:
                    continue
                # 查询出力数据
                power_sql = f"""
                SELECT
                *
                FROM sys_power_data_tab spower
                WHERE spower.dev_id = '{dev_id}'
                AND spower.data_year = {item.year}
                """
                power_resp = db.execute(text(power_sql)).fetchall()
                df_power = pd.DataFrame(power_resp)
                df_power = utils.process_sql_resp(df_power, val_name="power")
                if base_df is None:
                    base_df = df_power
                else:
                    base_df = pd.concat([base_df, df_power], axis=0, ignore_index=True)
        if isinstance(base_df, pd.DataFrame) and not base_df.empty:
            df = base_df.groupby("时间").sum().reset_index()
    if not isinstance(df, pd.DataFrame) or df.empty:
        stime = f"{item.year}-01-01 00:00:00"
        etime = utils.get_current_datetime()
        time_list = pd.date_range(start=stime, end=etime, freq="5T").astype(str).tolist()
        power_list = [0] * len(time_list)
        df = pd.DataFrame({"时间": time_list, "power": power_list})
    if capacity:
        df['capacity'] = capacity
    option = {
        "options": "count",
        "months": item.months,
        "hours": item.hours,
        "confidence": item.confidence,
        "time_start": df['时间'].min(),
        "time_end": df['时间'].max(),
        "freq_step": "5T"
    }
    resp, max_value = hisdata_utils.get_load_series(df=df, options_dict=option)
    if isinstance(resp, pd.DataFrame):
        new_dict = {}
        for index, obj in resp.iterrows():
            if np.isnan(obj[0]):
                new_dict[index] = 0
            else:
                new_dict[index] = round(float(obj[0]), 2)
        result.data = {"outputs": new_dict, "max_value": max_value}
    else:
        result.data = resp
    return result


@router.post("/history/usual/map", tags=[common.HENAN_TAG], response_model=data.Response)
def history_usual_map(
        item: params.GetUsualMapParams, result: data.Response = Depends(utils.response_obj), db=Depends(utils.get_db)
):
    """
    获取电网运行数据-常规机组-拓扑图
    :param result:
    :return:
    """
    data_dict = {}
    # 查询四个特殊的大区所包含的城市
    city_sql = f"""
    SELECT
    city_name, area_name
    FROM sys_city_code_tab
    WHERE area_name != "全省"
    """
    city_dict = defaultdict(list)
    citys = db.execute(text(city_sql)).fetchall()
    for obj in citys:
        city_dict[obj[1]].append(obj[0])
    if item.mode in [1, 3]:
        # 1: 新能源，3:电源特性，2:常规机组
        # 新能源， 风、光只查询统调数据
        df: Optional[pd.DataFrame] = None
        for field_key in ["wind", "solar", "stogen_energy_storage"]:
            sql = f"""
            SELECT
            city, sum(capacity)
            FROM sys_newstation_tab
            WHERE
            field_key = "{field_key}"
            GROUP BY city
            """
            quansheng_sql = f"""
            SELECT
            '全省', sum(capacity)
            FROM sys_newstation_tab
            WHERE
            field_key = "{field_key}"
            """
            quansheng_sql_resp = db.execute(text(quansheng_sql)).fetchall()
            # 查询四个特殊的大区
            big_df: Optional[pd.DataFrame] = None
            for big_area, city_list in city_dict.items():
                big_area_sql = f"""
                    SELECT
                    '{big_area}', sum(capacity)
                    FROM sys_newstation_tab
                    WHERE
                    field_key = "{field_key}"
                    AND city in {tuple(city_list)}
                """
                big_sql_resp = db.execute(text(big_area_sql)).fetchall()
                big_df1 = pd.DataFrame(big_sql_resp, columns=["area", field_key])
                if big_df is None:
                    big_df = big_df1
                else:
                    big_df = pd.concat([big_df, big_df1], axis=0).fillna(0)
                big_df = big_df.groupby('area').sum().reset_index()
            sql_resp = db.execute(text(sql)).fetchall()
            sql_resp += quansheng_sql_resp
            df1 = pd.DataFrame(sql_resp, columns=['area', field_key])
            if df is None:
                df = df1
            else:
                df = pd.merge(df, df1, on="area", how="outer").fillna(0)
            if df is not None and big_df is not None:
                df = pd.concat([df, big_df], axis=0, ignore_index=True).groupby('area').sum().reset_index()
        for _, row in df.iterrows():
            data_dict[row['area']] = {
                "风电": round(row['wind'] * 0.1, 2),
                "光伏": round(row['solar'] * 0.1, 2),
                "储能": round(row['stogen_energy_storage'] * 0.1, 2)
            }
        if item.mode == 1:
            # 只查新能源，则直接返回新能源的数据
            result.data = data_dict
            return result
    if item.mode in [2, 3]:
        # 常规机组的地图中鼠标悬浮数据
        df: Optional[pd.DataFrame] = None
        for gen_type in common.usual_gen_all_keys_two:
            if gen_type == 'gen_coal':
                sql = f"""
                SELECT
                city, SUM(capacity)
                FROM sys_station_tab
                WHERE
                field_key = "{gen_type}"
                AND isheat = 0
                AND station_type = "factory"
                AND city != '全省'
                GROUP BY city
                """
            elif gen_type == 'gen_heat':
                sql = f"""
                SELECT
                city, SUM(capacity)
                FROM sys_station_tab
                WHERE
                (field_key = "{gen_type}"
                OR isheat = 1)
                AND station_type = "factory"
                AND city != '全省'
                GROUP BY city
                """
            else:
                sql = f"""
                SELECT
                city, SUM(capacity)
                FROM sys_station_tab
                WHERE
                field_key = "{gen_type}"
                AND station_type = "factory"
                AND city != '全省'
                GROUP BY city
                """
            sql_resp = db.execute(text(sql)).fetchall()
            df1 = pd.DataFrame(sql_resp, columns=['area', gen_type]).fillna(0)
            if df is None:
                df = df1
            else:
                df = pd.merge(df, df1, on="area", how="outer").fillna(0)

            # 查询全省的装机
            if gen_type == 'gen_coal':
                quansheng_sql = f"""
                SELECT
                '全省', SUM(capacity)
                FROM sys_station_tab
                WHERE
                field_key = "{gen_type}"
                AND isheat = 0
                AND station_type = "factory"
                """
            elif gen_type == 'gen_heat':
                quansheng_sql = f"""
                SELECT
                '全省', SUM(capacity)
                FROM sys_station_tab
                WHERE
                (field_key = "{gen_type}"
                OR isheat = 1)
                AND station_type = "factory"
                """
            else:
                quansheng_sql = f"""
                SELECT
                '全省', SUM(capacity)
                FROM sys_station_tab
                WHERE
                field_key = "{gen_type}"
                AND station_type = "factory"
                """
            quansheng_sql_resp = db.execute(text(quansheng_sql)).fetchall()
            df1_quan = pd.DataFrame(quansheng_sql_resp, columns=['area', gen_type]).fillna(0)
            if df is None:
                df = df1_quan
            else:
                df = pd.concat([df, df1_quan], axis=0, ignore_index=True).fillna(0)

            # 查询四个特殊的大区
            big_df: Optional[pd.DataFrame] = None
            for big_area, city_list in city_dict.items():
                if gen_type == "gen_coal":
                    big_area_sql = f"""
                    SELECT
                    '{big_area}', sum(capacity)
                    FROM sys_station_tab
                    WHERE
                    field_key = "{gen_type}"
                    AND isheat = 0
                    AND city in {tuple(city_list)}
                    """
                elif gen_type == "gen_heat":
                    big_area_sql = f"""
                    SELECT
                    '{big_area}', sum(capacity)
                    FROM sys_station_tab
                    WHERE
                    (field_key = "{gen_type}"
                    OR isheat = 1)
                    AND city in {tuple(city_list)}
                    """
                else:
                    big_area_sql = f"""
                    SELECT
                    '{big_area}', sum(capacity)
                    FROM sys_station_tab
                    WHERE
                    field_key = "{gen_type}"
                    AND city in {tuple(city_list)}
                    """
                big_sql_resp = db.execute(text(big_area_sql)).fetchall()
                big_df1 = pd.DataFrame(big_sql_resp, columns=["area", gen_type])
                if big_df is None:
                    big_df = big_df1
                elif big_df1.empty:
                    big_df = pd.concat([big_df, big_df1], axis=0).fillna(0)
                else:
                    big_df = pd.concat([big_df, big_df1], axis=0).fillna(0)
                big_df = big_df.groupby('area')[gen_type].sum().reset_index()
            if df is not None and big_df is not None:
                df = pd.concat([df, big_df], axis=0, ignore_index=True).groupby('area').sum().reset_index()
        for _, row in df.iterrows():
            if data_dict.get(row['area']):
                data_dict[row['area']].update(
                    {
                        "煤电": round(row['gen_coal'] * 0.1, 2),
                        "燃气": round(row['gen_gas'] * 0.1, 2),
                        "供热": round(row['gen_heat'] * 0.1, 2),
                        "水电": round(row['gen_hydro'] * 0.1, 2),
                        "抽蓄": round(row['stogen_pump_hydro'] * 0.1, 2)
                    }
                )
            else:
                data_dict[row['area']] = {
                    "煤电": round(row['gen_coal'] * 0.1, 2),
                    "燃气": round(row['gen_gas'] * 0.1, 2),
                    "供热": round(row['gen_heat'] * 0.1, 2),
                    "水电": round(row['gen_hydro'] * 0.1, 2),
                    "抽蓄": round(row['stogen_pump_hydro'] * 0.1, 2)
                }
        if item.mode == 2:
            result.data = data_dict
            return result
    result.data = data_dict
    return result


@router.post("/history/usual/hour/rate", tags=[common.HENAN_TAG], response_model=data.Response)
def history_usual_hour_rate(
        item: params.GetUsualHouRateParams,
        result: data.Response = Depends(utils.response_obj),
        db=Depends(utils.get_db)
):
    """
    获取电网运行数据-常规机组-利用小时统计图
    :param result:
    :return:
    """
    # 查询出力数据
    if not item.year:
        max_year_sql = f"""
            SELECT
            spower.data_year 
            FROM sys_power_data_tab AS spower
            WHERE
            spower.field_key = "{item.gen_type}" 
            AND spower.area_name = "{item.area}" 
            ORDER BY spower.data_year ASC
            LIMIT 1
        """
        max_year_resp = db.execute(text(max_year_sql)).fetchone()
        if max_year_resp and len(max_year_resp) >= 1:
            item.year = max_year_resp[0]
        else:
            item.year = dt.datetime.now().year
    df = pd.DataFrame({"power": [], "capacity": []})
    if item.area in ['豫西', '豫中东', '豫南', '豫北']:
        if item.gen_type == "gen_all":
            base_df: Optional[pd.DataFrame] = None
            for gen_type in common.usual_gen_all_keys_three:
                dev_id, citys = utils.get_big_area_dev_id_or_citys(
                    area=item.area, field_key=gen_type, is_citys=True
                )
                if dev_id:
                    sql_power = f"""
                    SELECT
                            * 
                    FROM sys_power_data_tab AS spower
                    WHERE
                    spower.field_key = "{item.gen_type}" 
                    AND spower.area_name = "{item.area}"
                    AND spower.dev_id = "{dev_id}"
                    AND spower.data_year = {item.year}
                    """
                    sql_power_resp = db.execute(text(sql_power)).fetchall()
                    df_power = pd.DataFrame(sql_power_resp)
                    df_power = utils.process_sql_resp(df_power, val_name="power")
                    # 查询常规机组中类型装机数据
                    sql_capacity = f"""
                    SELECT
                    sum(capacity)
                    FROM sys_station_tab
                    WHERE
                    city in {tuple(citys)}
                    AND field_key = "{item.gen_type}"
                    """
                    sql_capacity_resp = db.execute(text(sql_capacity)).fetchone()
                    if sql_capacity_resp and len(sql_capacity_resp) >= 1:
                        capacity = sql_capacity_resp[0]
                    else:
                        capacity = 0
                    df_power['capacity'] = capacity
                    if base_df is None:
                        base_df = df_power
                    else:
                        base_df = pd.concat([base_df, df_power], axis=0, ignore_index=True)
                elif citys:
                    sql_power = f"""
                    SELECT
                            * 
                    FROM sys_power_data_tab AS spower
                    WHERE
                    spower.field_key = "{item.gen_type}" 
                    AND spower.area_name in {tuple(citys)}
                    AND spower.data_year = {item.year}
                    """
                    sql_power_resp = db.execute(text(sql_power)).fetchall()
                    df_power = pd.DataFrame(sql_power_resp)
                    df_power = utils.process_sql_resp(df_power, val_name="power")
                    # 查询常规机组中类型装机数据
                    sql_capacity = f"""
                    SELECT
                    sum(capacity)
                    FROM sys_station_tab
                    WHERE
                    city in {tuple(citys)}
                    AND field_key = "{item.gen_type}"
                    """
                    sql_capacity_resp = db.execute(text(sql_capacity)).fetchone()
                    if sql_capacity_resp and len(sql_capacity_resp) >= 1:
                        capacity = sql_capacity_resp[0]
                    else:
                        capacity = 0
                    df_power['capacity'] = capacity
                    if base_df is None:
                        base_df = df_power
                    else:
                        base_df = pd.concat([base_df, df_power], axis=0, ignore_index=True)
            if isinstance(base_df, pd.DataFrame) and not base_df.empty:
                df = base_df.groupby("时间").sum().reset_index()
        elif item.gen_type in ['wind', 'solar', 'stogen_energy_storage', '新能源']:
            if item.gen_type == "新能源":
                base_df: Optional[pd.DataFrame] = None
                for gen_type in ['wind', 'solar']:
                    dev_id, citys = utils.get_big_area_dev_id_or_citys(
                        area=item.area, field_key=gen_type, is_citys=True
                    )
                    if dev_id:
                        sql_power = f"""
                        SELECT
                                * 
                        FROM sys_power_data_tab AS spower
                        WHERE
                        spower.field_key = "{gen_type}" 
                        AND spower.area_name = "{item.area}"
                        AND spower.dev_id = "{dev_id}"
                        AND spower.data_year = {item.year}
                        """
                        sql_power_resp = db.execute(text(sql_power)).fetchall()
                        df_power = pd.DataFrame(sql_power_resp)
                        df_power = utils.process_sql_resp(df_power, val_name="power")
                        # 查询新能源装机数据
                        sql_capacity = f"""
                        SELECT
                        sum(capacity)
                        FROM sys_newstation_tab
                        WHERE
                        city in {tuple(citys)}
                        AND field_key = "{gen_type}"
                        """
                        sql_capacity_resp = db.execute(text(sql_capacity)).fetchone()
                        if sql_capacity_resp and len(sql_capacity_resp) >= 1:
                            capacity = sql_capacity_resp[0]
                        else:
                            capacity = 0
                        df_power['capacity'] = capacity
                        if base_df is None:
                            base_df = df_power
                        else:
                            base_df = pd.concat([base_df, df_power], axis=0, ignore_index=True)
                    elif citys:
                        sql_power = f"""
                        SELECT
                                * 
                        FROM sys_power_data_tab AS spower
                        WHERE
                        spower.field_key = "{gen_type}" 
                        AND spower.area_name in {tuple(citys)}
                        AND spower.data_year = {item.year}
                        """
                        sql_power_resp = db.execute(text(sql_power)).fetchall()
                        df_power = pd.DataFrame(sql_power_resp)
                        df_power = utils.process_sql_resp(df_power, val_name="power")
                        # 查询新能源装机数据
                        sql_capacity = f"""
                        SELECT
                        sum(capacity)
                        FROM sys_newstation_tab
                        WHERE
                        city in {tuple(citys)}
                        AND field_key = "{gen_type}"
                        """
                        sql_capacity_resp = db.execute(text(sql_capacity)).fetchone()
                        if sql_capacity_resp and len(sql_capacity_resp) >= 1:
                            capacity = sql_capacity_resp[0]
                        else:
                            capacity = 0
                        df_power['capacity'] = capacity
                        if base_df is None:
                            base_df = df_power
                        else:
                            base_df = pd.concat([base_df, df_power], axis=0, ignore_index=True)
                if isinstance(base_df, pd.DataFrame) and not base_df.empty:
                    df = base_df.groupby("时间").sum().reset_index()
            else:
                dev_id, citys = utils.get_big_area_dev_id_or_citys(
                    area=item.area, field_key=item.gen_type, is_citys=True
                )
                if dev_id:
                    sql_power = f"""
                    SELECT
                            * 
                    FROM sys_power_data_tab AS spower
                    WHERE
                    spower.field_key = "{item.gen_type}" 
                    AND spower.area_name = "{item.area}"
                    AND spower.dev_id = "{dev_id}"
                    AND spower.data_year = {item.year}
                    """
                    sql_power_resp = db.execute(text(sql_power)).fetchall()
                    df = pd.DataFrame(sql_power_resp)
                    df = utils.process_sql_resp(df, val_name="power")
                    # 查询新能源装机数据
                    sql_capacity = f"""
                    SELECT
                    sum(capacity)
                    FROM sys_newstation_tab
                    WHERE
                    city in {tuple(citys)}
                    AND field_key = "{item.gen_type}"
                    """
                    sql_capacity_resp = db.execute(text(sql_capacity)).fetchone()
                    if sql_capacity_resp and len(sql_capacity_resp) >= 1:
                        capacity = sql_capacity_resp[0]
                    else:
                        capacity = 0
                    df['capacity'] = capacity
                elif citys:
                    sql_power = f"""
                    SELECT
                            * 
                    FROM sys_power_data_tab AS spower
                    WHERE
                    spower.field_key = "{item.gen_type}" 
                    AND spower.area_name in {tuple(citys)}
                    AND spower.data_year = {item.year}
                    """
                    sql_power_resp = db.execute(text(sql_power)).fetchall()
                    df = pd.DataFrame(sql_power_resp)
                    df = utils.process_sql_resp(df, val_name="power")
                    # 查询新能源装机数据
                    sql_capacity = f"""
                    SELECT
                    sum(capacity)
                    FROM sys_newstation_tab
                    WHERE
                    city in {tuple(citys)}
                    AND field_key = "{item.gen_type}"
                    """
                    sql_capacity_resp = db.execute(text(sql_capacity)).fetchone()
                    if sql_capacity_resp and len(sql_capacity_resp) >= 1:
                        capacity = sql_capacity_resp[0]
                    else:
                        capacity = 0
                    df['capacity'] = capacity
        else:
            dev_id, citys = utils.get_big_area_dev_id_or_citys(
                area=item.area, field_key=item.gen_type, is_citys=True
            )
            if dev_id:
                sql_power = f"""
                SELECT
                        * 
                FROM sys_power_data_tab AS spower
                WHERE
                spower.field_key = "{item.gen_type}" 
                AND spower.area_name = "{item.area}"
                AND spower.dev_id = "{dev_id}"
                AND spower.data_year = {item.year}
                """
                sql_power_resp = db.execute(text(sql_power)).fetchall()
                df = pd.DataFrame(sql_power_resp)
                df = utils.process_sql_resp(df, val_name="power")
                # 查询常规机组中类型装机数据
                sql_capacity = f"""
                SELECT
                sum(capacity)
                FROM sys_station_tab
                WHERE
                city in {tuple(citys)}
                AND field_key = "{item.gen_type}"
                """
                sql_capacity_resp = db.execute(text(sql_capacity)).fetchone()
                if sql_capacity_resp and len(sql_capacity_resp) >= 1:
                    capacity = sql_capacity_resp[0]
                else:
                    capacity = 0
                df['capacity'] = capacity
            elif citys:
                sql_power = f"""
                SELECT
                        * 
                FROM sys_power_data_tab AS spower
                WHERE
                spower.field_key = "{item.gen_type}" 
                AND spower.area_name in {tuple(citys)}
                AND spower.data_year = {item.year}
                """
                sql_power_resp = db.execute(text(sql_power)).fetchall()
                df = pd.DataFrame(sql_power_resp)
                df = utils.process_sql_resp(df, val_name="power")
                # 查询常规机组中类型装机数据
                sql_capacity = f"""
                SELECT
                sum(capacity)
                FROM sys_station_tab
                WHERE
                city in {tuple(citys)}
                AND field_key = "{item.gen_type}"
                """
                sql_capacity_resp = db.execute(text(sql_capacity)).fetchone()
                if sql_capacity_resp and len(sql_capacity_resp) >= 1:
                    capacity = sql_capacity_resp[0]
                else:
                    capacity = 0
                df['capacity'] = capacity

    else:
        if item.gen_type == "gen_all":
            # 煤电、热电、燃气、水电、抽蓄
            base_df: Optional[pd.DataFrame] = None
            for gen_type in common.usual_gen_all_keys_three:
                # 查询测点id
                dev_id = utils.get_dev_id(area=item.area, field_key=gen_type)
                if not dev_id:
                    continue
                sql_power = f"""
                SELECT
                        * 
                FROM sys_power_data_tab AS spower
                WHERE
                spower.field_key = "{gen_type}" 
                AND spower.area_name = "{item.area}"
                AND spower.dev_id = "{dev_id}"
                AND spower.data_year = {item.year}
                """
                sql_power_resp = db.execute(text(sql_power)).fetchall()
                df_power = pd.DataFrame(sql_power_resp)
                df_power = utils.process_sql_resp(df_power, val_name="power")
                # 查询常规机组中类型装机数据
                sql_capacity = f"""
                SELECT
                sum(capacity)
                FROM sys_station_tab
                WHERE
                city = "{item.area}"
                AND field_key = "{gen_type}"
                """
                sql_capacity_resp = db.execute(text(sql_capacity)).fetchone()
                if sql_capacity_resp and len(sql_capacity_resp) >= 1:
                    capacity = sql_capacity_resp[0]
                else:
                    capacity = 0
                df_power['capacity'] = capacity
                if base_df is None:
                    base_df = df_power
                else:
                    base_df = pd.concat([base_df, df_power], axis=0, ignore_index=True)
            if isinstance(base_df, pd.DataFrame) and not base_df.empty:
                df = base_df.groupby("时间").sum().reset_index()
        elif item.gen_type in ['wind', 'solar', 'stogen_energy_storage', '新能源']:
            if item.gen_type == "新能源":
                base_df: Optional[pd.DataFrame] = None
                for gen_type in ['wind', 'solar']:
                    # 查询测点id
                    dev_id = utils.get_dev_id(area=item.area, field_key=gen_type)
                    if not dev_id:
                        continue
                    sql_power = f"""
                    SELECT
                            * 
                    FROM sys_power_data_tab AS spower
                    WHERE
                    spower.field_key = "{gen_type}" 
                    AND spower.area_name = "{item.area}"
                    AND spower.dev_id = "{dev_id}"
                    AND spower.data_year = {item.year}
                    """
                    sql_power_resp = db.execute(text(sql_power)).fetchall()
                    df_power = pd.DataFrame(sql_power_resp)
                    df_power = utils.process_sql_resp(df_power, val_name="power")
                    # 查询风电、光伏、储能的装机数据
                    if item.area == "全省":
                        sql_capacity = f"""
                        SELECT
                        sum(capacity)
                        FROM sys_newstation_tab
                        WHERE
                        field_key = "{gen_type}"
                        """
                    else:
                        sql_capacity = f"""
                        SELECT
                        sum(capacity)
                        FROM sys_newstation_tab
                        WHERE
                        city = "{item.area}"
                        AND field_key = "{gen_type}"
                        """
                    sql_capacity_resp = db.execute(text(sql_capacity)).fetchone()
                    if sql_capacity_resp and len(sql_capacity_resp) >= 1:
                        capacity = sql_capacity_resp[0]
                    else:
                        capacity = 0
                    df_power['capacity'] = capacity
                    if base_df is None:
                        base_df = df_power
                    else:
                        base_df = pd.concat([base_df, df_power], axis=0, ignore_index=True)
                if isinstance(base_df, pd.DataFrame) and not base_df.empty:
                    df = base_df.groupby("时间").sum().reset_index()
            else:
                # 查询测点id
                dev_id = utils.get_dev_id(area=item.area, field_key=item.gen_type)
                if dev_id:
                    sql_power = f"""
                    SELECT
                            * 
                    FROM sys_power_data_tab AS spower
                    WHERE
                    spower.field_key = "{item.gen_type}" 
                    AND spower.area_name = "{item.area}"
                    AND spower.dev_id = "{dev_id}"
                    AND spower.data_year = {item.year}
                    """
                    sql_power_resp = db.execute(text(sql_power)).fetchall()
                    df = pd.DataFrame(sql_power_resp)
                    df = utils.process_sql_resp(df, val_name="power")
                    # 查询风电、光伏、储能的装机数据
                    if item.area == "全省":
                        sql_capacity = f"""
                        SELECT
                        sum(capacity)
                        FROM sys_newstation_tab
                        WHERE
                        field_key = "{item.gen_type}"
                        """
                    else:
                        sql_capacity = f"""
                        SELECT
                        sum(capacity)
                        FROM sys_newstation_tab
                        WHERE
                        city = "{item.area}"
                        AND field_key = "{item.gen_type}"
                        """
                    sql_capacity_resp = db.execute(text(sql_capacity)).fetchone()
                    if sql_capacity_resp and len(sql_capacity_resp) >= 1:
                        capacity = sql_capacity_resp[0]
                    else:
                        capacity = 0
                    df['capacity'] = capacity
        else:
            # 查询测点id
            dev_id = utils.get_dev_id(area=item.area, field_key=item.gen_type)
            if dev_id:
                sql_power = f"""
                SELECT
                        * 
                FROM sys_power_data_tab AS spower
                WHERE
                spower.field_key = "{item.gen_type}" 
                AND spower.area_name = "{item.area}"
                AND spower.dev_id = "{dev_id}"
                AND spower.data_year = {item.year}
                """
                sql_power_resp = db.execute(text(sql_power)).fetchall()
                df = pd.DataFrame(sql_power_resp)
                df = utils.process_sql_resp(df, val_name="power")
                # 查询常规机组中类型装机数据
                sql_capacity = f"""
                SELECT
                sum(capacity)
                FROM sys_station_tab
                WHERE
                city = "{item.area}"
                AND field_key = "{item.gen_type}"
                """
                sql_capacity_resp = db.execute(text(sql_capacity)).fetchone()
                if sql_capacity_resp and len(sql_capacity_resp) >= 1:
                    capacity = sql_capacity_resp[0]
                else:
                    capacity = 0
                df['capacity'] = capacity

    if isinstance(df, pd.DataFrame) and df.empty:
        stime = f"{item.year}-01-01 00:00:00"
        etime = utils.get_current_datetime()
        time_list = pd.date_range(start=stime, end=etime, freq="5T").astype(str).tolist()
        power_list = [0] * len(time_list)
        df = pd.DataFrame({"时间": time_list, "power": power_list})
    option = {
        "options": "using_hours",
        "b_electric": True,
        "freq": 'M',
        "time_start": df['时间'].min(),
        "time_end": df["时间"].max(),
        "freq_step": "5T"
    }
    if item.gen_type in ['stogen_pump_hydro', "stogen_energy_storage"]:
        option['bstogen'] = True
    resp = hisdata_utils.get_load_series(df=df, options_dict=option)
    result.data = resp.to_dict("list")
    return result


@router.post("/history/new_energy/output", tags=[common.HENAN_TAG], response_model=data.Response)
def history_usual_new_energy_output(
        item: params.GetNewEnergyOutputDataParams,
        result: data.Response = Depends(utils.response_obj),
        db=Depends(utils.get_db)
):
    """
    获取电网运行数据-新能源-出力曲线
    :param result:
    :return:
    """
    if not item.year:
        max_year_sql = f"""
            SELECT 
            DISTINCT data_year
            FROM sys_power_data_tab
            ORDER BY data_year DESC
            LIMIT 1
            """
        max_year_resp = db.execute(text(max_year_sql)).fetchone()
        if max_year_resp and len(max_year_resp) >= 1:
            item.year = max_year_resp[0]
        else:
            item.year = dt.datetime.now().year
    if item.area in ['豫西', '豫中东', '豫南', '豫北']:
        query_city_sql = f"""
        SELECT city_name
        FROM sys_city_code_tab
        WHERE area_name = '{item.area}'
        """
        query_city_resp = db.execute(text(query_city_sql)).fetchall()
        citys = []
        for obj in query_city_resp:
            citys.append(obj[0])
        area_sql = f"area_name in {tuple(citys)}"
    else:
        citys = []
        area_sql = f"area_name = '{item.area}'"
    if isinstance(item.scene_date, dt.datetime):
        # 场景过滤
        item.year = item.scene_date.year
        date = "-".join(str(item.scene_date.date()).split("-")[1:])
        scence_sql = f" AND day_time = '{date}'"
    else:
        scence_sql = ""
    data_dict = {}
    # 新能源数据，只取统调，不考虑分布式
    for field_key in ["wind", "solar", "stogen_energy_storage"]:
        if field_key == "stogen_energy_storage":
            # 储能
            sql = f"""
            SELECT
            *
            FROM sys_power_data_tab
            WHERE 
            field_key = "{field_key}"
            AND {area_sql}
            AND data_year = {item.year}
            """
        else:
            # 风、光
            sql = f"""
            SELECT
            *
            FROM sys_power_data_tab
            WHERE 
            field_key = "{field_key}_centralized"
            AND {area_sql}
            AND data_year = {item.year}
            """
        if scence_sql:
            # 场景过滤
            sql += scence_sql
        sql_resp = db.execute(text(sql)).fetchall()
        df = pd.DataFrame(sql_resp).fillna(0)
        if field_key in ["wind", "solar"] and df.empty:
            # 统调为空数据时，则查询总的减去分布式的
            if citys:
                # 四个大区
                base_big_df: Optional[pd.DataFrame] = None
                for city in citys:
                    # 查询城市所属的测点id
                    city_total_dev_sql = f"""
                    SELECT
                    dev_id
                    FROM sys_dev_id_map_tab
                    WHERE field_key  = "{field_key}"
                    AND area_name = "{city}"
                    LIMIT 1
                    """
                    city_total_dev_resp = db.execute(text(city_total_dev_sql)).fetchone()
                    if not city_total_dev_resp or len(city_total_dev_resp) == 0:
                        continue
                    city_total_dev_id = city_total_dev_resp[0]
                    city_total_sql = f"""
                    SELECT
                    *
                    FROM sys_power_data_tab
                    WHERE data_year = {item.year}
                    AND dev_id = '{city_total_dev_id}'
                    """
                    if scence_sql:
                        # 场景过滤
                        city_total_sql += scence_sql
                    city_total_sql_resp = db.execute(text(city_total_sql)).fetchall()
                    if not city_total_sql_resp or len(city_total_sql_resp) == 0:
                        continue
                    city_total_power_df = pd.DataFrame(city_total_sql_resp)
                    city_total_power_df = city_total_power_df[[
                        col for col in city_total_power_df.columns
                        if col.startswith("value_") or col in ["data_year", "day_time"]
                    ]]
                    # 查询分布式
                    distribute_city_dev_sql = f"""
                    SELECT
                    dev_id
                    FROM sys_dev_id_map_tab
                    WHERE field_key  = "{field_key}_distribute"
                    AND area_name = "{city}"
                    LIMIT 1
                    """
                    distribute_city_dev_resp = db.execute(text(distribute_city_dev_sql)).fetchone()
                    if distribute_city_dev_resp and len(distribute_city_dev_resp) >= 1:
                        distribute_city_dev_id = distribute_city_dev_resp[0]
                    else:
                        logger.debug(f"未查询到city: {city}， field_key: {field_key}__distribute 对应测点id")
                        continue
                    distribute_city_power_sql = f"""
                    SELECT
                    *
                    FROM sys_power_data_tab
                    WHERE data_year = {item.year}
                    AND dev_id = '{distribute_city_dev_id}'
                    """
                    if scence_sql:
                        # 场景过滤
                        distribute_city_power_sql += scence_sql
                    distribute_city_power_resp = db.execute(text(distribute_city_power_sql)).fetchall()
                    if not distribute_city_power_resp or len(distribute_city_power_resp) == 0:
                        city_df = city_total_power_df
                    else:
                        distribute_city_power_df = pd.DataFrame(distribute_city_power_resp)
                        distribute_city_power_df = distribute_city_power_df[[
                            col for col in distribute_city_power_df.columns
                            if col.startswith("value_") or col in ["data_year", "day_time"]
                        ]]
                        merged = pd.merge(
                            city_total_power_df,
                            distribute_city_power_df,
                            on=['data_year', 'day_time'],
                            how='outer',
                            suffixes=('_1', '_2')
                        )
                        value_cols = [col for col in total_power_df.columns if col.startswith('value')]
                        for col in value_cols:
                            merged[col] = merged[col + '_1'] - merged[col + '_2']
                        city_df = merged.drop(
                            [col + '_1' for col in value_cols] + [col + '_2' for col in value_cols], axis=1
                        )
                    if base_big_df is None:
                        base_big_df = city_df
                    else:
                        base_big_df = pd.concat([base_big_df, city_df], axis=0, ignore_index=True)
                if isinstance(base_big_df, pd.DataFrame):
                    df = base_big_df.groupby(['data_year', 'day_time']).sum().reset_index()
            else:
                # 先查询测点id-总
                dev_sql = f"""
                SELECT
                dev_id
                FROM sys_dev_id_map_tab
                WHERE field_key  = "{field_key}"
                AND {area_sql}
                LIMIT 1
                """
                dev_resp = db.execute(text(dev_sql)).fetchone()
                if dev_resp and len(dev_resp) >= 1:
                    dev_id = dev_resp[0]
                else:
                    logger.debug(f"未查询到area: {item.area}， field_key: {field_key}对应测点id")
                    continue
                # 结合测点id，查询对应的出力数据
                total_power_sql = f"""
                SELECT
                *
                FROM sys_power_data_tab
                WHERE 
                {area_sql}
                AND data_year = {item.year}
                AND dev_id = '{dev_id}'
                """
                if scence_sql:
                    # 场景过滤
                    total_power_sql += scence_sql
                total_power_resp = db.execute(text(total_power_sql)).fetchall()
                if not total_power_resp or len(total_power_resp) == 0:
                    continue
                total_power_df = pd.DataFrame(total_power_resp)
                total_power_df = total_power_df[[
                    col for col in total_power_df.columns
                    if col.startswith("value_") or col in ["data_year", "day_time"]
                ]]
                # 查询分布式
                # 先查询测点id-分布式
                distribute_dev_sql = f"""
                SELECT
                dev_id
                FROM sys_dev_id_map_tab
                WHERE field_key  = "{field_key}_distribute"
                AND {area_sql}
                LIMIT 1
                """
                distribute_dev_resp = db.execute(text(distribute_dev_sql)).fetchone()
                if distribute_dev_resp and len(distribute_dev_resp) >= 1:
                    distribute_dev_id = distribute_dev_resp[0]
                else:
                    logger.debug(f"未查询到area: {item.area}， field_key: {field_key}__distribute 对应测点id")
                    continue
                distribute_power_sql = f"""
                SELECT
                *
                FROM sys_power_data_tab
                WHERE 
                {area_sql}
                AND data_year = {item.year}
                AND dev_id = '{distribute_dev_id}'
                """
                if scence_sql:
                    # 场景过滤
                    distribute_power_sql += scence_sql
                distribute_power_resp = db.execute(text(distribute_power_sql)).fetchall()
                if not distribute_power_resp or len(distribute_power_resp) == 0:
                    continue
                distribute_power_df = pd.DataFrame(distribute_power_resp)
                distribute_power_df = distribute_power_df[[
                    col for col in distribute_power_df.columns
                    if col.startswith("value_") or col in ["data_year", "day_time"]
                ]]
                merged = pd.merge(
                    total_power_df,
                    distribute_power_df,
                    on=['data_year', 'day_time'],
                    how='outer',
                    suffixes=('_1', '_2')
                )
                value_cols = [col for col in total_power_df.columns if col.startswith('value')]
                for col in value_cols:
                    merged[col] = merged[col + '_1'] - merged[col + '_2']
                df = merged.drop([col + '_1' for col in value_cols] + [col + '_2' for col in value_cols], axis=1)
        df = utils.process_sql_resp(df, is_unit=True)
        new_field_key = common.field_map.get(field_key, "")
        data_dict[new_field_key] = df['value'].tolist()
        data_dict['time_range'] = df['时间'].astype(str).tolist()
    if "风电" in data_dict and "光伏" in data_dict:
        new_energy_value_list = []
        for i in range(len(data_dict['风电'])):
            v = data_dict['风电'][i] + data_dict['光伏'][i]
            new_energy_value_list.append(v)
        data_dict["新能源"] = new_energy_value_list
    result.data = data_dict
    return result


@router.get("/history/acdc/curve/list", tags=[common.HENAN_TAG], response_model=data.Response)
def history_acdc_curve_list(result: data.Response = Depends(utils.response_obj), db=Depends(utils.get_db)):
    """
    获取电网运行数据-直流-曲线列表
    :param result:
    :return:
    """
    curve_list = utils.get_dc_curev_list()
    result.data = curve_list
    return result


@router.get("/history/acdc/year/list", tags=[common.HENAN_TAG], response_model=data.Response)
def history_acdc_year_list(result: data.Response = Depends(utils.response_obj), db=Depends(utils.get_db)):
    """
    获取电网运行数据-直流-年份列表
    :param result:
    :return:
    """
    sql = f"""
    SELECT
        DISTINCT data_year
    FROM
        sys_feedin_data_tab
    """
    sql_resp = db.execute(text(sql)).fetchall()
    year_list = []
    for obj in sql_resp:
        year_list.append(obj[0])
    result.data = year_list
    return result


@router.post("/history/acdc/send/power/year", tags=[common.HENAN_TAG], response_model=data.Response)
def history_acdc_send_power_year(
        item: params.ListAcDcYearPowerDataParams,
        result: data.Response = Depends(utils.response_obj),
        db=Depends(utils.get_db)
):
    """
    获取电网运行数据-直流-年送电曲线
    :param result:
    :return:
    """
    if not item.year:
        max_year_sql = """
        SELECT
        data_year
        FROM sys_feedin_data_tab
        ORDER BY data_year DESC
        LIMIT 1
        """
        max_year_resp = db.execute(text(max_year_sql)).fetchone()
        if max_year_sql and len(max_year_resp) >= 1:
            item.year = max_year_resp[0]
        else:
            item.year = dt.datetime.now().year
    if not item.name:
        # 默认查询第一个曲线名称
        curve_list = utils.get_dc_curev_list()
        if not curve_list:
            result.code = 1002
            result.msg = "未查询到曲线名称"
            return result
        curve_name = curve_list[0]
        item.name = curve_name
        # 查询曲线的测点id
        dev_id = utils.get_curve_dev_id(curve_name=curve_name)
        if not dev_id:
            result.code = 1002
            result.msg = "未查询到测点id"
            return result
    else:
        sql_curve = f"""
        SELECT
            dev_id 
        FROM
            sys_dev_id_map_tab 
        WHERE
            field_key = "feedin" 
            AND display_desc = "{item.name}"
            LIMIT 1
        """
        curve_resp = db.execute(text(sql_curve)).fetchone()
        if curve_resp and len(curve_resp) >= 1:
            dev_id = curve_resp[0]
        else:
            result.code = 1002
            result.msg = "未查询到曲线信息"
            return result
    sql = f"""
    SELECT
        * 
    FROM
        sys_feedin_data_tab 
    WHERE
        data_year = '{item.year}'
        AND dev_id = '{dev_id}'
    """
    sql_resp = db.execute(text(sql)).fetchall()
    df = pd.DataFrame(sql_resp)
    df = utils.process_sql_resp(df)
    if item.mode == 1:
        # 日最大值模式数据
        option = {
            "options": "max",
            "freq": 'D',
            "time_start": df['时间'].min(),
            "time_end": df['时间'].max(),
        }
        column_name = "max_load"
    else:
        # 时序模式数据
        option = {
            "options": "query",
            "freq": 'M',
            "time_start": df['时间'].min(),
            "time_end": df['时间'].max(),
        }
        column_name = "value"
    resp = hisdata_utils.get_load_series(df=df, options_dict=option)
    if isinstance(resp, pd.DataFrame):
        result.data = {
            "curve_name": item.name,
            "time_range": resp['时间'].astype(str).tolist(),
            "value": resp[column_name].tolist()
        }
    else:
        result.data = resp
    return result


@router.post("/history/acdc/send/power/day", tags=[common.HENAN_TAG], response_model=data.Response)
def history_acdc_send_power_day(
        item: params.ListAcDcDayPowerDataParams,
        result: data.Response = Depends(utils.response_obj),
        db=Depends(utils.get_db)
):
    """
    获取电网运行数据-直流-日送电曲线
    :param result:
    :return:
    """
    if not item.name:
        # 默认查询第一个曲线名称
        curve_list = utils.get_dc_curev_list()
        if not curve_list:
            result.code = 1002
            result.msg = "未查询到曲线名称"
            return result
        curve_name = curve_list[0]
        item.name = curve_name
        # 查询曲线的测点id
        dev_id = utils.get_curve_dev_id(curve_name=curve_name)
        if not dev_id:
            result.code = 1002
            result.msg = "未查询到测点id"
            return result
    else:
        sql_curve = f"""
        SELECT
            dev_id 
        FROM
            sys_dev_id_map_tab 
        WHERE
            field_key = "feedin" 
            AND display_desc = "{item.name}"
            LIMIT 1
        """
        curve_resp = db.execute(text(sql_curve)).fetchone()
        if curve_resp and len(curve_resp) >= 1:
            dev_id = curve_resp[0]
        else:
            result.code = 1002
            result.msg = "未查询到曲线信息"
            return result
    sql = f"""
    SELECT
        * 
    FROM
        sys_feedin_data_tab 
    WHERE
        dev_id = '{dev_id}'
    """
    # 负荷
    load_sql = f"""
    SELECT
    *
    FROM sys_load_data_tab
    WHERE area_name = "全省"
    AND field_key = "load"
    AND dev_id = "121878665603582980"
    """
    # 全网风电
    wind_sql = f"""
    SELECT
    *
    FROM sys_power_data_tab
    WHERE field_key = "wind"
    AND area_name = "全省"
    AND dev_id = "121878665603584101"
    """
    # 全网光伏
    solar_sql = f"""
    SELECT
    *
    FROM sys_power_data_tab
    WHERE field_key = "solar"
    AND area_name = "全省"
    AND dev_id = "121878665603584103"
    """
    if isinstance(item.scene_date, dt.datetime):
        # 获取场景那一天的所有时间点的数据
        date = "-".join(str(item.scene_date.date()).split("-")[1:])
        sql += f" AND data_year = {item.scene_date.year} AND day_time = '{date}'"
        stime = f"{str(item.scene_date.date())} 00:00:00"
        etime = f"{str(item.scene_date.date())} 23:59:59"
        # 全省负荷
        load_sql += f" AND data_year = {item.scene_date.year} AND day_time = '{date}'"
        # 全省风电
        wind_sql += f" AND data_year = {item.scene_date.year} AND day_time = '{date}'"
        # 全省光伏
        solar_sql += f" AND data_year = {item.scene_date.year} AND day_time = '{date}'"
    else:
        if isinstance(item.date, dt.date):
            date = "-".join(str(item.date).split("-")[1:])
            sql += f" AND data_year = {item.date.year} AND day_time = '{date}'"
            stime = f"{str(item.date)} 00:00:00"
            etime = f"{str(item.date)} 23:59:59"
            # 全省负荷
            load_sql += f" AND data_year = {item.date.year} AND day_time = '{date}'"
            # 全省风电
            wind_sql += f" AND data_year = {item.date.year} AND day_time = '{date}'"
            # 全省光伏
            solar_sql += f" AND data_year = {item.date.year} AND day_time = '{date}'"
        else:
            max_year_sql = f"""
                SELECT
                data_year
                FROM sys_feedin_data_tab
                WHERE dev_id = '{dev_id}'
                ORDER BY data_year DESC
                LIMIT 1
            """
            max_year_resp = db.execute(text(max_year_sql)).fetchone()
            if max_year_sql is not None and len(max_year_resp) >= 1:
                year = max_year_resp[0]
            else:
                year = dt.datetime.now().year
            sql += f" AND data_year = {year}"
            # 全省负荷
            load_sql += f" AND data_year = {year}"
            # 全省风电
            wind_sql += f" AND data_year = {year}"
            # 全省光伏
            solar_sql += f" AND data_year = {year}"
            # 获取最近的一天
            new_day_sql = f"""
                SELECT
                day_time
                FROM sys_feedin_data_tab
                WHERE data_year = {year}
                AND dev_id = '{dev_id}'
                ORDER BY day_time ASC
                LIMIT 1
            """
            new_day_resp = db.execute(text(new_day_sql)).fetchone()
            if new_day_resp is not None and len(new_day_resp) >= 1:
                day = new_day_resp[0]
                stime = f"{year}-{day} 00:00:00"
                etime = f"{year}-{day} 23:59:59"
            else:
                date = dt.datetime.now().date()
                day = "-".join(str(date).split("-")[1:])
                stime = f"{str(date)} 00:00:00"
                etime = f"{str(date)} 23:59:59"
            sql += f" AND day_time = '{day}'"
            # 全省负荷
            load_sql += f" AND day_time = '{day}'"
            # 全省风电
            wind_sql += f" AND day_time = '{day}'"
            # 全省光伏
            solar_sql += f" AND day_time = '{day}'"
    sql_resp = db.execute(text(sql)).fetchall()
    df = pd.DataFrame(sql_resp)
    df = utils.process_sql_resp(df)
    # 时序模式数据
    option = {
        "options": "query",
        "freq": 'M',
        "time_start": stime,
        "time_end": etime,
    }
    resp = hisdata_utils.get_load_series(df=df, options_dict=option)
    data_dict = {}
    if isinstance(resp, pd.DataFrame):
        data_dict = {
            "curve_name": item.name,
            "time_range": resp['时间'].astype(str).tolist(),
            "value": resp["value"].tolist()
        }
    else:
        data_dict = resp
    # 查询负荷
    print(load_sql)
    load_sql_resp = db.execute(text(load_sql)).fetchall()
    load_df = pd.DataFrame(load_sql_resp)
    load_df = utils.process_sql_resp(load_df)
    if load_df.empty:
        data_dict['load'] = []
    else:
        data_dict['load'] = (load_df['value'] * 0.1).tolist()
    # 查询风电
    wind_sql_resp = db.execute(text(wind_sql)).fetchall()
    wind_df = pd.DataFrame(wind_sql_resp)
    wind_df = utils.process_sql_resp(wind_df)
    if wind_df.empty:
        data_dict["wind"] = []
    else:
        data_dict['wind'] = (wind_df['value'] * 0.1).tolist()
    # 查询光伏
    solar_sql_resp = db.execute(text(solar_sql)).fetchall()
    solar_df = pd.DataFrame(solar_sql_resp)
    solar_df = utils.process_sql_resp(solar_df)
    if solar_df.empty:
        data_dict['solar'] = []
    else:
        data_dict['solar'] = (solar_df['value'] * 0.1).tolist()
    # 新能源
    data_dict['new_energy'] = ((wind_df['value'] + solar_df['value']) * 0.1).tolist()
    result.data = data_dict
    return result


@router.post("/history/acdc/power/data", tags=[common.HENAN_TAG], response_model=data.Response)
def history_acdc_power_data(
        item: params.ListAcDcPowerDataParams,
        result: data.Response = Depends(utils.response_obj),
        db=Depends(utils.get_db)
):
    """
    获取电网运行数据-直流-功率输送值统计
    :param result:
    :return:
    """
    if not item.year:
        max_year_sql = """
        SELECT
        data_year
        FROM sys_feedin_data_tab
        ORDER BY data_year DESC
        LIMIT 1
        """
        max_year_resp = db.execute(text(max_year_sql)).fetchone()
        if max_year_sql and len(max_year_resp) >= 1:
            item.year = max_year_resp[0]
        else:
            item.year = dt.datetime.now().year
    if not item.name:
        # 默认查询第一个曲线名称
        curve_list = utils.get_dc_curev_list()
        if not curve_list:
            result.code = 1002
            result.msg = "未查询到曲线名称"
            return result
        curve_name = curve_list[0]
        item.name = curve_name
        # 查询曲线的测点id
        dev_id = utils.get_curve_dev_id(curve_name=curve_name)
        if not dev_id:
            result.code = 1002
            result.msg = "未查询到测点id"
            return result
    else:
        sql_curve = f"""
        SELECT
            dev_id 
        FROM
            sys_dev_id_map_tab 
        WHERE
            field_key = "feedin" 
            AND display_desc = "{item.name}"
            LIMIT 1
        """
        curve_resp = db.execute(text(sql_curve)).fetchone()
        if curve_resp and len(curve_resp) >= 1:
            dev_id = curve_resp[0]
        else:
            result.code = 1002
            result.msg = "未查询到曲线信息"
            return result

    sql = f"""
    SELECT
        * 
    FROM
        sys_feedin_data_tab 
    WHERE
        dev_id = '{dev_id}'
        AND data_year = {item.year}
    """
    if item.freq == "M":
        if item.month and item.day:
            new_month = str(item.month).zfill(2)
            new_day = str(item.day).zfill(2)
            day_time = f"{new_month}-{new_day}"
            sql += f"AND day_time = '{day_time}'"
        elif item.month:
            new_month = str(item.month).zfill(2)
            sql += f"AND day_time >= '{new_month}-01' AND day_time <= '{new_month}-31'"
    elif item.freq == "H":
        # 小时, 可以选择多个月份
        if item.months:
            sql_months = ""
            for month in item.months:
                month_v = str(month).zfill(2)
                sql_months += f" (day_time >= '{month_v}-01' AND day_time <= '{month_v}-31') OR"
            sql_months = sql_months.strip("OR")
            sql += f" AND ( {sql_months} )"
    sql_resp = db.execute(text(sql)).fetchall()
    df = pd.DataFrame(sql_resp)
    df = utils.process_sql_resp(df, val_name="power")
    option = {
        "options": "using_hours",
        "freq": item.freq,
        "b_electric": True,
        "time_start": df['时间'].min(),
        "time_end": df["时间"].max(),
    }
    if item.freq == "M":
        if item.month and item.day:
            option['freq'] = "H"
        elif item.month:
            option["freq"] = "D"
    resp = hisdata_utils.get_load_series(df=df, options_dict=option)
    if isinstance(resp, pd.DataFrame):
        order_dict = {"spring": 1, "summer": 2, "autumn": 3, "winter": 4}
        resp['sort_key'] = resp['时间'].map(order_dict)
        # 根据新生成的sort_key列进行排序，ascending=True表示升序排序
        resp = resp.sort_values(by="sort_key", ascending=True)
        data = {"curve_name": item.name, "time_range": resp["时间"].tolist(), "value": resp["usinghour"].tolist()}
        result.data = data
    else:
        result.data = resp
    return result


@router.post("/history/acdc/power/distribute", tags=[common.HENAN_TAG], response_model=data.Response)
def history_acdc_power_distribute(
        item: params.GetAcDcPowerDistributeParams,
        result: data.Response = Depends(utils.response_obj),
        db=Depends(utils.get_db)
):
    """
    获取电网运行数据-交直流-出力统计分布图
    :param result:
    :return:
    """
    if not item.year:
        max_year_sql = """
        SELECT
        data_year
        FROM sys_feedin_data_tab
        ORDER BY data_year DESC
        LIMIT 1
        """
        max_year_resp = db.execute(text(max_year_sql)).fetchone()
        if max_year_sql and len(max_year_resp) >= 1:
            item.year = max_year_resp[0]
        else:
            item.year = dt.datetime.now().year
    if not item.name:
        # 默认查询第一个曲线名称
        curve_list = utils.get_dc_curev_list()
        if not curve_list:
            result.code = 1002
            result.msg = "未查询到曲线名称"
            return result
        curve_name = curve_list[0]
        item.name = curve_name
        # 查询曲线的测点id
        dev_id = utils.get_curve_dev_id(curve_name=curve_name)
        if not dev_id:
            result.code = 1002
            result.msg = "未查询到测点id"
            return result
    else:
        sql_curve = f"""
        SELECT
            dev_id 
        FROM
            sys_dev_id_map_tab 
        WHERE
            field_key = "feedin" 
            AND display_desc = "{item.name}"
            LIMIT 1
        """
        curve_resp = db.execute(text(sql_curve)).fetchone()
        if curve_resp and len(curve_resp) >= 1:
            dev_id = curve_resp[0]
        else:
            result.code = 1002
            result.msg = "未查询到曲线信息"
            return result
    sql = f"""
    SELECT
        * 
    FROM
        sys_feedin_data_tab 
    WHERE
        dev_id = '{dev_id}'
        AND data_year = {item.year}
    """
    print(sql)
    sql_resp = db.execute(text(sql)).fetchall()
    df = pd.DataFrame(sql_resp)
    df = utils.process_sql_resp(df)
    option = {
        "options": "count",
        "months": item.months,
        "hours": item.hours,
        "confidence": 1.0,
        "time_start": df['时间'].min(),
        "time_end": df['时间'].max()
    }
    resp, max_value = hisdata_utils.get_load_series(df=df, options_dict=option)
    if isinstance(resp, pd.DataFrame):
        new_dict = {}
        for index, obj in resp.iterrows():
            new_dict[index] = round(obj[0], 2)
        result.data = {"outputs": new_dict, "max_value": max_value}
    else:
        result.data = resp
    return result


@router.post("/history/interface/tide/data", tags=[common.HENAN_TAG], response_model=data.Response)
def history_interface_tide_data(
        item: params.GetInterfaceTideDataParams,
        result: data.Response = Depends(utils.response_obj),
        db=Depends(utils.get_db)
):
    """
    获取电网运行数据-断面-潮流统计
    :param result:
    :return:
    """
    now_date = dt.datetime.now().date()
    start_date = dt.datetime.strptime(item.start_time, "%Y-%m-%d").date()
    end_date =dt.datetime.strptime(item.end_time, "%Y-%m-%d").date()  # 获取结束日期部分

    s_day_time = "-".join(item.start_time.split(" ")[0].split("-")[1:])
    e_day_time = "-".join(item.end_time.split(" ")[0].split("-")[1:])
    s_year = int(item.start_time.split(" ")[0].split("-")[0])
    e_year = int(item.end_time.split(" ")[0].split("-")[0])
    # 找出sys_qs_interface_tab下跟 item.name 为同一个 zone 的所有inf_name
    # 判断 item.name 的 inf_type 是否为 "line_inf"
    inf_sql = f"""
    SELECT DISTINCT inf_name
    FROM sys_qs_interface_tab
    WHERE zone = (
        SELECT zone
        FROM sys_qs_interface_tab
        WHERE ((inf_name = '{item.name}') OR (zone = '{item.name}'))
        AND NOT (inf_type = 'line_inf')
        LIMIT 1
    )
    AND inf_name <> '{item.name}';
    """
    try:
        interface_list = db.execute(text(inf_sql)).scalars().all()
    except Exception as e:
        logger.warning(f"查询{item.name}的关联断面数据时出错: {e}")
        interface_list = []
    
    # 将原始接口名称也添加到列表中，确保包含所有相关接口
    all_interfaces = [item.name] + interface_list
    
    # 构建查询所有接口数据的SQL
    interface_names_str = '", "'.join(all_interfaces)

    history_power_sql = f"""
    SELECT
    sidt.*, sit.inf_name, sit.inf_desc, sit.inf_limit, sit.inf_type, sit.vlevel, sit.zone
    FROM sys_qs_inf_data_tab sidt 
    LEFT JOIN sys_qs_interface_tab sit ON sit.id = sidt.dev_id
    WHERE sit.inf_name IN ("{interface_names_str}")
    AND sidt.field_key = "inf_power"
    AND day_time >= "{s_day_time}"
    AND day_time <= "{e_day_time}"
    AND data_year >= "{s_year}"
    AND data_year <= "{e_year}"
    """
    history_power_sql_resp = db.execute(text(history_power_sql)).fetchall()
    power_df = pd.DataFrame(history_power_sql_resp)
    if end_date >= now_date:
        real_time_start = now_date.strftime("%Y-%m-%d 00:00:00")
        real_time_end = now_date.strftime("%Y-%m-%d 23:59:59")
        # 查询当天实时数据
        today_power_sql = f"""
        SELECT
        ddt.id, dit.dev_id, ift.inf_name, ift.inf_desc, ift.inf_limit, ift.inf_type, ift.vlevel, ift.zone, ddt.time, ddt.value as value_raw,
        DATE_FORMAT(ddt.time, '%Y-%m-%d') as data_date,
        DATE_FORMAT(ddt.time, '%m-%d') as day_time,
        YEAR(ddt.time) as data_year,
        DATE_FORMAT(ddt.time, '%H%i') as time_point
        FROM sys_real_day_data_tab ddt
        LEFT JOIN sys_real_day_index_tab dit ON dit.id = ddt.power_id
        LEFT JOIN sys_qs_interface_tab ift ON ift.id = dit.dev_id
        WHERE ddt.time >= "{real_time_start}" AND ddt.time <= "{real_time_end}"
        AND dit.field_key = "inf_power"
        AND ift.inf_name IN ("{interface_names_str}")
        """
        today_power_sql_resp = db.execute(text(today_power_sql)).fetchall()
        today_power_raw_df = pd.DataFrame(today_power_sql_resp)
        
        # 使用工具函数将实时数据转换为与历史数据相同的格式
        today_power_df = convert_realtime_to_history_format(today_power_raw_df)
        if not power_df.empty:
            power_df = pd.concat([power_df, today_power_df], ignore_index=True)
        else:
            power_df = today_power_df
    
    resp = inferface_analysis(power_df)
    result.data = resp
    return result


@router.get("/history/interface/name/list", tags=[common.HENAN_TAG], response_model=data.Response)
def history_interface_name_list(
        page: int = 1,
        size: int = 9999,
        inf_type: Optional[str] = None,
        result: data.Response = Depends(utils.response_obj),
        db=Depends(utils.get_db)
):
    """
    获取电网运行数据-断面-断名名称列表
    :param result:
    :return:
    """
    if inf_type in ["line_inf", "zone220_inf"]:
        sql = f"""
            SELECT
            inf_name, inf_desc
            FROM sys_qs_interface_tab
            WHERE in_service = 1
            AND inf_type = '{inf_type}'
            ORDER BY vlevel DESC, inf_limit DESC LIMIT {(page - 1) * size}, {size}
        """
    else:
        sql = f"""
            SELECT DISTINCT zone
            FROM sys_qs_interface_tab
            WHERE in_service = 1
            AND inf_type = '{inf_type}'
            LIMIT {(page - 1) * size}, {size}
        """
    sql_resp = db.execute(text(sql)).fetchall()
    if not sql_resp:
        result.data = []
        return result
    name_list = []
    for obj in sql_resp:
        if inf_type in ["line_inf", "zone220_inf"]:
            # 断面类型为线性或220kV断面
            data_dict = {"name": obj[0], "desc": obj[1]}
        else:
            data_dict = {"name": obj[0], "desc": obj[0]}
        name_list.append(data_dict)
    result.data = name_list
    return result


@router.get("/history/interface/year/list", tags=[common.HENAN_TAG], response_model=data.Response)
def history_interface_year_list(result: data.Response = Depends(utils.response_obj), db=Depends(utils.get_db)):
    """
    获取电网运行数据-断面-断面年份列表
    :param result:
    :return:
    """
    sql = f"""
    SELECT
    DISTINCT data_year
    FROM sys_interface_data_tab
    ORDER BY data_year DESC
    """
    sql_resp = db.execute(text(sql)).fetchall()
    year_list = []
    for obj in sql_resp:
        year_list.append(obj[0])
    result.data = year_list
    return result


@router.post("/history/interface/query", tags=[common.HENAN_TAG], response_model=data.Response)
def history_interface_query(
        item: params.HistoryInterfaceQueryDataParams,
        result: data.Response = Depends(utils.response_obj),
        db=Depends(utils.get_db)
):
    """
    获取电网运行数据-断面数据
    :param result:
    :return:
    """
    if not item.name:
        # 未传入断面名称，则从数据库中获取第一个断面名称
        first_interface_sql = f"""
            SELECT
            DISTINCT inf_name, vlevel, inf_limit
            FROM sys_qs_interface_tab
            WHERE in_service = 1
            ORDER BY vlevel DESC, inf_limit DESC
            LIMIT 1
        """
        first_interface = db.execute(text(first_interface_sql)).fetchone()
        if not first_interface:
            result.data = []
            return result
        item.name = first_interface[0]
    if item.scene_date and isinstance(item.scene_date, dt.datetime):
        # 获取场景那一天的所有时间点的数据
        item.stime = str(dt.datetime(item.scene_date.year, item.scene_date.month, item.scene_date.day, 0, 0, 0))
        item.etime = str(
            dt.datetime(item.scene_date.year, item.scene_date.month, item.scene_date.day, 23, 59, 59)
        )
    else:
        # 未传入典型场景时间
        if not item.stime:
            stime_sql = f"""
            SELECT
            data_year, day_time
            FROM sys_qs_inf_data_tab
            ORDER BY data_year DESC, day_time ASC
            LIMIT 1
            """
            stime_resp = db.execute(text(stime_sql)).fetchone()
            if not stime_resp:
                result.code = 1002
                result.msg = "未查询到断面信息"
                return result
            item.stime = f"{stime_resp[0]}-{stime_resp[1]} 00:00:00"
        if not item.etime:
            etime_sql = f"""
            SELECT
            data_year, day_time
            FROM sys_qs_inf_data_tab
            ORDER BY data_year DESC, day_time DESC
            LIMIT 1
            """
            etime_resp = db.execute(text(etime_sql)).fetchone()
            if not etime_resp:
                result.code = 1002
                result.msg = "未查询到断面信息"
                return result
            item.etime = f"{etime_resp[0]}-{etime_resp[1]} 23:59:59"
    if isinstance(item.stime, str) and isinstance(item.etime, str):
        start_time = dt.datetime.strptime(item.stime, "%Y-%m-%d %H:%M:%S")
        end_time = dt.datetime.strptime(item.etime, "%Y-%m-%d %H:%M:%S")
    else:
        start_time = item.stime
        end_time = item.etime
    power_sql = f"""
    SELECT
    sidt.*
    FROM sys_qs_inf_data_tab sidt 
    LEFT JOIN (
    SELECT
    * FROM sys_qs_interface_tab
    WHERE inf_name = "{item.name}"
    LIMIT 1
        ) sit ON sit.id = sidt.dev_id
    WHERE sit.inf_name = "{item.name}"
    AND sidt.field_key = "inf_power"
    AND sidt.data_year = {start_time.year}
    AND sidt.day_time >= '{"-".join(str(start_time.date()).split("-")[1:])}'
    AND sidt.day_time <= '{"-".join(str(end_time.date()).split("-")[1:])}'
    """
    power_sql_resp = db.execute(text(power_sql)).fetchall()
    power_df = pd.DataFrame(power_sql_resp)
    power_df = utils.process_sql_resp(power_df, is_filter=True, val_name=f"{item.name}_power")
    power_df = power_df.groupby(["时间"], as_index=False).first()
    with open(common.inf_define_file, "r") as fp:
        content = yaml.safe_load(fp.read())
        inf_limit = content.get(item.name, {}).get("inf_limit", {}).get("col_value")
        power_df[f'{item.name}_limit'] = inf_limit
    new_df = power_df.fillna(0)
    option = {
        "options": item.action,
        "freq": item.freq,
        "threshold": item.threshold,
        "time_start": item.stime,
        "time_end": item.etime,
        "hours": item.hours,
        "months": item.months,
        "confidence": item.confidence
    }
    resp = hisdata_utils.get_load_series(df=new_df, options_dict=option)
    if isinstance(resp, pd.DataFrame):
        resp["时间"] = resp['时间'].astype(str)
        resp_dict = resp.to_dict("list")
        result.data = resp_dict
    else:
        result.data = resp
    return result


@router.post("/history/station/data", tags=[common.HENAN_TAG], response_model=data.Response)
def history_station_data(
        item: params.HistoryStationDataParams,
        result: data.Response = Depends(utils.response_obj),
        db=Depends(utils.get_db)
):
    """
    获取电网运行数据-厂站数据
    :param result:
    :return:
    """
    if item.name in ['豫西', '豫中东', '豫南', '豫北']:
        sql_city = f"""
        SELECT
            city_name 
        FROM
            sys_city_code_tab 
        WHERE
            area_name = '{item.name}'
        """
        city_resp = db.execute(text(sql_city)).fetchall()
        citys = []
        for obj in city_resp:
            citys.append(obj[0])
        if not citys:
            result.code = 1002
            result.msg = '区域未查询到包含的城市'
            return result
        elif len(citys) == 1:
            if item.mode == 2:
                # 常规机组
                sql = f"""
                    SELECT `name`,city,zone,capacity,longitude,latitude,field_key, vlevel
                    FROM
                        sys_station_tab 
                    WHERE in_service = 1 
                        AND field_key not in ( 'wind', 'solar', 'wind_solar' ) 
                        AND vlevel >= {220 * 0.9} 
                        AND city = "{citys[0]}"
                    """
            elif item.mode == 3:
                # 电源特性
                sql = f"""
                SELECT `name`,city,zone,capacity,longitude,latitude,field_key, vlevel
                FROM
                    sys_station_tab 
                WHERE in_service = 1 
                    AND ((
                            field_key IN ( 'wind', 'solar', 'wind_solar' ) 
                            AND vlevel >= {110 * 0.9} 
                        ) 
                    OR ( field_key NOT IN ( 'wind', 'solar', 'wind_solar' ) AND vlevel >= {220 * 0.9} ))
                    AND city = "{citys[0]}"
                """
            else:
                # 新能源
                sql = f"""
                    SELECT `name`,city,zone,capacity,longitude,latitude,field_key, vlevel
                    FROM
                        sys_station_tab
                    WHERE in_service = 1
                    AND field_key IN ( 'wind', 'solar', 'wind_solar' ) 
                    AND vlevel >= {110 * 0.9}
                    AND city = "{citys[0]}"
                    """
        else:
            if item.mode == 2:
                # 常规机组
                sql = f"""
                    SELECT `name`,city,zone,capacity,longitude,latitude,field_key, vlevel
                    FROM
                        sys_station_tab 
                    WHERE in_service = 1 
                        AND field_key not in ( 'wind', 'solar', 'wind_solar' ) 
                        AND vlevel >= {220 * 0.9}
                        AND city in {tuple(citys)}
                    """
            elif item.mode == 3:
                # 电源特性
                sql = f"""
                SELECT `name`,city,zone,capacity,longitude,latitude,field_key, vlevel
                FROM
                    sys_station_tab 
                WHERE in_service = 1 
                    AND ((
                            field_key IN ( 'wind', 'solar', 'wind_solar' ) 
                            AND vlevel >= {110 * 0.9} 
                        ) 
                    OR ( field_key NOT IN ( 'wind', 'solar', 'wind_solar' ) AND vlevel >= {220 * 0.9} ))
                    AND city in {tuple(citys)}
                """
            else:
                # 新能源
                sql = f"""
                    SELECT `name`,city,zone,capacity,longitude,latitude,field_key, vlevel
                    FROM
                        sys_station_tab
                    WHERE in_service = 1
                        AND field_key IN ( 'wind', 'solar', 'wind_solar' ) 
                        AND vlevel >= {110 * 0.9}
                        AND city in {tuple(citys)}
                    """
    elif item.name in ['全省', '全部', '全网']:
        if item.mode == 2:
            # 常规机组
            sql = f"""
            SELECT `name`,city,zone,capacity,longitude,latitude,field_key, vlevel
            FROM
                sys_station_tab 
            WHERE in_service = 1 
                AND field_key not in ( 'wind', 'solar', 'wind_solar' ) 
                AND vlevel >= {220 * 0.9}
            """
        elif item.mode == 3:
            # 电源特性
            sql = f"""
            SELECT `name`,city,zone,capacity,longitude,latitude,field_key, vlevel
            FROM
                sys_station_tab 
            WHERE in_service = 1 
                AND ((
                        field_key IN ( 'wind', 'solar', 'wind_solar' ) 
                        AND vlevel >= {110 * 0.9} 
                    ) 
                OR ( field_key NOT IN ( 'wind', 'solar', 'wind_solar' ) AND vlevel >= {220 * 0.9} ))
            """
        else:
            sql = f"""
            SELECT `name`,city,zone,capacity,longitude,latitude,field_key, vlevel
            FROM
                sys_station_tab
            WHERE in_service = 1
            AND field_key IN ( 'wind', 'solar', 'wind_solar' ) 
            AND vlevel >= {110 * 0.9}
            """
    else:
        if item.mode == 2:
            # 常规机组
            sql = f"""
            SELECT `name`,city,zone,capacity,longitude,latitude,field_key, vlevel
            FROM
                sys_station_tab
            WHERE city = "{item.name}"
                AND in_service = 1 
                AND field_key not in ( 'wind', 'solar', 'wind_solar' ) 
                AND vlevel >= {220 * 0.9} 
            """
        elif item.mode == 3:
            # 电源特性
            sql = f"""
            SELECT `name`,city,zone,capacity,longitude,latitude,field_key, vlevel
            FROM
                sys_station_tab 
            WHERE in_service = 1 
                AND ((
                        field_key IN ( 'wind', 'solar', 'wind_solar' ) 
                        AND vlevel >= {110 * 0.9} 
                    ) 
                OR ( field_key NOT IN ( 'wind', 'solar', 'wind_solar' ) AND vlevel >= {220 * 0.9} ))
                AND city = "{item.name}"
            """
        else:
            # 新能源
            sql = f"""
            SELECT `name`,city,zone,capacity,longitude,latitude,field_key, vlevel
            FROM
                sys_station_tab
            WHERE in_service = 1
            AND field_key IN ( 'wind', 'solar', 'wind_solar' ) 
            AND vlevel >= {110 * 0.9}
            AND city = "{item.name}"
            """
    sql_resp = db.execute(text(sql)).fetchall()
    station_list = []
    for obj in sql_resp:
        station_dict = {
            "name": obj[0],
            "city": obj[1],
            "zone": obj[2],
            "capacity": obj[3],
            "longitude": obj[4],
            "latitude": obj[5],
            "type": obj[6],
            "vlevel": obj[7]
        }
        station_list.append(station_dict)
    result.data = station_list
    return result


@router.post("/history/interface/line", tags=[common.HENAN_TAG], response_model=data.Response)
def history_interface_line(
        item: params.HistoryInterfaceLineParams,
        result: data.Response = Depends(utils.response_obj),
        db=Depends(utils.get_db)
):
    """
    获取电网运行数据-断面-线路
    :param result:
    :return:
    """
    if not item.name:
        # 未传入断面名称，则从数据库中获取第一个断面名称
        sql = f"""
            SELECT
            inf_name, vlevel, inf_limit, inf_type, zone
            FROM sys_qs_interface_tab
            WHERE in_service = 1
            ORDER BY vlevel DESC, inf_limit DESC
            LIMIT 1
        """
        first_interface = db.execute(text(sql)).fetchone()
        if not first_interface:
            result.data = []
            return result
        item.name = first_interface[0]
        inf_type = first_interface[3]
        zone = first_interface[4]
    else:
        # 传销断面所属类型
        sql = f"""
            SELECT
            inf_name, inf_type, zone
            FROM sys_qs_interface_tab
            WHERE in_service = 1
            AND (inf_name = "{item.name}") OR (zone = "{item.name}")
            LIMIT 1
        """
        first_interface = db.execute(text(sql)).fetchone()
        inf_type = first_interface[1]
        zone = first_interface[2]
    with open(common.inf_define_file, "r") as fp:
        content = yaml.safe_load(fp.read())
        if inf_type in ["line_inf", "zone220_inf"]:
            line_list = list(content.get(inf_type, {}).get(zone, {}).get(item.name, {}).get("element", {}).keys())
        else:
            line_list = list(content.get(inf_type, {}).get(zone, {}).keys())
        result.data = {item.name: line_list}
    return result


@router.get("/interface/detail/data", tags=[common.HENAN_TAG], response_model=data.Response)
def interface_detail_data(
        case_id: str, name: str, is_short: Union[int, str] = 1, result: data.Response = Depends(utils.response_obj)
):
    """
    获取断面详细数据
    :param case_id: 算例id
    :param name: 断面名称
    :param is_short: 1: 长期算例, 2: 短期算例
    :param result: 返回值
    :return:
    """
    if isinstance(is_short, str):
        is_short = int(is_short)
    if is_short == 1:
        cache_dict = common.case_dict
    else:
        cache_dict = common.short_case_dict
    if case_id in cache_dict:
        case = cache_dict.get(case_id, {})
    else:
        read_resp = utils.read_case(case_id=case_id, is_short=is_short)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result
    network = case.get("network", {})
    output = case.get('output', {})
    interface_df = network['interface']
    interface_row_df = interface_df.loc[interface_df['name'] == name]
    interface_index = interface_row_df.index[0]
    max_p_mw = interface_row_df.loc[interface_index, 'max_p_mw'] * 0.1
    interface_power = output['interface_power'][interface_index] * 0.1
    interface_power = np.round(interface_power, decimals=2).tolist()
    result.data = {"limit": float(max_p_mw), "power": interface_power}
    return result


@router.get("/case/initoff/gens", tags=[common.HENAN_TAG], response_model=data.Response)
def case_initoff_gens(
        case_id: str, is_short: Union[int, str] = 2, result: data.Response = Depends(utils.response_obj)
):
    """
    针对QS文件中或者一开始就关停无法开机的机组查询(在case的'off_gen'机组里,如果有)
    :param case_id: 算例id
    :param is_short: 1: 长期算例, 2: 短期算例
    :param result: 返回值
    :return:
    """
    if isinstance(is_short, str):
        is_short = int(is_short)
    if is_short == 1:
        cache_dict = common.case_dict
    else:
        cache_dict = common.short_case_dict
    if case_id in cache_dict:
        case = cache_dict.get(case_id, {})
    else:
        read_resp = utils.read_case(case_id=case_id, is_short=is_short)
        if read_resp.code != 200:
            return read_resp
        case = read_resp.data
    status = case.get('status')
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    if status != 4:
        result.code = 1002
        result.msg = '暂无启用的算例数据'
        return result
    network = case.get("network", {})
    data = data_utils.get_case_initoff_gens(case_net=network)
    data_list = []
    for obj in data.values():
        obj['dev_id'] = str(obj['dev_id'])
        data_list.append(obj)
    result.data = data_list
    return result


@router.get("/short/analyze/case", tags=[common.HENAN_TAG], response_model=data.Response)
def short_analyze_case(result: data.Response = Depends(utils.response_obj)):
    """
    分析中短期算例文件
    :param result: 返回值
    :return:
    """
    utils.auto_analyze_case()
    return result


@router.get("/now/generate/case", tags=[common.HENAN_TAG], response_model=data.Response)
def now_generate_case(result: data.Response = Depends(utils.response_obj), db=Depends(utils.get_db)):
    """
    获取立即生成中短期算例状态
    :param result:
    :return:
    """
    short_qs_obj = db.query(SysShortQs).filter(SysShortQs.is_delete == 0).first()
    if short_qs_obj:
        if short_qs_obj.value == 1:
            value = 1
        else:
            value = 0
    else:
        value = 0
    result.data = {"value": value}
    return result


@router.post("/set/generate/case", tags=[common.HENAN_TAG], response_model=data.Response)
def set_generate_case(result: data.Response = Depends(utils.response_obj), db=Depends(utils.get_db)):
    """
    设置立即生成中短期算例
    :param result:
    :return:
    """
    short_qs_obj = db.query(SysShortQs).filter(SysShortQs.is_delete == 0).first()
    if short_qs_obj:
        short_qs_obj.value = 1
    else:
        short_qs_obj = SysShortQs(value=1)
        db.add(short_qs_obj)
    return result


@router.get("/realtime/feedin/data", tags=[common.HENAN_TAG], response_model=data.Response)
def realtime_feedin_data(result: data.Response = Depends(utils.response_obj), db=Depends(utils.get_db)):
    """
    实时模块，获取区外交流和区外直流
    :param result:
    :return:
    """
    nowtime = dt.datetime.now()
    nowdate = nowtime.date()
    all_list = ["天中直流总加", "灵宝直流", "青豫本侧", "AGC特高压长南-南荆（网调）", "鄂豫交换有功总加", "受入总加"]
    dc_list = ["天中直流总加", "灵宝直流", "青豫本侧"]
    ac_list = ["AGC特高压长南-南荆（网调）", "鄂豫交换有功总加"]
    dc_sql = f"""
    SELECT
    rdd.value, rdd.time, rdi.field_key, rdi.display_desc
    FROM sys_real_day_data_tab rdd
    LEFT JOIN sys_real_day_index_tab rdi ON rdi.id = rdd.power_id
    WHERE rdi.day_time = "{str(nowdate)}"
    AND rdi.field_key = 'feedin'
    AND rdi.dev_id in (SELECT
    dev_id
    FROM sys_dev_id_map_tab
    WHERE field_key = 'feedin'
    AND display_desc in {tuple(all_list)})
    ORDER BY rdd.time ASC
    """
    sql_resp = db.execute(text(dc_sql)).fetchall()
    df = pd.DataFrame(sql_resp, columns=['value', 'time', 'field_key', 'display_desc'])
    df['value'] = df['value'] * 0.1
    time_range = df.drop_duplicates(subset="time")['time'].astype(str).tolist()
    dc_dict = {}
    ac_dict = {}
    for display_desc in dc_list:
        df1 = df.loc[df['display_desc'] == display_desc]
        value_list = df1['value'].tolist()
        if len(value_list) == 0:
            value_list = [0] * len(time_range)
        if display_desc == "天中直流总加":
            display_desc = "天中直流"
        dc_dict[display_desc] = value_list

    for display_desc in ac_list:
        df1 = df.loc[df['display_desc'] == display_desc]
        if display_desc == "AGC特高压长南-南荆（网调）":
            df2 = df.groupby('time').apply(
                lambda group:
                # 提取“受入总加”的value（假设每组仅有一个“受入总加”）
                group[group['display_desc'] == '受入总加']['value'].sum()
                # 减去其他display_desc的value总和
                - group[group['display_desc'].isin(["天中直流总加", "灵宝直流", "青豫本侧", "鄂豫交换有功总加"])][
                    'value'].sum()
            ).reset_index(name='value')
            display_desc = "长南"
            value_list = df2['value'].tolist()
            ac_dict[display_desc] = value_list
            continue
        elif display_desc == "鄂豫交换有功总加":
            display_desc = "鄂豫"
        value_list = df1['value'].tolist()
        if len(value_list) == 0:
            value_list = [0] * len(time_range)
        ac_dict[display_desc] = value_list
    result.data = {"dc": dc_dict, "ac": ac_dict, "time_range": time_range}
    return result


@router.post("/history/load/trend", tags=[common.HENAN_TAG], response_model=data.Response)
def history_load_trend(
        item: params.HistoryLoadTrendParams,
        result: data.Response = Depends(utils.response_obj),
        db=Depends(utils.get_db)
):
    """
    历史数据-负荷数据-负荷趋势曲线图
    :param result:
    :return:
    """
    nowtime = dt.datetime.now()
    data_dict = {}
    # 查询电源
    if not item.stime:
        # 电源最近一年的开始时间
        gen_stime_sql = f"""
        SELECT
        data_year, day_time
        FROM sys_power_data_tab
        ORDER BY data_year DESC, day_time ASC
        LIMIT 1
        """
        gen_stime_resp = db.execute(text(gen_stime_sql)).fetchone()
        if gen_stime_resp or len(gen_stime_resp) >= 1:
            data_year_stime = gen_stime_resp[0]
            day_stime = gen_stime_resp[1]
        else:
            item.stime = f"{nowtime.year}-01-01 00:00:00"
            data_year_stime = nowtime.year
            day_stime = "01-01"
    else:
        data_year_stime = item.stime.split("-")[0]
        day_stime = "-".join(item.stime.split(" ")[0].split("-")[1:])
    if not item.etime:
        # 电源最近一年的结束时间
        gen_etime_sql = f"""
        SELECT
        data_year, day_time
        FROM sys_power_data_tab
        ORDER BY data_year DESC, day_time DESC
        LIMIT 1
        """
        gen_etime_resp = db.execute(text(gen_etime_sql)).fetchone()
        if gen_etime_resp or len(gen_etime_resp) >= 1:
            data_year_etime = gen_etime_resp[0]
            day_etime = gen_etime_resp[1]
        else:
            item.etime = f"{nowtime.year}-12-31 23:59:59"
            data_year_etime = nowtime.year
            day_etime = "12-31"
    else:
        data_year_etime = item.etime.split("-")[0]
        day_etime = "-".join(item.etime.split(" ")[0].split("-")[1:])
    # 场景时间
    if item.scene_date:
        data_year_stime = item.scene_date.split("-")[0]
        data_year_etime = item.scene_date.split("-")[0]
        day_stime = "-".join(item.scene_date.split('-')[1:]).split(" ")[0]
        day_etime = "-".join(item.scene_date.split('-')[1:]).split(" ")[0]
    # 查询负荷
    if item.type == "全网":
        field_key = "load"
    else:
        field_key = "load_grid"
    time_range = []
    if item.area in ['豫西', '豫中东', '豫南', '豫北']:
        dev_id, citys = utils.get_big_area_dev_id_or_citys(area=item.area, field_key=field_key)
        if dev_id:
            load_sql = f"""
            SELECT
            sload.*
            FROM sys_load_data_tab sload 
            WHERE sload.field_key = "{field_key}"
            AND sload.data_year >= {data_year_stime}
            AND sload.data_year <= {data_year_etime}
            AND sload.day_time >= '{day_stime}'
            AND sload.day_time <= '{day_etime}'
            AND sload.dev_id = "{dev_id}"
            AND sload.area_name = "{item.area}"
            """
            load_sql_resp = db.execute(text(load_sql)).fetchall()
            load_df = pd.DataFrame(load_sql_resp)
            load_df = utils.process_sql_resp(load_df)
            if load_df.empty:
                data_dict['load'] = []
            else:
                time_range = load_df.drop_duplicates(subset="时间")['时间'].astype(str).tolist()
                data_dict['load'] = (load_df['value'] * 0.1).tolist()
        else:
            base_df: Optional[pd.DataFrame] = None
            for city in citys:
                dev_id = utils.get_dev_id(area=city, field_key=field_key)
                if not dev_id:
                    continue
                load_sql = f"""
                SELECT
                sload.*
                FROM sys_load_data_tab sload 
                WHERE sload.field_key = "{field_key}"
                AND sload.data_year >= {data_year_stime}
                AND sload.data_year <= {data_year_etime}
                AND sload.day_time >= '{day_stime}'
                AND sload.day_time <= '{day_etime}'
                AND sload.dev_id = "{dev_id}"
                """
                load_sql_resp = db.execute(text(load_sql)).fetchall()
                load_df = pd.DataFrame(load_sql_resp)
                load_df = utils.process_sql_resp(load_df)
                if load_df.empty:
                    continue
                if base_df is None:
                    base_df = load_df
                else:
                    base_df = pd.concat([base_df, load_df], axis=0, ignore_index=True)
            if isinstance(base_df, pd.DataFrame) and not base_df.empty:
                base_df = base_df.groupby(['时间']).sum().reset_index()
                time_range = base_df.drop_duplicates(subset="时间")['时间'].astype(str).tolist()
                data_dict['load'] = (base_df['value'] * 0.1).tolist()
            else:
                data_dict["load"] = []
    else:
        # 查询测点id
        dev_id = utils.get_dev_id(area=item.area, field_key=field_key)
        if dev_id:
            load_sql = f"""
            SELECT
            sload.*
            FROM sys_load_data_tab sload 
            WHERE sload.field_key = "{field_key}"
            AND sload.data_year >= {data_year_stime}
            AND sload.data_year <= {data_year_etime}
            AND sload.day_time >= '{day_stime}'
            AND sload.day_time <= '{day_etime}'
            AND sload.dev_id = "{dev_id}"
            """
            load_sql_resp = db.execute(text(load_sql)).fetchall()
            load_df = pd.DataFrame(load_sql_resp).fillna(0)
            if load_df.empty:
                data_dict['load'] = []
            else:
                load_df = utils.process_sql_resp(load_df)
                load_df['value'] = load_df['value'] * 0.1
                time_range = load_df.drop_duplicates(subset="时间")['时间'].astype(str).tolist()
                data_dict['load'] = load_df['value'].tolist()
        else:
            data_dict['load'] = []
    # 查询feedin, 仅在全省区域下返回这个数据
    if item.area == "全省":
        feedin_sql = f"""
        SELECT
        feedin.*
        FROM sys_feedin_data_tab feedin
        WHERE feedin.dev_id = "121878665603580823"
        AND feedin.data_year >= {data_year_stime}
        AND feedin.data_year <= {data_year_etime}
        AND feedin.day_time >= '{day_stime}'
        AND feedin.day_time <= '{day_etime}'
        """
        feedin_sql_resp = db.execute(text(feedin_sql)).fetchall()
        feedin_df = pd.DataFrame(feedin_sql_resp)
        if feedin_df.empty:
            data_dict['feedin'] = []
        else:
            feedin_df = utils.process_sql_resp(feedin_df)
            data_dict['feedin'] = (feedin_df['value'] * 0.1).tolist()
            time_range = feedin_df.drop_duplicates(subset="时间")['时间'].astype(str).tolist()
    # 查询电源field_key
    gen_field_key_sql = f"""
    SELECT
    DISTINCT field_key
    FROM sys_power_data_tab
    """
    gen_field_key_resp = db.execute(text(gen_field_key_sql)).fetchall()
    if not gen_field_key_resp or len(gen_field_key_resp) == 0:
        result.code = 1100
        result.msg = "暂无数据"
        return result
    for obj in gen_field_key_resp:
        gen_field_key = obj[0]
        if gen_field_key in ['gen_all']:
            continue
        if item.area in ['豫西', '豫中东', '豫南', '豫北']:
            dev_id, citys = utils.get_big_area_dev_id_or_citys(area=item.area, field_key=gen_field_key)
            if dev_id:
                gen_sql = f"""
                SELECT
                spower.*
                FROM sys_power_data_tab spower
                WHERE spower.field_key = "{gen_field_key}"
                AND spower.data_year >= {data_year_stime}
                AND spower.data_year <= {data_year_etime}
                AND spower.day_time >= '{day_stime}'
                AND spower.day_time <= '{day_etime}'
                AND spower.dev_id = "{dev_id}"
                AND spower.dev_id = "{dev_id}"
                """
                gen_sql_resp = db.execute(text(gen_sql)).fetchall()
                gen_df = pd.DataFrame(gen_sql_resp)
                gen_df = utils.process_sql_resp(gen_df)
                if gen_df.empty:
                    data_dict[gen_field_key] = []
                else:
                    time_range = gen_df.drop_duplicates(subset="时间")['时间'].astype(str).tolist()
                    data_dict[gen_field_key] = (gen_df['value'] * 0.1).tolist()
            else:
                base_df: Optional[pd.DataFrame] = None
                for city in citys:
                    dev_id = utils.get_dev_id(area=city, field_key=gen_field_key)
                    if not dev_id:
                        continue
                    gen_sql = f"""
                    SELECT
                    spower.*
                    FROM sys_power_data_tab spower
                    WHERE spower.field_key = "{gen_field_key}"
                    AND spower.data_year >= {data_year_stime}
                    AND spower.data_year <= {data_year_etime}
                    AND spower.day_time >= '{day_stime}'
                    AND spower.day_time <= '{day_etime}'
                    AND spower.dev_id = "{dev_id}"
                    """
                    gen_sql_resp = db.execute(text(gen_sql)).fetchall()
                    gen_df = pd.DataFrame(gen_sql_resp)
                    gen_df = utils.process_sql_resp(gen_df)
                    if gen_df.empty:
                        continue
                    if base_df is None:
                        base_df = gen_df
                    else:
                        base_df = pd.concat([base_df, gen_df], axis=0, ignore_index=True)
                if isinstance(base_df, pd.DataFrame) and not base_df.empty:
                    base_df = base_df.groupby(['时间']).sum().reset_index()
                    time_range = base_df.drop_duplicates(subset="时间")['时间'].astype(str).tolist()
                    data_dict[gen_field_key] = (base_df['value'] * 0.1).tolist()
                else:
                    data_dict[gen_field_key] = []
        else:
            dev_id = utils.get_dev_id(area=item.area, field_key=gen_field_key)
            if dev_id:
                gen_sql = f"""
                SELECT
                spower.*
                FROM sys_power_data_tab spower
                WHERE spower.dev_id = "{dev_id}"
                AND spower.data_year >= {data_year_stime}
                AND spower.data_year <= {data_year_etime}
                AND spower.day_time >= '{day_stime}'
                AND spower.day_time <= '{day_etime}'
                AND spower.field_key = "{gen_field_key}"
                """
                gen_sql_resp = db.execute(text(gen_sql)).fetchall()
                gen_df = pd.DataFrame(gen_sql_resp)
                if gen_df.empty:
                    data_dict[gen_field_key] = []
                else:
                    gen_df = utils.process_sql_resp(gen_df)
                    data_dict[gen_field_key] = (gen_df['value'] * 0.1).tolist()
                    time_range = gen_df.drop_duplicates(subset="时间")['时间'].astype(str).tolist()
            else:
                data_dict[gen_field_key] = []
    if not time_range:
        nowdate = nowtime.date()
        stime = dt.datetime.combine(nowdate, dt.time.min)
        time_range = pd.date_range(start=stime, end=nowtime, freq="5T")
    time_range_len = len(time_range)
    for key in list(data_dict.keys()):
        value_list = data_dict[key]
        if len(value_list) < time_range_len:
            data_dict[key] += [0] * (time_range_len - len(value_list))
    data_dict['time_range'] = time_range
    result.data = data_dict
    return result


@router.post("/history/load/update/max", tags=[common.HENAN_TAG], response_model=data.Response)
def history_load_update_max(
        item: params.HistoryLoadUpdateMaxParams,
        result: data.Response = Depends(utils.response_obj),
        db=Depends(utils.get_db)
):
    """
    历史数据-负荷数据-修改最大负荷点的数据
    :param result:
    :return:
    """
    # 处理时间格式
    if isinstance(item.time, dt.datetime):
        date_str = str(item.time.date())
        time_list = pd.date_range(
            start=f"{date_str} 00:00:00", end=f"{date_str} 23:59:59", freq="5T"
        ).astype(str).tolist()
        if str(item.time) not in time_list:
            result.code = 1002
            result.msg = "时间点不存在"
            return result
        year = item.time.year
        day_time = "-".join(date_str.split("-")[1:])
        column_name = f"value_{str(item.time.hour).zfill(2)}{str(item.time.minute).zfill(2)}"
    else:
        result.code = 1002
        result.msg = "时间格式错误"
        return result
    # 获取测点id
    dev_id = utils.get_dev_id(area=item.area, field_key=item.field_key)
    if not dev_id:
        result.code = 1003
        result.msg = "未查询到测点id"
        return result
    sql = f"""
    SELECT
    {column_name}
    FROM sys_load_data_tab
    WHERE data_year = {year}
    AND day_time = "{day_time}"
    AND field_key = '{item.field_key}'
    AND dev_id = '{dev_id}'
    AND area_name = '{item.area}'
    LIMIT 1
    """
    # 查询对应负荷的时间点是否存在
    sql_resp = db.execute(text(sql)).fetchone()
    if sql_resp:
        update_sql = f"""
        UPDATE sys_load_data_tab SET {column_name} = {item.new_value}
        WHERE data_year = {year}
        AND day_time = "{day_time}"
        AND dev_id = '{dev_id}'
        AND area_name = '{item.area}'
        AND field_key = '{item.field_key}'
        """
        db.execute(text(update_sql))
        result.msg = "更新成功"
    else:
        result.code = 1002
        result.msg = "不存在当前时间点数据"
    return result


@router.post("/user/login", tags=[common.HENAN_TAG], response_model=data.Response)
def user_login(
        item: params.UserLoginParams, result: data.Response = Depends(utils.response_obj), db=Depends(utils.get_db)
):
    """
    用户登录
    :param result:
    :return:
    """
    sql = f"""
    SELECT
    user_name, passwd, id
    FROM sys_user_tab
    WHERE user_name = "{item.username}"
    LIMIT 1
    """
    sql_resp = db.execute(text(sql)).fetchone()
    if not sql_resp:
        result.code = 1002
        result.msg = "用户名不存在"
        return result
    passwd = sql_resp[1]
    if item.password != passwd:
        result.code = 1003
        result.msg = "密码错误"
        return result
    token = str(uuid.uuid4()).replace("-", "")
    last_login_time = utils.get_current_datetime()
    expire_time = dt.datetime.strptime(last_login_time,
                                       "%Y-%m-%d %H:%M:%S") + dt.timedelta(hours=common.user_login_expire_time)
    user_id = sql_resp[2]
    token_id = str(uuid.uuid4())
    save_sql = f"""
    INSERT INTO sys_user_login(id, userid, last_login_time, token) VALUES('{token_id}', '{user_id}', '{last_login_time}', '{token}')
    """
    db.execute(text(save_sql))
    # 保存数据库
    result.data = {"token": token, "expire_time": str(expire_time)}
    result.msg = "登录成功"
    return result


@router.get("/system/log", tags=[common.HENAN_TAG], response_model=data.Response)
def system_log(path: str):
    """
    获取日志
    :param result:
    :return:
    """

    if not os.path.isdir(path):
        raise Exception("目录不存在")
    filename = os.path.basename(path)
    zip_path = f"{filename}.zip"
    if os.path.isfile(zip_path):
        os.remove(zip_path)
    with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(path):
            for file in files:
                file_path = os.path.join(root, file)
                # 计算相对于指定目录的相对路径
                relative_path = os.path.relpath(file_path, path)
                zipf.write(file_path, relative_path)
    return FileResponse(zip_path, filename=filename, media_type="application/json")


@router.post("/history/feedin/data", tags=[common.HENAN_TAG], response_model=data.Response)
def history_feedin_data(
        item: params.HistoryAcDcParams, result: data.Response = Depends(utils.response_obj), db=Depends(utils.get_db)
):
    """
    历史模块，获取区外交流和区外直流
    :param year: 年份
    :param result:
    :return:
    """
    start_year = item.stime.year
    end_year = item.etime.year
    start_day_time = f"{item.stime.month:02d}-{item.stime.day:02d}"
    end_day_time = f"{item.etime.month:02d}-{item.etime.day:02d}"
    all_list = ["天中直流总加", "灵宝直流", "青豫本侧", "AGC特高压长南-南荆（网调）", "鄂豫交换有功总加", "受入总加"]
    dc_list = ["天中直流总加", "灵宝直流", "青豫本侧"]
    sql = f"""
    SELECT
    *
    FROM sys_feedin_data_tab
    WHERE data_year >= {start_year}
    AND data_year <= {end_year}
    AND day_time >= '{start_day_time}'
    AND day_time <= '{end_day_time}'
    AND display_desc in {tuple(all_list)}
    ORDER BY day_time ASC
    """
    sql_resp = db.execute(text(sql)).fetchall()
    df = pd.DataFrame(sql_resp)
    df = utils.process_sql_resp(df=df, is_unit=True, is_filter=True, is_display_desc=True)
    dc_dict = {}
    ac_dict = {}
    time_range = df.drop_duplicates(subset="时间")['时间'].astype(str).tolist()
    for display_desc in all_list:
        new_df = df.loc[df['display_desc'] == display_desc, ["时间", 'value']]
        if display_desc in dc_list:
            # 区外直流
            if display_desc == "天中直流总加":
                display_desc = "天中直流"
            if new_df.empty:
                dc_dict[display_desc] = [0] * len(time_range)
            else:
                dc_dict[display_desc] = new_df['value'].round(1).tolist()
        else:
            if display_desc == "AGC特高压长南-南荆（网调）":
                df2 = df.groupby('时间').apply(
                    lambda group:
                    # 提取“受入总加”的value（假设每组仅有一个“受入总加”）
                    group[group['display_desc'] == '受入总加']['value'].sum()
                    # 减去其他display_desc的value总和
                    - group[group['display_desc'].isin(["天中直流总加", "灵宝直流", "青豫本侧", "鄂豫交换有功总加"])][
                        'value'].sum()
                ).reset_index(name='value')
                display_desc = "长南"
                value_list = df2['value'].round(1).tolist()
                ac_dict[display_desc] = value_list
                continue
            elif display_desc == "鄂豫交换有功总加":
                display_desc = "鄂豫"
            if new_df.empty:
                ac_dict[display_desc] = [0] * len(time_range)
            else:
                ac_dict[display_desc] = new_df['value'].round(1).tolist()
    result.data = {"dc": dc_dict, "ac": ac_dict, "time_range": time_range}
    return result


@functools.lru_cache(maxsize=128)
def _get_cached_inf_data(sql: str, db_conn_str: str):
    # 使用连接字符串创建新的数据库连接
    db = next(utils.get_db_from_str(db_conn_str))
    return db.execute(text(sql)).fetchall()

@router.post("/interface/tide", tags=[common.HENAN_TAG], response_model=data.PageResponse)
def interface_tide(
    item: params.InterfacePowerParams,
    result: data.PageResponse = Depends(utils.page_response_obj),
    db=Depends(utils.get_db)
):
    """
    断面历史时刻功率查询;  输入参数：时间范围：
    查询表：sys_qs_inf_data_tab; + sys_qs_interface_tab; 断面台账表和断面历史功率表
    :param result:  df,name,display_desc,limit,power,loading_percent
    :return:
    """
    # 使用 utils.interface_tool 中的函数简化代码
    qs_inf_data_df = get_interface_tide_data(
        start_time=item.start_time,
        end_time=item.end_time,
        inf_type=item.inf_type,
        db_uri=db_uri,
        db=db
    )
    
    if not qs_inf_data_df.empty:
        # 分页处理
        total_records = len(qs_inf_data_df)
        start_index = (item.page - 1) * item.size
        end_index = start_index + item.size
        paged_data = qs_inf_data_df.iloc[start_index:end_index]

        result.data = paged_data.to_dict("records")
        result.total = total_records
        result.page = item.page
        result.size = item.size
        result.pages = (total_records + item.size - 1) // item.size if item.size > 0 else 0
    else:
        result.data = []
        result.page = item.page
        result.size = item.size
        result.total = 0
        result.pages = 0

    return result


@router.post("/interface/tide/download", tags=[common.HENAN_TAG])
def interface_tide_excel(
    item: params.InterfacePowerParams,
    db=Depends(utils.get_db)
):
    """
    断面历史时刻功率查询并导出为Excel;  输入参数：时间范围：
    查询表：sys_qs_inf_data_tab; + sys_qs_interface_tab; 断面台账表和断面历史功率表
    不分页，导出所有数据为Excel表格
    :return: Excel文件下载
    """
    start_time = item.start_time
    end_time = item.end_time
    
    # 使用公共函数获取数据
    qs_inf_data_df = get_interface_tide_data(start_time, end_time, item.inf_type, db_uri, db)
    
    if qs_inf_data_df.empty:
        return {"message": "没有找到数据", "code": 404}
    
    # 导出到Excel
    filename = export_interface_data_to_excel(qs_inf_data_df)
    
    if filename is None:
        return {"message": "导出Excel失败", "code": 500}
    
    # 返回Excel文件
    return FileResponse(
        path=filename,
        filename=f"interface_data_{uuid.uuid4}.xlsx",
        media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    )


@router.get("/acline/trafo/qs/data", tags=[common.HENAN_TAG], response_model=data.Response)
def acline_trafo_qs_data(
        time_value: str, result: data.Response = Depends(utils.response_obj), db=Depends(utils.get_db)
):
    """
    交流线路、主变信息
    :param result:  df,name,display_desc,limit,power,loading_percent
    :return:
    """
    new_time = dt.datetime.strptime(time_value, "%Y-%m-%d %H:%M:%S")
    nowtime = dt.datetime.now()
    data_year = new_time.year
    day_time = f"{new_time.month:02d}-{new_time.day:02d}"
    # 线路
    if new_time.date() == nowtime.date():
        sql1 = f"""
        SELECT
        dit.field_key,dit.display_desc,dit.power_node,
        dit.day_time,ddt.time, ddt.`value`
        FROM sys_qs_line_real_day_data_tab ddt 
        LEFT JOIN sys_qs_line_real_day_index_tab dit ON dit.id = ddt.power_id
        WHERE field_key = "line_power_p"
        AND day_time = "{str(new_time.date())}"
        """
        resp1 = db.execute(text(sql1)).fetchall()
        df1 = pd.DataFrame(resp1)
        if not df1.empty:
            # 1. 转换日期列为datetime类型
            df1['day_time'] = pd.to_datetime(df1['day_time'])
            df1['time'] = pd.to_datetime(df1['time'])
            # 2. 拆分day_time：提取年份和月日
            df1['data_year'] = df1['day_time'].dt.year  # 新增年份列
            df1['day_time'] = df1['day_time'].dt.strftime('%m-%d')  # 月日格式如'07-13'
            # 3. 处理time列：生成时间代码（如00:05 → 'value_0005'）
            df1['time_code'] = 'value_' + df1['time'].dt.strftime('%H%M')  # 提取小时和分钟，格式化为HHMM
            # 4. 生成完整的时间点列表（00:00到23:55，每5分钟一个）
            all_time_codes = [f'value_{h:02d}{m:02d}' for h in range(24) for m in range(0, 60, 5)]
            # 5. 重塑为宽表，确保包含所有时间点列
            df1 = df1.pivot_table(
                index=['field_key', 'display_desc', 'power_node', 'data_year', 'day_time'],
                columns='time_code',
                values='value',
                aggfunc='first'
            ).reindex(
                columns=all_time_codes, fill_value=0
            ).reset_index()
    else:
        sql1 = f"""
        SELECT
        *
        FROM sys_qs_line_data_tab
        WHERE data_year = {data_year}
        AND day_time = "{day_time}"
        """
        resp1 = db.execute(text(sql1)).fetchall()
        df1 = pd.DataFrame(resp1)
        if not df1.empty:
            df1.drop(
                columns=['id', 'qs_file_name', 'area_name', 'create_time', 'update_time', 'is_delete', 'dev_id'],
                inplace=True
            )
            df1.fillna(0, inplace=True)
    # 主变
    if new_time.date() == nowtime.date():
        sql2 = f"""
        SELECT
        dit.field_key,dit.display_desc,dit.power_node,
        dit.day_time,ddt.time, ddt.`value`
        FROM sys_qs_trafo_real_day_data_tab ddt 
        LEFT JOIN sys_qs_trafo_real_day_index_tab dit ON dit.id = ddt.power_id
        WHERE field_key = "trafo_power_p"
        AND day_time = "{str(new_time.date())}"
        """
        resp2 = db.execute(text(sql2)).fetchall()
        df2 = pd.DataFrame(resp2)
        if not df2.empty:
            # 1. 转换日期列为datetime类型
            df2['day_time'] = pd.to_datetime(df2['day_time'])
            df2['time'] = pd.to_datetime(df2['time'])
            # 2. 拆分day_time：提取年份和月日
            df2['data_year'] = df2['day_time'].dt.year  # 新增年份列
            df2['day_time'] = df2['day_time'].dt.strftime('%m-%d')  # 月日格式如'07-13'
            # 3. 处理time列：生成时间代码（如00:05 → 'value_0005'）
            df2['time_code'] = 'value_' + df2['time'].dt.strftime('%H%M')  # 提取小时和分钟，格式化为HHMM
            # 4. 生成完整的时间点列表（00:00到23:55，每5分钟一个）
            all_time_codes = [f'value_{h:02d}{m:02d}' for h in range(24) for m in range(0, 60, 5)]
            # 5. 重塑为宽表，确保包含所有时间点列
            df2 = df2.pivot_table(
                index=['field_key', 'display_desc', 'power_node', 'data_year', 'day_time'],
                columns='time_code',
                values='value',
                aggfunc='first'
            ).reindex(
                columns=all_time_codes, fill_value=0
            ).reset_index()
    else:
        sql2 = f"""
        SELECT
        *
        FROM sys_qs_trafo_data_tab
        WHERE data_year = {data_year}
        AND day_time = "{day_time}"
        """
        resp2 = db.execute(text(sql2)).fetchall()
        df2 = pd.DataFrame(resp2)
        if not df2.empty:
            df2.drop(
                columns=['id', 'qs_file_name', 'area_name', 'create_time', 'update_time', 'is_delete', 'dev_id'],
                inplace=True
            )
            df2.fillna(0, inplace=True)
    result.data = {"line": df1.to_dict("records"), "trafo": df2.to_dict("records")}
    return result


@router.get("/qs/interface/zone/list", tags=[common.HENAN_TAG], response_model=data.Response)
def qs_interface_zone_list(result: data.Response = Depends(utils.response_obj), db=Depends(utils.get_db)):
    """
    区分电压-区域列表
    :param result:
    :return:
    """
    sql = f"""
    SELECT
    DISTINCT zone
    FROM sys_qs_interface_tab
    WHERE inf_type = "zone220_inf"
    """
    resp = db.execute(text(sql)).fetchall()
    zone_list = []
    for obj in resp:
        zone_list.append(obj[0])
    result.data = zone_list
    return result


@router.post("/qs/interface/level/data", tags=[common.HENAN_TAG], response_model=data.Response)
def qs_interface_level_data(
        item: params.ListInterfaceFiffLevelDataParams,
        result: data.Response = Depends(utils.response_obj),
        db=Depends(utils.get_db)
):
    """
    区分电压-获取不同的电压断面数据
    """
    s_day_time = "-".join(item.stime.split(" ")[0].split("-")[1:])
    e_day_time = "-".join(item.etime.split(" ")[0].split("-")[1:])
    s_year = int(item.stime.split(" ")[0].split("-")[0])
    e_year = int(item.etime.split(" ")[0].split("-")[0])
    sql = f"""
    SELECT
    idt.*, ift.inf_name,inf_desc,inf_limit,ift.inf_type, ift.vlevel, ift.zone, ift.sub_type
    FROM sys_qs_inf_data_tab idt
    LEFT JOIN sys_qs_interface_tab ift ON ift.id = idt.dev_id
    WHERE day_time >= "{s_day_time}"
    AND day_time <= "{e_day_time}"
    AND data_year >= {s_year}
    AND data_year <= {e_year}
    AND inf_type != ""
    """
    if item.zone:
        # 区域过滤
        sql += f"""
        AND inf_type = 'zone220_inf'
        AND zone = '{item.zone}'
        """
    resp = db.execute(text(sql)).fetchall()
    df = pd.DataFrame(resp)
    if not df.empty:
        result.data = calculate_section_statistics(df)
    return result


@router.get("/test", tags=[common.HENAN_TAG], response_model=data.Response)
def history_test(result: data.Response = Depends(utils.response_obj), db=Depends(utils.get_db)):
    """
    测试
    :param year: 年份
    :param result:
    :return:
    """
    from utils.scheduler import real_analysis_interface_from_qs
    real_analysis_interface_from_qs()
    return result
