import datetime as dt
import json
import os
import shutil
import threading
import time
import uuid
from dataclasses import asdict
from datetime import datetime
from typing import Dict

import aiofiles
import pandas as pd
from fastapi import APIRouter
from fastapi import Body
from fastapi import Depends
from fastapi import File
from fastapi import Form
from fastapi import Query
from fastapi import UploadFile
from loguru import logger
from starlette.responses import FileResponse

import common
import data
import utils
from alg.ssmetrics_main import TeapAna_main
from data import params
from database.models import *

router = APIRouter()


@router.post('/file/upload', response_model=data.Response)
def upload_file(file: UploadFile, result: data.Response = Depends(utils.response_obj)):
    """
    上传临时文件
    :param result: 返回值
    :return:
    """
    content = file.file.read()
    # 判断临时目录是否存在，若是不存在，则创建
    if not os.path.isdir(common.file_tmp_storage):
        os.makedirs(common.file_tmp_storage)
    # 文件名称添加uuid前缀，防止文件名称重复
    key = str(uuid.uuid4())
    # 保存之后的文件名称
    new_filename = f"{key}_{file.filename}"
    # 文件存放路径
    new_file = os.path.join(common.file_tmp_storage, new_filename)
    with open(new_file, 'wb') as fp:
        fp.write(content)
    result.data = {
        'id': key,  # 文件对应的key,也是主键id
        'filename': file.filename,  # 文件名称
        'content_type': file.content_type,  # 文件类型
        'size':
            file.size  # 文件大小
    }
    return result


@router.post("/file/upload/init", response_model=data.Response)
async def upload_file_init(result: data.Response = Depends(utils.response_obj)):
    """
    文件分片上传, 初始化文件id
    :param result: 返回值
    :return:
    """
    file_id = str(uuid.uuid4())
    path = os.path.join(common.file_tmp_storage, file_id)
    if not os.path.exists(path):
        os.makedirs(path)
    result.data = {'file_id': file_id}
    return result


@router.post("/file/upload/slice", response_model=data.Response)
async def upload_file_slice(
    file_id: str = Body(..., description="文件唯一标识符"),
    number: int = Body(default=0, description="文件分片序号(初值为0)"),
    file: UploadFile = File(..., description="文件"),
    result: data.Response = Depends(utils.response_obj)
):
    """
    文件分片上传
    :param file_id: 文件id
    :param number: 文件分片序号
    :param file: 文件块内容
    :param result: 返回值
    :return:
    """
    path = os.path.join(common.file_tmp_storage, file_id)
    if not os.path.exists(path):
        os.makedirs(path)
    file_name = os.path.join(path, f'{number}')
    if not os.path.exists(file_name):
        async with aiofiles.open(file_name, 'wb') as f:
            await f.write(await file.read())
    result.data = {'file_id': file_id, 'chunk': f'{file_id}_{number}', 'number': number}
    return result


@router.post("/file/upload/merge", response_model=data.Response)
def merge_file(
    name: str = Body(..., description="文件名称（不含后缀）"),
    file_id: str = Body(..., description="文件唯一标识符"),
    result: data.Response = Depends(utils.response_obj)
):
    """合并分片文件"""
    target_file_name = os.path.join(common.file_tmp_storage, f'{file_id}_{name}')
    path = os.path.join(common.file_tmp_storage, file_id)
    try:
        with open(target_file_name, 'wb+') as target_file:  # 打开目标文件
            for i in range(len(os.listdir(path))):
                temp_file_name = os.path.join(path, f'{i}')
                with open(temp_file_name, 'rb') as temp_file:  # 按序打开每个分片
                    data = temp_file.read()
                    target_file.write(data)  # 分片内容写入目标文件
    except Exception as e:
        result.code = 500
        result.msg = f'合并失败：{e}'
        return result
    shutil.rmtree(path)  # 删除临时目录
    result.data = {'name': f'{name}'}
    return result


@router.post('/case/save', response_model=data.Response)
def save_case(item: params.SaveCaseParams, result: data.Response = Depends(utils.response_obj)):
    """
    新增算例
    :param result: 返回值
    :return:
    """
    db_data = utils.get_db_data(is_short=item.is_short)
    # 检查predict时间不可重复, predict字段 仅限河南保供存在，其他则为空list
    if item.predict:
        params_stime = item.predict[0]
        s1 = datetime.strptime(params_stime, '%Y-%m-%d %H:%M:%S').date()
        for _, case in db_data.items():
            other_stime = case.get("predict", [])
            if other_stime:
                other_stime = case['predict'][0]
                s2 = datetime.strptime(other_stime, '%Y-%m-%d %H:%M:%S').date()
                if s1 == s2:
                    result.msg = '该预测时段算例已存在'
                    result.code = 1003
                    return result
    # 将文件从tmp目录中移至到files目录下
    file_name = f"{item.id}_{item.filename}"
    f_src = os.path.join(common.file_tmp_storage, file_name)
    f_dst = os.path.join(common.file_storage, file_name)
    # 将数据转为dict
    data = asdict(item)
    # 获取当前时间
    nowtime = utils.get_current_datetime()
    data['create_time'] = nowtime  # 创建时间
    data['update_time'] = nowtime  # 更新时间
    data['is_delete'] = 0  # 逻辑删除，0:未删除，1:已删除
    data['status'] = 0  # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    db_data[item.id] = data
    utils.save_db_data(db_data, is_short=item.is_short)
    result.msg = "保存成功"
    # 移动文件
    shutil.move(f_src, f_dst)
    # 清空临时文件夹
    shutil.rmtree(common.file_tmp_storage)
    os.mkdir(common.file_tmp_storage)
    return result


@router.get('/case/list', response_model=data.Response)
def list_case(
    page: int = Query(1),
    size: int = Query(20),
    is_short: int = Query(1),
    result: data.Response = Depends(utils.response_obj)
):
    """
    获取算例列表
    :param page: 页码， 默认为1
    :param size: 每页数量， 默认为20
    :param is_short: 1: 长期算例, 2: 中短期算例，仅限河南存在
    :param result: 返回值
    :return:
    """
    total = 0
    if is_short == 1:
        db_file = common.db_path
    else:
        db_file = common.db_short_file
    if not os.path.isfile(db_file):
        result.data = {'total': total, 'cases': []}
        return result
    db_data = utils.get_db_data(is_short=is_short)
    total = len(db_data)
    case_list = []
    for case_id, case in db_data.items():
        create_time = case.get('create_time')
        update_time = case.get('update_time')
        new_case = {
            'id': case_id,
            'name': case.get('name', ''),
            'filename': case.get("filename", ''),
            'year': case.get('year', 0),
            'status': case.get('status', 0),  # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
            'create_time': create_time,
            'update_time': update_time,
            'size': utils.calculate_size(case.get("size", 0)),
            'content_type': case.get("content_type", ''),
            'comment': case.get("comment", ''),
            'start_time': str(case.get("start_time")),
            'predict': case.get("predict", [])
        }
        case_list.append(new_case)
    result.data = {'total': total, 'cases': case_list}
    return result


@router.post('/case/update', response_model=data.Response)
def update_case(item: params.UpdateCaseParams, result: data.Response = Depends(utils.response_obj)):
    """
    更新算例
    :param item -> case_id: 算例id
    :param item -> name: 算例名称
    :param result: 返回值
    :return:
    """
    # 获取当前时间
    nowtime = utils.get_current_datetime()
    db_data = utils.get_db_data(is_short=item.is_short)
    for case_id, case in db_data.items():
        if case_id != item.case_id and case['name'] == item.name:
            result.code = 1001
            result.msg = '名称已存在'
            return result
        # 检查predict时间不可重复, predict字段 仅限河南保供存在，其他则为空list
        if case_id != item.case_id and item.predict and case.get("predict"):
            params_stime = item.predict[0]
            other_stime = case['predict'][0]
            s1 = datetime.strptime(params_stime, '%Y-%m-%d %H:%M:%S').date()
            s2 = datetime.strptime(other_stime, '%Y-%m-%d %H:%M:%S').date()
            if s1 == s2:
                result.msg = '该预测时段算例已存在'
                result.code = 1003
                return result
    case = db_data.get(item.case_id)
    if not case:
        result.code = 1002
        result.msg = '未查询到算例信息'
        return result
    case['name'] = item.name
    case['year'] = item.year
    case['update_time'] = nowtime
    case['start_time'] = item.start_time
    case['comment'] = item.comment
    case['predict'] = item.predict
    db_data[item.case_id] = case
    utils.save_db_data(db_data, is_short=item.is_short)
    result.msg = '更新成功'
    return result


@router.post('/case/delete', response_model=data.Response)
def delete_case(item: params.DeleteCaseParams, result: data.Response = Depends(utils.response_obj)):
    """
    删除算例
    :param item -> case_id: 算例id
    :param result: 返回值
    :return:
    """
    db_data = utils.get_db_data(is_short=item.is_short)
    case = db_data.get(item.case_id)
    if not case:
        result.code = 1001
        result.msg = '未查询到待删除的算例数据'
        return result
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    status = case.get("status")
    if status == 1 and not item.debug:
        result.code = 1002
        result.msg = '算例正在分析中，禁止删除'
        return result
    # 删除成功时需要清除之前存储的算例文件
    file_name = case.get("filename")
    utils.clear_case_files(case_id=item.case_id, filename=file_name)
    del db_data[item.case_id]
    if len(db_data) == 0:
        if item.is_short == 1:
            db_file = common.db_path
        else:
            db_file = common.db_short_file
        if os.path.isfile(db_file):
            os.remove(db_file)
    else:
        utils.save_db_data(db_data, is_short=item.is_short)
    # 检查是否已经加入缓存，若是存在，则删除
    if item.case_id in common.case_dict:
        del common.case_dict[item.case_id]
    result.msg = '删除成功'
    return result


@router.post('/case/get_status', response_model=data.Response)
def get_case_status(item: params.GetStatusCaseParams, result: data.Response = Depends(utils.response_obj)):
    """
    获取算例状态信息
    :param ids: 主键列表
    :param result: 返回值
    :return:
    """
    db_data = utils.get_db_data(is_short=item.is_short)
    new_dict = {}
    for case_id in item.case_ids:
        case = db_data.get(case_id)
        if not case:
            result.code = 1002
            result.msg = '未查询到算例信息'
            return result
        # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
        new_dict[case_id] = case['status']
    result.data = new_dict
    return result


@router.post('/case/status', response_model=data.Response)
def case_status(item: params.StatusCaseParams, result: data.Response = Depends(utils.response_obj)):
    """
    获取算例状态信息, 仅限河南
    :param ids: 主键列表
    :param result: 返回值
    :return:
    """
    new_dict = {}
    for case in item.cases:
        db_data = utils.get_db_data(is_short=case.is_short)
        case_obj = db_data.get(case.case_id)
        if not case_obj:
            result.code = 1002
            result.msg = '未查询到算例信息'
            return result
        # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
        new_dict[case.case_id] = case_obj['status']
    result.data = new_dict
    return result


@router.post('/case/analyze', response_model=data.Response)
def analyse_case(item: params.AnalyzeCaseParams, result: data.Response = Depends(utils.response_obj)):
    """
    开始分析算例
    :param item: 参数
    :param result: 返回值
    :return:
    """
    # 获取锁
    common.lock.acquire()
    try:
        db_data = utils.get_db_data(is_short=item.is_short)
        case = db_data.get(item.case_id)
        if not case:
            result.code = 1001
            result.msg = '未查询到算例信息'
            return result
        # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
        status = case.get("status")
        if status == 1 and not item.debug:
            result.code = 1002
            result.msg = '算例已在分析中'
            return result
        file_name = case.get("filename")
        filename = f"{item.case_id}_{file_name}"
        f_dst = os.path.join(common.file_storage, filename)
        # 获取当前时间
        nowtime = utils.get_current_datetime()
        case['update_time'] = nowtime
        # 修改算例状态为1，分析中
        case['status'] = 1
        start_time = case.get("start_time")
        db_data[item.case_id] = case
        # 保存数据
        utils.save_db_data(db_data, is_short=item.is_short)
        # 生成分析的日志文件
        log_file = os.path.join(common.case_logs_dir, f'{item.case_id}.log')
        if not os.path.join(log_file):
            os.mknod(log_file)
        else:
            with open(log_file, "w+") as fp:
                fp.truncate()  # 清空日志文件内容
        # 检查当前算例是否已添加到缓存，若是存在，则清除当前算例缓存
        if item.case_id in common.case_dict:
            del common.case_dict[item.case_id]
        args = {
            'case_id': item.case_id,
            'case_file': f_dst,
            'case_address': item.area,
            'tofile': False,
            'case_start_time': f"{start_time} 00:00:00",
            'func': utils.callback_alg_save_data,
            'log_file': log_file,
            'is_short': item.is_short
        }
        logger.info(f"分析算例参数: {args}")
        if item.debug:
            TeapAna_main(**args)
        else:
            # 提交线程池，执行算例分析
            common.pool.submit(TeapAna_main, **args)
    finally:
        # 释放锁
        common.lock.release()
    return result


@router.post('/case/start', response_model=data.Response)
def start_case(item: params.StartCaseParams, result: data.Response = Depends(utils.response_obj)):
    """
    启用算例/取消启用算例
    :param item -> case_id: 算例id
    :param item -> status: 算例状态
    :param result: 返回值
    :return:
    """
    db_data = utils.get_db_data(is_short=item.is_short)
    case = db_data.get(item.case_id)
    if not case:
        result.code = 1001
        result.msg = '未查询到算例信息'
        return result
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    status = case.get('status')
    if item.status == 4 and status != 2:
        result.code = 1002
        result.msg = '算例尚未完成分析，不可启用'
        return result
    # 获取当前时间
    nowtime = utils.get_current_datetime()
    case['status'] = item.status
    case['update_time'] = nowtime
    db_data[item.case_id] = case
    if item.status == 4:
        if item.is_short == 2:
            # 中短期算例仅限启用一个，也仅限河南存在中短期算例
            for case_id, obj in db_data.items():
                if case_id == item.case_id:
                    continue
                if obj['status'] == 4:
                    # 如果是已经启用的，则变更为已完成分析
                    obj['status'] = 2
        else:
            # 获取系统设置的缓存算例的数量
            if not os.path.isfile(common.db_system_path):
                cache_number = 1
            else:
                with open(common.db_system_path, 'r') as fp:
                    content = fp.read()
                    if not content:
                        cache_number = 1
                    else:
                        contnet = json.loads(content)
                        cache_number = contnet.get('cache_number', 1)
            if len(common.case_dict) > cache_number:
                result.msg = f'最多同时启用{cache_number}个算例'
                result.code = 1002
                return result
        utils.save_db_data(db_data, is_short=item.is_short)
        # 添加缓存
        utils.read_case(case_id=item.case_id, is_network=True, is_output=True, is_short=item.is_short)
    else:
        # 清除当前算例缓存
        if item.is_short == 2:
            if item.case_id in common.short_case_dict:
                del common.short_case_dict[item.case_id]
        else:
            if item.case_id in common.case_dict:
                del common.case_dict[item.case_id]
        utils.save_db_data(db_data, is_short=item.is_short)
    result.msg = '启用算例'
    return result


@router.get("/case/download")
def download_case_file(case_id: str, is_short: int = 1, result: data.Response = Depends(utils.response_obj)):
    """
    下载算例文件
    :param case_id: 算例id
    :param is_short: 1: 长期算例, 2: 中短期算例，仅限河南存在
    :param result: 返回值
    :return:
    """
    case = utils.get_db_data(case_id=case_id, is_short=is_short)
    if not case:
        result.code = 1001
        result.msg = '未查询到算例信息'
        return result
    filename = case.get("filename")
    case_path = f"{common.file_storage}/{case_id}_{filename}"
    if not os.path.isfile(case_path):
        raise Exception(f"算例文件不存在")
    return FileResponse(case_path, filename=filename, media_type="application/json")


@router.get('/case/finish', response_model=data.Response)
def finish_case(case_id: str, is_short: int = 1, result: data.Response = Depends(utils.response_obj)):
    """
    把算例状态置为已完成分析
    :param case_id: 算例id
    :param result: 返回值
    :return:
    """
    db_data = utils.get_db_data(is_short=is_short)
    case = db_data.get(case_id)
    if not case:
        result.code = 1001
        result.msg = '未查询到算例信息'
        return result
    # 获取当前时间
    nowtime = utils.get_current_datetime()
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    case['status'] = 2
    case['update_time'] = nowtime
    db_data[case_id] = case
    utils.save_db_data(db_data, is_short=is_short)
    result.msg = '已将算例设置为已完成状态'
    return result


@router.get('/case/stop/analyse', response_model=data.Response)
def stop_analyse_case(case_id: str, is_short: int = 1, result: data.Response = Depends(utils.response_obj)):
    """
    停止分析算例
    :param case_id: 算例id
    :param result: 返回值
    :return:
    """
    db_data = utils.get_db_data(is_short=is_short)
    case = db_data.get(case_id)
    if not case:
        result.code = 1001
        result.msg = '未查询到算例信息'
        return result
    # 获取当前时间
    nowtime = utils.get_current_datetime()
    # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    case['status'] = 0
    case['update_time'] = nowtime
    db_data[case_id] = case
    utils.save_db_data(db_data, is_short=is_short)
    result.msg = '已将算例设置为未分析状态'
    return result


@router.get('/analyse/case/log', response_model=data.Response)
def analyse_case_log(case_id: str, result: data.Response = Depends(utils.response_obj)):
    """
    获取分析日志
    :param case_id: 算例id
    :param result: 返回值
    :return:
    """
    log_file = os.path.join(common.case_logs_dir, f'{case_id}.log')
    if not os.path.isfile(log_file):
        result.code = 1001
        result.msg = '未查询到算例分析日志'
        return result
    content = ""
    with open(log_file, 'r') as fp:
        content = fp.read()
    result.data = {"log": content}
    return result


@router.post('/system/setup', response_model=data.Response)
def system_setup(item: Dict, result: data.Response = Depends(utils.response_obj)):
    """
    系统设置
    :param item: 系统配置信息
    :param result: 返回值
    :return:
    """
    config = item.get("config", {})
    if config:
        full_name = config.get("full_name")
        if full_name:
            # 文件存放路径
            config_file = os.path.join(common.file_tmp_storage, full_name)
            if os.path.isfile(config_file):
                # 移动文件
                f_dst = os.path.join(common.db_config_path, full_name)
                shutil.move(config_file, f_dst)
            if os.path.isdir(common.file_tmp_storage):
                # 删除临时目录
                shutil.rmtree(common.file_tmp_storage)
    with open(common.db_system_path, 'w+') as fp:
        fp.write(json.dumps(item, indent=4, ensure_ascii=False))
    return result


@router.get('/system/setup/get', response_model=data.Response)
def system_setup_get(result: data.Response = Depends(utils.response_obj)):
    """
    获取系统系统设置
    :param result: 返回值
    :return:
    """
    result.data = {
        "cache_number": 1,
        'login_user': common.LOGIN_USER,
        'login_password': common.LOGIN_PASSWORD,
        'login_expire':
            common.LOGIN_EXPIRE  # 过期时间默认位1天，也就是次日凌晨1点
    }
    if os.path.isfile(common.db_system_path):
        with open(common.db_system_path, 'r') as fp:
            content = fp.read()
            if content:
                item = json.loads(content)
                if 'login_user' not in item:
                    item['login_user'] = common.LOGIN_USER
                if 'login_password' not in item:
                    item['login_password'] = common.LOGIN_PASSWORD
                if 'login_expire' not in item:
                    item['login_expire'] = common.LOGIN_EXPIRE
                result.data = item
    return result


@router.post('/config/file/upload', response_model=data.Response)
def config_file_upload(file: UploadFile, result: data.Response = Depends(utils.response_obj)):
    """
    配置文件替换/上传
    :param result: 返回值
    :return:
    """
    content = file.file.read()
    # 文件名称添加uuid前缀，防止文件名称重复
    key = str(uuid.uuid4())
    # 保存之后的文件名称
    new_filename = f"{key}_{file.filename}"
    if not os.path.isdir(common.file_tmp_storage):
        os.makedirs(common.file_tmp_storage, exist_ok=False)
    # 文件存放路径
    new_file = os.path.join(common.file_tmp_storage, new_filename)
    with open(new_file, 'wb') as fp:
        fp.write(content)
    result.data = {
        'id': key,  # 文件对应的key,也是主键id
        'filename': file.filename,  # 文件名称
        'full_name': new_filename
    }
    return result


@router.post('/case/replace/file', response_model=data.Response)
def replace_case_file(item: params.ReplaceCaseFileParams, result: data.Response = Depends(utils.response_obj)):
    """
    替换算例
    :param result: 返回值
    :return:
    """
    # 将文件从tmp目录中移至到files目录下
    file_name = f"{item.new_id}_{item.filename}"
    f_src = os.path.join(common.file_tmp_storage, file_name)
    f_dst = os.path.join(common.file_storage, file_name)
    db_data = utils.get_db_data(is_short=item.is_short)
    case = db_data.get(item.old_id)
    if not case:
        result.code = 1001
        result.msg = '未查询到算例信息'
        return result
    old_filename = case['filename']
    # 获取当前时间
    nowtime = utils.get_current_datetime()
    case['update_time'] = nowtime  # 更新时间
    case['status'] = 0  # 算例状态， 0: 未分析， 1:分析中，2:已分析，3: 分析失败, 4: 已启用
    case['filename'] = item.filename
    case['id'] = item.new_id
    case['size'] = item.size
    # 删除成功时需要清除之前存储的算例文件
    utils.clear_case_files(case_id=item.old_id, filename=old_filename)
    del db_data[item.old_id]
    db_data[item.new_id] = case
    utils.save_db_data(db_data, is_short=item.is_short)
    if item.old_id in common.case_dict:
        del common.case_dict[item.old_id]
    result.msg = "替换算例成功"
    # 移动文件
    shutil.move(f_src, f_dst)
    # 清空临时文件夹
    shutil.rmtree(common.file_tmp_storage)
    os.mkdir(common.file_tmp_storage)
    return result


@router.post('/login', response_model=data.Response)
def login(item: params.LoginParams, result: data.Response = Depends(utils.response_obj)):
    """
    登陆
    :param result: 返回值
    :return:
    """
    # 读取数据库用户信息
    login_user = common.LOGIN_USER
    login_password = common.LOGIN_PASSWORD
    if os.path.isfile(common.db_system_path):
        with open(common.db_system_path, 'r') as fp:
            content = fp.read()
            if content:
                login_dict = json.loads(content)
                if isinstance(login_dict, dict):
                    login_user = login_dict.get("login_user", common.LOGIN_USER)
                    login_password = login_dict.get("login_password", common.LOGIN_PASSWORD)
    if item.username != login_user:
        result.code = 1002
        result.msg = '用户不存在'
        return result
    if item.password != login_password:
        result.code = 1002
        result.msg = '密码错误'
        return result
    token = str(uuid.uuid4()).replace('-', '')
    nowtime = utils.get_current_datetime()
    # 默认次日凌晨1点过期
    login_expire = common.LOGIN_EXPIRE
    # 读取配置文件信息
    if os.path.isfile(common.db_system_path):
        with open(common.db_system_path, 'r') as fp:
            config_txt = fp.read()
            if config_txt:
                conf = json.loads(config_txt)
                if isinstance(conf, dict) and 'login_expire' in conf:
                    login_expire = conf.get('login_expire', 1)
    t1 = datetime.now() + dt.timedelta(days=login_expire)
    endtime = str(datetime(t1.year, t1.month, t1.day, 1, 0, 0))
    # endtime = str(datetime.now() + dt.timedelta(seconds=30)).split('.')[0]
    login_data = {token: {"stime": nowtime, "etime": endtime}}
    # 读取已经登陆的用户信息，允许多点登陆
    if os.path.isfile(common.db_login):
        with open(common.db_login, 'r') as fp:
            content = fp.read()
            if content:
                login_txt = json.loads(content)
                login_data.update(login_txt)
    with open(common.db_login, 'w+') as fp:
        fp.write(json.dumps(login_data, indent=4, ensure_ascii=False))
    result.msg = '登陆成功'
    result.data = {'token': token, 'start_time': nowtime, "end_time": endtime.replace("T", '')}
    # 创建线程，清除已过期的token信息
    th = threading.Thread(target=utils.clear_invalid_token, daemon=True)
    th.start()
    return result


@router.post('/login/valid', response_model=data.Response)
def login_valid(item: params.LoginValidParams, result: data.Response = Depends(utils.response_obj)):
    """
    登陆信息校验
    :param result: 返回值
    :return:
    """
    valid_info = utils.valid_token(token=item.token)
    if valid_info.code != 200:
        result.data = asdict(valid_info)
        return result
    result.data = valid_info.data
    return result


@router.get('/auto/analyze/case', response_model=data.Response)
def auto_analyze_case(result: data.Response = Depends(utils.response_obj)):
    """
    自动分析算例
    :param result: 返回值
    :return:
    """
    th = threading.Thread(target=utils.auto_analyze_case, daemon=True)
    th.start()
    return result