import os
import sys

root_path = os.getcwd()
sys.path.append(root_path)
import json
import time
from contextlib import asynccontextmanager
from dataclasses import asdict

import uvicorn
from fastapi import FastAPI
from fastapi import Request
from fastapi.responses import JSONResponse
from loguru import logger
from starlette.middleware.cors import CORSMiddleware

import common
import utils
from router import api
from router import henan
from utils.scheduler import load_realtime_data
from utils.scheduler import sched


def load_cache():
    s1 = time.time()
    if not os.path.isfile(common.db_path):
        logger.info(f"db_key[{common.db_path}] not exist")
        return
    db_data = utils.get_db_data()
    if len(db_data) == 0:
        logger.info(f"暂无算例数据")
        return
    start_cases = []
    for _, case in db_data.items():
        if case.get("status") == 4:
            start_cases.append(case)
    if len(start_cases) == 0:
        logger.info(f"暂无已启用的算例")
        return
    # 添加年份缓存
    years = []
    for case in start_cases:
        years.append({"year": case.get("year"), 'case_id': case.get("id")})
    common.case_year_dict = years
    # 获取系统设置的缓存算例的数量
    if not os.path.isfile(common.db_system_path):
        cache_number = 1
    else:
        with open(common.db_system_path, 'r') as fp:
            content = fp.read()
            if not content:
                cache_number = 1
            else:
                contnet = json.loads(content)
                cache_number = contnet.get('cache_number', 1)
    if isinstance(cache_number, str):
        if cache_number.isdigit():
            cache_number = int(cache_number)
        else:
            logger.debug(f"缓存数量数据类型错误, 默认为1")
            cache_number = 1
    # 获取已经启用的前三个算例文件添加到内存缓存中
    cache_cases = start_cases[:cache_number]
    for case_dict in cache_cases:
        utils.read_case(case_id=case_dict['id'], is_network=True, is_output=True)
    s2 = time.time() - s1
    logger.info(f"添加缓存成功, 缓存算例数量: {len(cache_cases)}, 耗时: {int(s2)}秒")
    # 中短期算例缓存
    short_db_data = utils.get_db_data(is_short=2)
    for case_id, case in short_db_data.items():
        if case.get("status") != 4:
            continue
        utils.read_case(case_id=case_id, is_network=True, is_output=True, is_short=2)


@asynccontextmanager
async def lifespan(app: FastAPI):
    load_cache()
    # 加载实时态最新时刻的数据
    load_realtime_data()
    # utils.process_scene()
    # 启动定时任务
    logger.info("启动定时器")
    sched.start()
    yield
    del common.case_dict
    logger.info(f"删除缓存")
    logger.info("停止定时器")
    sched.shutdown()


app = FastAPI(openapi_url="/api/v1/openapi.json", lifespan=lifespan, docs_url="/api/v1/docs")

app.include_router(
    henan.router,
    prefix="/api/v1/henan",
    tags=[common.HENAN_TAG],
    responses={404: {
        "description": "not found"
    }},
)

app.include_router(
    api.router,
    prefix="/api/v1/common",
    tags=["common"],
    responses={404: {
        "description": "not found"
    }},
)

#设置跨域传参
app.add_middleware(
    CORSMiddleware,
    allow_origins=['*'],  #设置允许的origins来源
    allow_credentials=True,
    allow_methods=["*"],  # 设置允许跨域的http方法，比如 get、post、put等。
    allow_headers=["*"]
)  #允许跨域的headers，可以用来鉴别来源等作用。


@app.middleware("http")
# 必须用 async
async def add_process_time_header(request: Request, call_next):
    # 必须用 await
    # 此处中间件做全局抛出异常的捕获，并做出处理
    try:
        host = request.headers.get("host", "")
        path = request.url.path  # 请求路径
        if "/api/v1/common" in path and path not in common.allow_api and "bg.tode.ltd" not in host:
            # 不在免校验的api中，需要检测token有效性
            token = request.headers.get("token")
            if not token:
                data = {"code": 1001, "msg": "尚未登陆", "data": {}}
                return JSONResponse(data)
            valid_result = utils.valid_token(token=token)
            if valid_result.code != 200:
                return JSONResponse(asdict(valid_result))
        # if "/api/v1/henan" in path and path not in common.allow_api:
        #     token = request.headers.get("token")
        #     if not token:
        #         data = {"code": 1001, "msg": "尚未登陆", "data": {}}
        #         return JSONResponse(data)
        #     valid_result = utils.valid_henan_token(token=token)
        #     if valid_result.code != 200:
        #         return JSONResponse(asdict(valid_result))
        response = await call_next(request)
    except Exception as e:
        logger.exception(e)
        data = {"code": 500, "msg": str(e), "data": {}}
        return JSONResponse(data)
    # 返回响应
    return response


if __name__ == "__main__":
    # 该方法作用是阻止子进程运行其后面的代码 ----
    uvicorn.run(app="main:app", port=8000, reload=False, host="0.0.0.0", workers=1)
