import uuid
from datetime import datetime
from typing import List
from typing import Optional

from sqlalchemy import FLOAT
from sqlalchemy import TEXT
from sqlalchemy import <PERSON>ole<PERSON>
from sqlalchemy import Column
from sqlalchemy import Date
from sqlalchemy import DateTime
from sqlalchemy import Integer
from sqlalchemy import <PERSON>Integer
from sqlalchemy import String

from .connect import Base


# 抽象类
class BaseModel(Base):
    __abstract__ = True

    id = Column(String(64), primary_key=True, default=uuid.uuid4)
    create_time = Column(DateTime, default=datetime.now, comment='创建时间')
    update_time = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='更新时间')
    is_delete = Column(SmallInteger, default=0, comment='逻辑删除,0:未删除,1:已删除')

    def to_dict(self, fields: Optional[List[str]] = None, exclude: Optional[List[str]] = None):
        dict_data = {}
        for c in self.__table__.columns:
            # 排除字段
            if exclude and c.name in exclude:
                continue
            # 包含字段
            if fields and c.name not in fields:
                continue
            if c.name in ['create_time', 'update_time', 'stime', 'etime', 'date_time']:
                dict_data[c.name] = str(getattr(self, c.name))
            else:
                dict_data[c.name] = getattr(self, c.name)
        return dict_data


# 实时数据-电网出力
class PowerData(BaseModel):
    __tablename__ = "sys_real_day_index_tab"

    area_name = Column(String(256), index=True, comment='区域名称')
    field_key = Column(String(128), index=True, comment='字段key')
    day_time = Column(Date, index=True, comment='日期')
    dev_id = Column(String(128), default="", index=True, comment='测点id')
    display_desc = Column(String(128), default="", comment="数据描述")


# 实时数据-电网出力数值
class PowerDataTime(BaseModel):
    __tablename__ = "sys_real_day_data_tab"

    power_id = Column(String(64), index=True, comment='关联的主表id')
    time = Column(DateTime, index=True, comment='时刻')
    value = Column(FLOAT, default=0, comment='数据值')


# 测点映射表
class MapField(BaseModel):
    __tablename__ = "sys_dev_id_map_tab"

    dev_id = Column(String(128), default="", index=True, comment='测点id')
    area_name = Column(String(256), index=True, comment='区域名称')
    area_id = Column(String(128), default="", index=True, comment='区域id')
    field_key = Column(String(16), default="", index=True, comment='数据类型')
    data_desc = Column(String(64), default="", comment='数据描述')
    display_desc = Column(String(64), default="", comment='数据描述')
    real_func_need = Column(Boolean, default=True, comment='是否实时态需求')
    his_func_need = Column(Boolean, default=True, comment="是否历史态需求")
    data_source = Column(String(16), default="sys", comment="数据来源")


# 记录立刻生成算例
class SysShortQs(BaseModel):
    __tablename__ = "sys_short_qs"

    value = Column(SmallInteger, default=0, comment='0:不需要立刻生成, 1:需要生成')
