import os

import pymysql
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker

database_pwd = os.getenv('db_pwd', "tode20200426")
database_host = os.getenv("db_host", "*************")
database_port = os.getenv("db_port", 35676)
database_name = os.getenv("db_name", "baogong")
database_user = os.getenv("db_user", "root")
url = f"mysql+pymysql://{database_user}:{database_pwd}@{database_host}:{database_port}/{database_name}?charset=utf8mb4"

engine = create_engine(url=url, max_overflow=10, pool_size=20, pool_timeout=3, pool_recycle=3600)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

Base = declarative_base()
