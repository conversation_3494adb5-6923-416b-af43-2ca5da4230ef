# 连接池
# 把他做成单例，写在一个文件里面，import它
import os

import redis

reids_host = os.getenv("redis_host", "*************")
redis_port = os.getenv("redis_port", 35267)
redis_password = os.getenv("redis_password", "tode20200426")
# 拿到一个redis的连接池
pool = redis.ConnectionPool(host=reids_host, port=redis_port, db=0, password=redis_password, max_connections=10)

# 从池子中拿一个链接
redis_obj = redis.Redis(connection_pool=pool, decode_responses=True)
