"""
@bref: 用于计算与算例结果统计的相关数据；
# 生成指标 风、光、新能源消纳率 碳排放 火、风、光利用小时数
index_data, index_dict = generate_index_data(net, result_dict, config)
"""
import os.path

import numpy as np
import h5py
import pandas as pd
from pandapower import pandapowerNet
from alg.cal_ssmetrics.case_info_statistics import CCaseInfo
from alg.cal_ssmetrics.result_distribute_ana import (result_distribute_cal, result_zone_distribute_cal,
                                                     get_grid_psm_distribute_info, get_topo_device_rate_period)
from alg.cal_ssmetrics.base_func import cof_power, cof_energy, GLOBAL_QW_NAME, _output_dict
from alg.cal_ssmetrics.zone_func import get_area_level_df_ind


class CCaseResultAna(CCaseInfo):
    """算例的静态数据统计--返回json文件,保存,全网全局静态信息的文件;
    return:
    json: 'key': 'value', key:case_name
    """

    def __init__(self, net: pandapowerNet, result_dict: dict, config: dict = dict(), area_details: dict = dict()):
        super().__init__(net, result_dict, config)

        self.case_rlt_cal = dict()  # cal_result--dict---输出;
        self.cof_power = 0.1  # MW-> 万千瓦
        self.cof_energy = 0.00001  # MWh-> 亿千瓦时
        self.zero_arr = np.zeros(config['sum_snap'])
        self.area_details_dict = area_details
        self.peaking_gap_arr = np.zeros(config['sum_snap'])  # 调峰缺口时序

    def start_result_output(self, tofile: bool = False):
        """
        进行结果有关系的数据分析和中间结果存储;
        area_details_dict: 分区关联的设备索引;
        tofile: True,将结果保存至中间文件;
        Returns:
        """
        # logger
        print("开始进行算例结果数据处理分析")

        # 下面3个函数是有顺序的;
        # step1:提取功率平衡图曲线;--结果曲线--火电/水电/核电/储能/风/光/弃风/弃光-全8760的时序等;  # 年总发电量
        self.get_power_balance_series(self.net, self.result_dict, self.config)

        # step2:计算最大调峰、供电缺口
        self.get_max_result_val_data(self.net, self.config, self.result_dict)
        # 计算分析全网的供电缺口和调峰缺口--月日分布；
        self.get_grid_psmpeaking_margin(self.net, self.config, self.result_dict)

        # step3:统计分析结果数据的小时、日、月的分布特性数据
        self.statis_power_distribute_data(self.net, self.config, self.result_dict)

        # 计算生成指标 风、光、新能源消纳率 碳排放 火、风、光、储能利用小时数
        self.generate_index_data(self.net, self.result_dict, self.config)

        # 计算全网/分区的累计缺口电量;
        self.get_electricity_slack_value()

        # 风光的弃电原因分析
        self.newable_cutailment_reason_ana(self.config, self.result_dict, self.peaking_gap_arr)

        # 保存所有时刻断面的设备数据(线路、主变、断面潮流单独存放另一h5文件):
        self.save_devices_detail_data(self.result_dict, self.config)

        # 缓存结果潮流(line_power/trafo_power/interface_power)存入至.h5文件
        # save_line_trafo_data(self.net, self.config, self.result_dict)

        # 分析全年的重载设备; 主变/线路--最高负载率排序;---平均负载率，等数据;
        self.cal_all_devices_loadratio(self.net, self.result_dict, self.config)

        # 根据topo地图,分析统计全年双回线路的最大负载率及时刻;
        # 根据拓扑信息,计算每个bus_data、line_data关联主变、多回线路直接进的平均负载率信息;
        case_topo_dict = self.GetNetTopo(self.net, self.config)
        self.case_rlt_cal['topo_dev_rate'] = get_topo_device_rate_period(case_topo_dict, self.config,
                                                                         self.result_dict)

        # 总体指标计算
        if tofile:
            self.caseinfo_tojson(self.case_rlt_cal, tofile, key_wd='cal_result')

    def get_max_result_val_data(self, net, config, result_dict):
        """
        提取结果中的最大供电缺口/调峰缺口等极值数据及发生时刻;
        # 供电缺口根据削减负荷来进行分析; 调峰缺口根据削减新能源进行分析(有可能是调峰/也有可能是断面/线路/主变过载？？);
        Args:
            result_dict:
        Returns:
            dict()
        """
        self.case_rlt_cal['max_val'] = dict()

        self.case_rlt_cal['max_val']['power_slack'] = dict()
        load_curtailment_arr = cal_sys_loadcutailment_val(config, result_dict)  # 计算向上供电缺口
        max_index = np.argmax(load_curtailment_arr)  # 负荷缺口最大值索引
        max_load_curtail = round(float(load_curtailment_arr[max_index]), 1)  # 万千瓦,1位小数
        self.case_rlt_cal['max_val']['power_slack']['supply_power'] = [max_load_curtail, int(max_index)]

        # 计算调峰缺口
        payload_arr = cal_sys_peaking_val(net, config, result_dict)

        peak_gap_max_idx = np.argmax(payload_arr)  # 调峰困难缺口最大值索引
        max_peaking_gap = round(float(payload_arr[peak_gap_max_idx]), 1)  # 万千瓦,1位小数

        self.case_rlt_cal['max_val']['power_slack']['peaking'] = [max_peaking_gap, int(peak_gap_max_idx)]
        self.peaking_gap_arr = payload_arr  # 保留调峰缺口时序--万千瓦
        self.case_rlt_cal['peaking'] = payload_arr.tolist()
        return

    def newable_cutailment_reason_ana(self, config, result_dict, peaking_gap_arr):
        """
        # 需要的数据调峰缺口; self.peaking_gap_arr
        # 风光的弃电原因分析: 弃电类型: 弃风/弃光/弃风弃光，弃电原因:调峰/网架, 弃电量: wind/solar, 弃电时刻;
        peaking_gap_arr: 调峰缺口时序;
        """
        wind_curtail = np.zeros(config['sum_snap'])  # 时序数目
        if 'wind_curtailment' in result_dict:
            wind_curtail = result_dict['wind_curtailment'].sum(axis=0) * cof_power  # --万千瓦;
        solar_curtail = np.zeros(config['sum_snap'])
        if 'solar_curtailment' in result_dict:
            solar_curtail = result_dict['solar_curtailment'].sum(axis=0) * cof_power  # --万千瓦;
        load_init_data = (result_dict['load'].sum(axis=0) + result_dict['load_curtailment'].sum(
            axis=0)) * cof_power  # 原始负荷时序--万千瓦;

        self.case_rlt_cal['new_cutail_reason'] = func_newcutailment_reason_ana(config, wind_curtail, solar_curtail,
                                                                               load_init_data, peaking_gap_arr)

    def generate_index_data(self, net, result_dict, config):
        """
        # 生成指标 风光新能源的消纳率,碳排放 火、风、光利用小时数
        # 碳排放 火电利用小时数
        # 碳排放计算：分别以总发电量折算成能源消耗量，然后再计算出排放量
        # 发一度电消耗标准煤(0.1229kg/kWh)和天然气(0.143 m3/kWh)(按照每立方米天然气发电7度)
        # 单位能源二氧化碳排放系数 标准煤 (2.64kg/kg) 天然气(2.162kg/m3)

        # 增加按地市/分区进行新能源消纳率的统计; gen/wind/solar的'city'
        Args:
            net:案例
            result_dict:计算结果
            config:配置
        Returns:
            index_list:计算统计指标包含风、光、新能源消纳率，碳排放，火电、风、光利用小时数
            key:
            carbon_emission: 碳排;using_hours:利用小时;consump_rate:消纳率;
        """
        coal_cost_per_kwh = 0.1229
        gas_cost_per_kwh = 0.143
        cof_carbon_coal = 2.64
        cof_carbon_gas = 2.162
        coal_index = net.gen.loc[net.gen.type == 'coal', :].index
        gas_index = net.gen.loc[net.gen.type == 'gas', :].index
        cap_coal = net.gen.loc[net.gen.type == 'coal', 'max_p_mw'].values.sum()
        cap_gas = net.gen.loc[net.gen.type == 'gas', 'max_p_mw'].values.sum()
        coal_output = result_dict['gen_output'][coal_index, :].sum()
        gas_output = result_dict['gen_output'][gas_index, :].sum()
        carbon_coal = coal_output * coal_cost_per_kwh * cof_carbon_coal
        carbon_gas = gas_output * gas_cost_per_kwh * cof_carbon_gas

        index_dict = {
            'gen_using_hours': 0,
            'carbon_emission': 0,
            'wind_proportion': 0,
            'wind_using_hours': 0,
            'centralized_wind_using_hours': 0,
            'distri_wind_using_hours': 0,
            'solar_proportion': 0,
            'centralized_solar_using_hours': 0,
            'distri_solar_using_hours': 0,
            'solar_using_hours': 0,
            'renewable_proportion': 0
        }
        if cap_coal + cap_gas > 0:
            index_dict['gen_using_hours'] = (coal_output + gas_output) / (cap_coal + cap_gas)
            index_dict['carbon_emission'] = carbon_coal + carbon_gas

        if config['n_element']['wind'] > 0:
            cap_wind = net.wind['max_p_mw'].values.sum()
            index_dict['wind_using_hours'] = result_dict['wind_output'].sum() / cap_wind

            # 集中式与分散式装机统计
            central_cap_wind_index = net.wind.loc[net.wind.type == 'centralized', :].index
            distri_cap_wind_index = net.wind.loc[net.wind.type == 'distributed', :].index
            central_cap_wind = net.wind.loc[central_cap_wind_index, 'max_p_mw'].values.sum()
            distri_cap_wind = net.wind.loc[distri_cap_wind_index, 'max_p_mw'].values.sum()
            centralized_wind_elec = result_dict['wind_output'][central_cap_wind_index, :].sum()
            distri_wind_elec = result_dict['wind_output'][distri_cap_wind_index, :].sum()
            centralized_wind_using_hours = centralized_wind_elec / central_cap_wind if central_cap_wind > 0 else 0
            index_dict['centralized_wind_using_hours'] = round(centralized_wind_using_hours, 1)
            distri_wind_using_hours = distri_wind_elec / distri_cap_wind if distri_cap_wind > 0 else 0
            index_dict['distri_wind_using_hours'] = round(distri_wind_using_hours, 1)
        else:
            cap_wind = 0
            central_cap_wind = 0  # 集中式风电装机容量
            distri_cap_wind = 0  # 分散式风电装机容量

        if config['n_element']['solar'] > 0:
            cap_solar = net.solar['max_p_mw'].values.sum()
            index_dict['solar_using_hours'] = result_dict['solar_output'].sum() / cap_solar

            # 集中式与分散式装机统计
            central_cap_solar_index = net.solar.loc[net.solar.type == 'centralized', :].index
            distri_cap_solar_index = net.solar.loc[net.solar.type == 'distributed', :].index
            central_cap_solar = net.solar.loc[central_cap_solar_index, 'max_p_mw'].values.sum()
            distri_cap_solar = net.solar.loc[distri_cap_solar_index, 'max_p_mw'].values.sum()
            centralized_solar_elec = result_dict['solar_output'][central_cap_solar_index, :].sum()
            distri_solar_elec = result_dict['solar_output'][distri_cap_solar_index, :].sum()

            centralized_solar_using_hours = centralized_solar_elec / central_cap_solar if central_cap_solar > 0.1 else 0.0
            index_dict['centralized_solar_using_hours'] = round(centralized_solar_using_hours, 1)
            distri_solar_using_hours = distri_solar_elec / distri_cap_solar if distri_cap_solar > 0.1 else 0.0
            index_dict['distri_solar_using_hours'] = round(distri_solar_using_hours, 1)
        else:
            cap_solar = 0
            central_cap_solar = 0  # 统调光伏装机容量
            distri_cap_solar = 0  # 分布式光伏装机容量

        # 添加至结果dict
        # 消纳率\弃电时长;
        consump_rate_dict, newcurtail_hours_dict = result_zone_distribute_cal(net, result_dict, config, zn_flag='city')

        self.case_rlt_cal['consump_rate'] = dict()  # 新能源消纳率统计
        self.case_rlt_cal['cutailment_hours'] = dict()  # 新能源弃电时长统计
        self.case_rlt_cal['consump_rate']['city'] = consump_rate_dict  # city:dict
        self.case_rlt_cal['cutailment_hours']['city'] = newcurtail_hours_dict
        zone_consump_rate_dict, zone_newcurtail_hours_dict = result_zone_distribute_cal(net, result_dict, config,
                                                                                        zn_flag='zone',
                                                                                        area_details_dict=self.area_details_dict)
        self.case_rlt_cal['consump_rate']['zone'] = zone_consump_rate_dict  # zone:dict
        self.case_rlt_cal['cutailment_hours']['zone'] = zone_newcurtail_hours_dict

        # 利用小时
        self.case_rlt_cal['using_hours'] = dict()
        self.case_rlt_cal['using_hours']['wind'] = round(index_dict['wind_using_hours'], 1)
        self.case_rlt_cal['using_hours']['solar'] = round(index_dict['solar_using_hours'], 1)
        self.case_rlt_cal['using_hours']['gen'] = round(index_dict['gen_using_hours'], 1)
        self.case_rlt_cal['using_hours']['coal'] = round(coal_output / cap_coal, 1) if cap_coal > 0.01 else 0  # 煤电
        self.case_rlt_cal['using_hours']['gas'] = round(gas_output / cap_gas, 1) if cap_gas > 0.01 else 0  # 气电必须展示--

        # 增加'centralized_wind'\'distributed_wind'\'centralized_solar'\'distributed_solar'
        self.case_rlt_cal['using_hours']['centralized_wind'] = index_dict['centralized_wind_using_hours']
        self.case_rlt_cal['using_hours']['distributed_wind'] = index_dict['distri_wind_using_hours']
        self.case_rlt_cal['using_hours']['centralized_solar'] = index_dict['centralized_solar_using_hours']
        self.case_rlt_cal['using_hours']['distributed_solar'] = index_dict['distri_solar_using_hours']

        self.case_rlt_cal['using_hours_cap'] = dict()
        # 风光的装机
        self.case_rlt_cal['using_hours_cap']['wind_cap'] = round(cap_wind, 1)
        self.case_rlt_cal['using_hours_cap']['solar_cap'] = round(cap_solar, 1)
        # 增加集中式/分散式装机统计
        self.case_rlt_cal['using_hours_cap']['centralized_wind_cap'] = round(central_cap_wind, 1)
        self.case_rlt_cal['using_hours_cap']['distributed_wind_cap'] = round(distri_cap_wind, 1)
        self.case_rlt_cal['using_hours_cap']['centralized_solar_cap'] = round(central_cap_solar, 1)
        self.case_rlt_cal['using_hours_cap']['distributed_solar_cap'] = round(distri_cap_solar, 1)

        # 获取gen表的机组类型unique--self.gen_types
        gen_types = list(net.gen['type'].unique())
        for i_type in gen_types:
            if i_type in ['coal', 'gas']:
                continue
            else:
                gen_index = net.gen.loc[net.gen.type == i_type, :].index
                cap_gen = net.gen.loc[net.gen.type == i_type, 'max_p_mw'].values.sum()
                power_output = result_dict['gen_output'][gen_index, :].sum()
                self.case_rlt_cal['using_hours'][i_type] = round(power_output / cap_gen, 1)

        # stogen = pump/battery 年利用小时
        if config['n_element']['stogen'] > 0:
            max_discharge_mw = net.stogen['max_p_discharge_mw'].values.sum()
            stogen_output_arr = result_dict['stogen_output']
            stogen_output_arr_dis = stogen_output_arr.copy()
            stogen_output_arr_dis[stogen_output_arr_dis < 0] = 0
            stogen_output_dis_hours = stogen_output_arr_dis.sum() / max_discharge_mw
            self.case_rlt_cal['using_hours']['stogen_discharge'] = round(stogen_output_dis_hours, 1)  # stogen总放电利用小时;
            # 充电利用小时;
            max_charge_mw = net.stogen['max_p_charge_mw'].values.sum()
            stogen_output_arr_charge = stogen_output_arr.copy()
            stogen_output_arr_charge[stogen_output_arr_charge > 0] = 0
            stogen_output_charge_hours = stogen_output_arr_charge.sum() * (-1) / max_charge_mw
            self.case_rlt_cal['using_hours']['stogen_charge'] = round(stogen_output_charge_hours, 1)  # stogen总充电利用小时;

            # 分类型;
            stogen_types = list(net.stogen['type'].unique())
            for i_type in stogen_types:
                if 'pump' in i_type:
                    key_type = 'pump'
                elif 'battery' in i_type or 'energy' in i_type:
                    key_type = 'battery'
                else:
                    key_type = i_type
                gen_index = net.stogen.loc[net.stogen.type == i_type, :].index
                dis_cap_gen = net.stogen.loc[net.stogen.type == i_type, 'max_p_discharge_mw'].values.sum()
                power_output_arr = result_dict['stogen_output'][gen_index, :]

                # 处理放电
                itype_output_arr_discharge = power_output_arr.copy()
                itype_output_arr_discharge[itype_output_arr_discharge < 0] = 0
                output_discharge_hours = itype_output_arr_discharge.sum() / dis_cap_gen
                self.case_rlt_cal['using_hours'][f'{key_type}_discharge'] = round(output_discharge_hours,
                                                                                  1)  # stogen总充电小时;

                # 处理充电
                charge_cap_gen = net.stogen.loc[net.stogen.type == i_type, 'max_p_charge_mw'].values.sum()
                itype_output_arr_charge = power_output_arr.copy()
                itype_output_arr_charge[itype_output_arr_charge > 0] = 0
                output_charge_hours = itype_output_arr_charge.sum() * (-1) / charge_cap_gen
                self.case_rlt_cal['using_hours'][f'{key_type}_charge'] = round(output_charge_hours, 1)  # stogen总充电利用小时;

        # 碳排放量(万吨)
        self.case_rlt_cal['carbon_emission'] = dict()
        self.case_rlt_cal['carbon_emission']['coal'] = round(carbon_coal, 1)
        self.case_rlt_cal['carbon_emission']['gas'] = round(carbon_gas, 1)

        return

    def get_gen_hydro_cutailment_power(self, net, result_dict, config):
        """
        计算gen表中的水电的弃水电力;
        Returns:
        """

        hydro_cutailment_arr = self.zero_arr  # TODO: 此处需要计算gen中的水电气电电力
        # gen_p_daily_accum_list = net.timeseries.loc[net.timeseries['type'] == 'gen_p_daily_accum'].drop(
        #     ['name', 'type'], axis=1)  # 获取 net.timeseries 表中的”type“为”gen_p_daily_accum“的所有数据
        #
        # gen_p_daily_accum_index = gen_p_daily_accum_list.index.values  # 获取 net.timeseries 表中的”type“为”gen_p_daily_accum“的所有索引值
        # gen_ts_index_nona = net.gen.dropna(subset=['timeseries'],
        #                                    inplace=False)  # net.gen 表中，如果 timeseries 列值为nan，则对于后续计算无意义，并且会导致 timeseries 列下多个索引时用”,“进行分割会报错，因此先剔除这些无效数据
        # gen_hydro_single_index = gen_ts_index_nona['timeseries'][
        #     gen_ts_index_nona['timeseries'].str.split(",", expand=True).isna()[
        #         0]].to_frame()  # net.gen的timeseries表下用”,“无法分割的项为单个索引，将单个索引的项记录下来
        # gen_hydro_multi_index = gen_ts_index_nona['timeseries'].str.split(",",
        #                                                                   expand=True).dropna()  # 用","可以分割的项为多个索引，将多个索引的所有索引值记录下来。
        # ## 此处使用append，dataframe的行名会全都是0。暂未发现有问题，此处特别标注一下
        # new_rows = []
        # for index, row in gen_hydro_multi_index.iterrows():
        #     cols = row.filter(regex='^[0-9]+$').index
        #     for col in cols:
        #         if int(row[col]) in gen_p_daily_accum_index:
        #             new_rows.append({'timeseries': row[col]})
        # # 多个索引里，如果有值在 gen_p_daily_accum 里，则记录下来
        # ##
        # gen_hydro_multi_index = pd.DataFrame(new_rows, index=gen_hydro_multi_index.index)
        # gen_hydro_single_index = gen_hydro_single_index[
        #     gen_hydro_single_index['timeseries'].astype(int).isin(gen_p_daily_accum_index)].reset_index(drop=True)
        # gen_hydro_index = gen_hydro_multi_index.append(
        #     gen_hydro_single_index)  # 水电索引; 将所有符合要求的索引合并在一起，并设置格式，行名为机组编号，内容为accum对应行索引
        # ##
        # timeseries_list = net.timeseries.drop(['name', 'type'], axis=1)
        # timeseries_index = timeseries_list.index.values
        # gen_hydro_eq_index = gen_ts_index_nona['timeseries'][
        #     gen_ts_index_nona['timeseries'].str.split(",", expand=True).isna()[0]].to_frame()
        # gen_hydro_eq_index = gen_hydro_eq_index[gen_hydro_eq_index['timeseries'].astype(int).isin(timeseries_index)]
        # # result_summary['hydro_fire_eq_output_hour'] = np.array(result_dict["gen_output"].loc[gen_hydro_eq_index.index].sum(axis=0))
        # # print(f"gen_hydro_eq_index.index:{gen_hydro_eq_index.index}")
        # # 以上几行代码的作用是，存在一种情况，火电特性的水电仅索引一个，且索引的这个索引号属于accum，这种情况也符合条件
        # ## gen_p_daily_accum_list-----type = gen_p_daily_accum--accum 的所有曲线;
        # hydro_fire_rate_P_reshape = net.gen['max_p_mw'][gen_hydro_index.index[:]].to_frame()
        # hydro_fire_g_1_plan_hour = pd.DataFrame()
        #
        # for i in gen_hydro_index.index:
        #     if pd.isnull(gen_hydro_index.loc[i, 'timeseries']) or np.isnan(gen_hydro_index.loc[i, 'timeseries']):
        #         hydro_fire_plan_speci_equip = np.array(result_dict["gen_output"][i])
        #     else:
        #         hydro_fire_plan_speci_equip = np.dot(hydro_fire_rate_P_reshape.loc[i], gen_p_daily_accum_list.loc[
        #             int(gen_hydro_index.loc[i].iloc[0])].to_frame().T)
        #         # 计划出力 = 具有火电特性的，符合条件的水电机组，的额定值 * 对应行的accum时序。 符合条件的水电机组的额定值矩阵为 1*n。对应的accum时序矩阵为n*8760。使用矩阵相乘，获得1*8760的每小时计划出力总量
        #     hydro_fire_plan_speci_equip = pd.DataFrame(hydro_fire_plan_speci_equip).T
        #     hydro_fire_g_1_plan_hour = pd.concat([hydro_fire_g_1_plan_hour, hydro_fire_plan_speci_equip])
        #
        # hydro_fire_g_1_plan_hour_sum = np.array(hydro_fire_g_1_plan_hour.sum(axis=0))
        # ### 注 result_summary['hydro_fire_output_hour'] 包含了 result_summary['hydro_fire_eq_output_hour']和result_summary['hydro_fire_g_1_output_hour']（在这里不需要考虑）
        # hydro_gen_idx_list = (net.gen)[(net.gen)['type'] == "hydro"].index.tolist()
        # hydro_fire_output_hour_sum = np.array(result_dict["gen_output"][hydro_gen_idx_list].sum(axis=0))
        # # 火电特性的水电的输出
        # hydro_cutailment_arr = hydro_fire_g_1_plan_hour_sum - hydro_fire_output_hour_sum
        return hydro_cutailment_arr

    def get_power_balance_series(self, net, all_result_dict, config):
        """
        获取功率平衡曲线堆叠图数据，各个时段的发电和负荷
        # 全网新能源风/光的日消纳和弃电分布(1-24时)，月弃电分布(1-12月)
        # 目前非全网的时序暂不存储/数据量较大;
        Args:
            net:案例
            all_result_dict:计算结果
            config:配置
            area_name: 分区名称
        Returns:
            power_balance_line_chart_data_dict:dict:功率平衡曲线堆叠图数据，各个时段的发电和负荷
            # 日电量/弃电电量分布 hour_electric_distri
            # 月电量/弃电电量分布 month_electric_distri
        """
        # 存储结果输出相关的时序数据;# 机组年发电量数据保存;
        self.case_rlt_cal['electricity'] = dict()
        areas_name = list(self.area_details_dict.keys()) + [GLOBAL_QW_NAME]
        for i_zone in areas_name:
            line_curve_data_dict, area_electricity_dict = get_power_balance_data(net, all_result_dict, config,
                                                                                 area_name=i_zone,
                                                                                 area_details_dict=self.area_details_dict)
            self.case_rlt_cal['electricity'][i_zone] = area_electricity_dict
            if i_zone == GLOBAL_QW_NAME:
                self.case_rlt_cal['line_curve_data'] = line_curve_data_dict

        return

    def statis_power_distribute_data(self, net, config, result_dict):
        """
        统计分布式特性数据
        Args:
            result_dict:  teap计算结果
            config:
        Returns:
        """
        # daily_electric_data  # 日分布数据; # daily_data
        # hours_distribute_dict  # 小时分布;
        # month_distribute_dict  # 月分布;
        # month_balance_distri # 月供电不足/调峰不足 分布
        (self.case_rlt_cal['hour_electric_distri'],
         self.case_rlt_cal['hour_distri'], self.case_rlt_cal['daily_electricity'],
         self.case_rlt_cal['month_electric_distri'],
         self.case_rlt_cal['month_balance_distri']) = result_distribute_cal(net, config, result_dict,
                                                                            self.case_rlt_cal['line_curve_data'],
                                                                            self.peaking_gap_arr)

        # 计算区域级(例如:江苏,苏南/苏北，河南，豫南等)的供电不足小时分布;

    def save_devices_detail_data(self, result_dict, config):
        """
        将8760时刻的所有设备时序数据保存为.h5文件;
        Args:
            net:案例
            result_dict:计算结果
            config:配置
            detail_data_dict:详细结果数据
        Returns:
        """
        # 保存为h5文件;
        h5file = os.path.join(self.save_dir, f'rlt_power_output_{self.case_id}.hdf5')
        with h5py.File(h5file, 'w') as f:
            p_out_grp = f.create_group("power_out")  # 创建一个名为'my_group'的group
            for key, value in _output_dict.items():
                if value not in config['n_element'] or config['n_element'][value] <= 0:
                    continue
                p_out_grp.create_dataset(key, data=result_dict[key])  # 写入dataset

        pf_output_dict = {
            "dcline_power_from": "dcline",
            "dcline_power_to": "dcline",
            'line_power': 'line',
            'trafo_power': 'trafo',
            'interface_power': 'interface',
        }
        pf_h5file = os.path.join(self.save_dir, f'power_flow_{self.case_id}.hdf5')
        with h5py.File(pf_h5file, 'w') as f:
            p_out_grp = f.create_group("power_flow")  # 创建一个名为'my_group'的group
            for key, value in pf_output_dict.items():
                if value not in config['n_element'] or config['n_element'][value] <= 0:
                    continue
                p_out_grp.create_dataset(key, data=result_dict[key])  # 写入dataset
        return

    def cal_all_devices_loadratio(self, net, result_dict, config, grep_vn_kv: bool = False,
                                  only_heavy_device: bool = False):
        """
        计算设备(线路/主变/断面)的全网年: 平均负载率/最高负载率,重载/越限时长;--按越限时长、重载时长、平均负载率、最高负载率从大到小排序;
        Args:
            result_dict:
            config:
            grep_vn_kv: 是否进行电压过滤的筛选
            only_heavy_device: 是否只输出重载的设备列表;
        Returns:
            dict：trafo/line/interface
            trafo:index/name/avg_ratio/max_ratio/overratio_hours/heavyratio_hours

            dict_from_df = df.set_index('column_name')['value_column'].to_dict()
        """
        dev_load_ratio_data = {
            'trafo': dict(),
            'line': dict(),
            'interface': dict()
        }
        device_limit_data = dict()  # 存放设备限额数据

        lines_df, trafo_df, interface_df = cal_all_devices_loadratio_df(net, result_dict, config, grep_vn_kv,
                                                                        only_heavy_device)
        # line_df, trafo,interface_df
        if config['n_element']['line'] > 0:
            for i_col in lines_df.columns.tolist():
                dev_load_ratio_data['line'][i_col] = lines_df[i_col].values.tolist()
            device_limit_data['line'] = net.line['stable_limit_mw'].values.tolist()
        else:
            device_limit_data['line'] = list()

        if config['n_element']['trafo'] > 0:
            for i_col in trafo_df.columns.tolist():
                dev_load_ratio_data['trafo'][i_col] = trafo_df[i_col].values.tolist()
            device_limit_data['trafo'] = net.trafo['stable_limit_mw'].values.tolist()
        else:
            device_limit_data['trafo'] = list()

        if config['n_element']['interface'] > 0:
            for i_col in interface_df.columns.tolist():
                dev_load_ratio_data['interface'][i_col] = interface_df[i_col].values.tolist()
            device_limit_data['interface'] = net.interface['max_p_mw'].values.tolist()
        else:
            device_limit_data['interface'] = list()

        self.case_rlt_cal['device_loadratio'] = dev_load_ratio_data
        self.case_rlt_cal['device_limit'] = device_limit_data  # 所有设备的限额;

        return

    def get_grid_psmpeaking_margin(self, net, config, result_dict):
        """
        分析全网的供电裕度不足、调峰裕度不足的年/日，月/日/小时分布
        """
        self.case_rlt_cal['balanceMargin'] = dict()
        self.case_rlt_cal['balanceMargin']['month'], self.case_rlt_cal['balanceMargin'][
            'monthDay'], power_supply_slack_arr, peaking_gap_arr = grid_psm_distribute_ana(net, config, result_dict)

        self.case_rlt_cal['powerSupply'] = dict()  # 供电方面信息
        self.case_rlt_cal['powerSupply']['series_data'] = power_supply_slack_arr.tolist()

    def get_electricity_slack_value(self):
        """
        计算全网/分区的年累计供电缺口电量;
        """
        pass


# 非类函数定义
def cal_all_devices_loadratio_df(net, result_dict, config, grep_vn_kv: bool = True,
                                 only_heavy_device: bool = False):
    """
    计算设备(线路/主变/断面)的全网年: 平均负载率/最高负载率,重载/越限时长;--按越限时长、重载时长、平均负载率、最高负载率从大到小排序;
    Args:
        result_dict:
        config:
        grep_vn_kv: 是否进行电压过滤的筛选
        only_heavy_device: 是否只输出重载的设备列表;
    Returns:
        dict：trafo/line/interface
        trafo:index/name/avg_ratio/max_ratio/overratio_hours/heavyratio_hours

        dict_from_df = df.set_index('column_name')['value_column'].to_dict()
    """

    def CalDeviceRatioInfo(device_type_df: pd.DataFrame, config, p_limit, out_put_arr, vn_kv_key: str, heavy_ratio_mk,
                           over_ratio_mk):
        """
        分析重载越限时长等信息
        Args:
            device_type_df: 类型 trafo/line 的df
            p_limit： 设备限额
            out_put_arr:  所有设备负载有功时序数据
            heavy_ratio_mk: 重载门槛值
            over_ratio_mk:越限门槛值
        Returns: df: ['over_hours_ratio', 'heavy_hours_ratio', 'avg_ratio', 'max_ratio']
        """
        devs_index = device_type_df.index
        devs_name = device_type_df['name']
        if vn_kv_key in device_type_df.columns:
            devs_vn_kv = device_type_df[vn_kv_key]
        else:
            devs_vn_kv = np.zeros(len(devs_name))
        out_put_arr_abs = np.abs(out_put_arr)
        ratio_arr = np.divide(out_put_arr_abs, p_limit[:, np.newaxis])  # 计算负载率;
        devs_avg_ratio = ratio_arr.mean(axis=1).round(4)  # 横向计算所有设备的年平均负载率;
        devs_max_ratio = ratio_arr.max(axis=1).round(4)  # 横向获取所有设备的年最大负载率;

        # 分析重载/越限时长
        def ana_hours(ratio_arr, val):
            mask = ratio_arr > val  # 创建bool数组，表示 arr1 中大于 val 的位置
            if 'cal_overlimit_rate' in config and config['cal_overlimit_rate']:
                fit_hours = (np.sum(mask, axis=1) / config['sum_snap']).round(4)  # 重载、越限时长 / 总时长
            else:
                fit_hours = (np.sum(mask, axis=1) / 100).round(2)  # 重载、越限时长--前端统一乘以100了;
            return fit_hours

        heavy_hours = ana_hours(ratio_arr, heavy_ratio_mk)  # 重载时长
        over_hours = ana_hours(ratio_arr, over_ratio_mk)  # 越限时长

        devs_df = pd.DataFrame()
        devs_df = pd.concat([devs_df, pd.DataFrame({
            'index': devs_index,
            'name': devs_name,
            'vn_kv': devs_vn_kv,
            'p_limit': p_limit.astype(int),
            'avg_ratio': devs_avg_ratio,
            'max_ratio': devs_max_ratio,
            'over_hours_ratio': over_hours,
            'heavy_hours_ratio': heavy_hours,
        })])
        # 进行大->小排序
        devs_df = devs_df.sort_values(by=['over_hours_ratio', 'heavy_hours_ratio', 'avg_ratio', 'max_ratio'],
                                      ascending=False)

        # 去除平均负载率为0的设备
        # devs_df = devs_df[devs_df['avg_ratio'] < 0.001]
        return devs_df

    # 需要筛选的最低电压等级;
    if grep_vn_kv:
        select_vn_kv = config['device_dis_vn_kv'] if 'device_dis_vn_kv' in config else 500
    else:
        select_vn_kv = 0.0

    col_names = ['index', 'name', 'vn_kv', 'p_limit', 'avg_ratio', 'max_ratio', 'over_hours_ratio', 'heavy_hours_ratio']
    # line
    lines_df = pd.DataFrame(columns=col_names)
    if config['n_element']['line'] > 0:
        selected_line_df = net.line[net.line['vn_kv'] >= select_vn_kv * 0.9]
        lines_index = selected_line_df.index
        if len(lines_index) > 0:
            lines_p_limit = net.line.loc[lines_index, 'stable_limit_mw'].values
            lines_power_arr = result_dict['line_power'][lines_index, :]
            lines_df = CalDeviceRatioInfo(selected_line_df, config, lines_p_limit, lines_power_arr, "vn_kv",
                                          config['line_heavy_ratio'], config['line_over_ratio'])
            if only_heavy_device:
                lines_df = lines_df[lines_df['heavy_hours_ratio'] > 0]

    # trafo
    trafo_df = pd.DataFrame(columns=col_names)
    if config['n_element']['trafo'] > 0:
        selected_trafo_df = net.trafo[net.trafo['vn_hv_kv'] >= select_vn_kv * 0.9]

        # 针对trafo进行整合--如果 : abs('vn_lv_kv' - 1) < 0.1 & lv_bus 相等; 保留电压等级高的绕组记录(三绕组主变只保留最高电压等级);
        selected_trafo_df['selected'] = False
        # 筛选满足 abs('vn_lv_kv' - 1) < 0.1 的记录
        filtered_df = selected_trafo_df[abs(selected_trafo_df['vn_lv_kv'] - 1) < 0.1]
        selected_trafo_df.loc[~selected_trafo_df.index.isin(filtered_df.index.tolist()), 'selected'] = True
        # 按 lv_bus 分组，并选择 hv_bus 最大的记录
        max_hv_bus_idx = filtered_df.groupby('lv_bus')['vn_hv_kv'].idxmax()
        selected_trafo_df.loc[max_hv_bus_idx, 'selected'] = True
        selected_trafo_df = selected_trafo_df[selected_trafo_df['selected']]
        selected_trafo_df = selected_trafo_df.drop('selected', axis=1)  # 去除'selected'列

        trafo_index = selected_trafo_df.index
        if len(trafo_index) > 0:
            trafo_p_limit = net.trafo.loc[trafo_index, 'stable_limit_mw'].values
            trafo_power_arr = result_dict['trafo_power'][trafo_index, :]
            trafo_df = CalDeviceRatioInfo(selected_trafo_df, config, trafo_p_limit, trafo_power_arr, "vn_hv_kv",
                                          config['trafo_heavy_ratio'], config['trafo_over_ratio'])
            if only_heavy_device:
                trafo_df = trafo_df[trafo_df['heavy_hours_ratio'] > 0]

    # interface
    interface_df = pd.DataFrame(columns=col_names)
    if config['n_element']['interface'] > 0:
        inf_p_limit = net.interface['max_p_mw'].values
        inf_power_arr = result_dict['interface_power']
        interface_df = CalDeviceRatioInfo(net.interface, config, inf_p_limit, inf_power_arr, "none",
                                          config['interface_heavy_ratio'], config['interface_over_ratio'])
        if only_heavy_device:
            interface_df = interface_df[interface_df['heavy_hours_ratio'] > 0]
    interface_df = interface_df.drop(columns=['vn_kv'])  # drop 'vn_kv'

    # 按24小时进行统计分析;

    # 按月进行统计分析; 季度不单独统计，在月的基础上进行统计分析;

    return lines_df, trafo_df, interface_df


def get_power_balance_data(net, all_result_dict, config, area_name=GLOBAL_QW_NAME, area_details_dict: dict = dict()):
    """
    获取功率平衡曲线堆叠图数据，各个时段的发电和负荷;  支持针对分区进行处理;
    # 全网新能源风/光的日消纳和弃电分布(1-24时)，月弃电分布(1-12月)
    Args:
        net:案例
        all_result_dict:计算结果
        config:配置
        area_name: 分区名称
        area_details_dict: 分区关联设备详情;
    Returns:
        power_balance_line_chart_data_dict:dict:功率平衡曲线堆叠图数据，各个时段的发电和负荷
        # 日电量/弃电电量分布 hour_electric_distri
        # 月电量/弃电电量分布 month_electric_distri
    """
    num_snaps = config['sum_snap']
    zero_arr = np.zeros(num_snaps)

    line_chart_data_dict = dict()
    electric_data_dict = dict()
    daily_electric_data = dict()  # 日电量数据;

    if area_name == GLOBAL_QW_NAME or area_name is None:
        result_dict = all_result_dict
    else:
        # 根据分区关联设备,筛选重新准备result_dict
        result_dict = get_area_level_df_ind(net, area_name, area_details_dict, all_result_dict)
    # load
    load_output = result_dict['load'].sum(axis=0)
    load_curtailment_arr = result_dict['load_curtailment']
    load_curtailment_arr[load_curtailment_arr < 0] = 0
    load_curtailment = load_curtailment_arr.sum(axis=0)
    load_init = load_output + load_curtailment
    line_chart_data_dict['load_curtailment'] = load_curtailment
    line_chart_data_dict['load_data'] = load_init  # 原始load时序曲线;
    electric_data_dict['load'] = load_output.sum()

    # feedin
    if config['n_element']['feedin'] > 0:
        feedin_output_arr = result_dict['feedin_output'].sum(axis=0)
        line_chart_data_dict['feedin_output'] = feedin_output_arr
        electric_data_dict['feedin'] = feedin_output_arr.sum()
    else:
        feedin_output_arr = zero_arr

    # wind/solar
    if config['n_element']['wind'] > 0:
        wind_output_arr = result_dict['wind_output'].sum(axis=0)
        line_chart_data_dict['wind_output'] = wind_output_arr
        wind_curt_arr = result_dict['wind_curtailment'].sum(axis=0)
        line_chart_data_dict['wind_curtailment'] = wind_curt_arr
        electric_data_dict['wind'] = wind_output_arr.sum()
        electric_data_dict['wind_curtailment'] = wind_curt_arr.sum()
        # 统调风电出力
        centralized_wind_idx = net.wind.loc[net.wind['type'] == 'centralized'].index
        if area_name == GLOBAL_QW_NAME or area_name is None:
            pass
        else:
            # 筛选出centralized_wind_idx在area_details_dict[area_name]['wind']的元素
            centralized_wind_idx = centralized_wind_idx[centralized_wind_idx.isin(area_details_dict[area_name]['wind'])]
        line_chart_data_dict['centralized_wind_output'] = all_result_dict['wind_output'][centralized_wind_idx, :].sum(
            axis=0)
    else:
        wind_output_arr = zero_arr
        wind_curt_arr = zero_arr
    if config['n_element']['solar'] > 0:
        solar_output_arr = result_dict['solar_output'].sum(axis=0)
        line_chart_data_dict['solar_output'] = solar_output_arr
        solar_curt_arr = result_dict['solar_curtailment'].sum(axis=0)
        line_chart_data_dict['solar_curtailment'] = solar_curt_arr
        electric_data_dict['solar'] = solar_output_arr.sum()
        electric_data_dict['solar_curtailment'] = solar_curt_arr.sum()
        # 统调光伏
        centralized_solar_idx = net.solar.loc[net.solar['type'] == 'centralized'].index
        if area_name == GLOBAL_QW_NAME or area_name is None:
            pass
        else:
            # 筛选出centralized_wind_idx在area_details_dict[area_name]['wind']的元素
            centralized_solar_idx = centralized_solar_idx[
                centralized_solar_idx.isin(area_details_dict[area_name]['solar'])]
        line_chart_data_dict['centralized_solar_output'] = all_result_dict['solar_output'][centralized_solar_idx,
                                                           :].sum(
            axis=0)
    else:
        solar_output_arr = zero_arr
        solar_curt_arr = zero_arr

    # stogen
    if config['n_element']['stogen'] > 0 and len(result_dict['stogen_output']) > 0:
        stogen_output_arr = result_dict['stogen_output'].sum(axis=0)
        line_chart_data_dict['stogen_output'] = stogen_output_arr

        # 储能放电电量
        stogen_output_arr1 = result_dict['stogen_output'].copy()
        stogen_output_arr1[stogen_output_arr1 < 0] = 0
        electric_data_dict['stogen_discharge'] = stogen_output_arr1.sum()

        # 抽蓄
        if area_name == GLOBAL_QW_NAME:
            pump_mask = net.stogen['type'].str.contains('pump', case=False, na=False)
        else:
            pump_mask = net.stogen['type'].str.contains('pump', case=False, na=False) & net.stogen['zone'] == area_name
        pump_index = net.stogen[pump_mask].index.tolist()
        if len(pump_index) > 0:
            pump_output_arr = all_result_dict['stogen_output'][pump_index, :]
            line_chart_data_dict['stogen_pump'] = pump_output_arr.sum(axis=0)
            # 抽蓄放电电量
            pump_output_arr1 = pump_output_arr.copy()
            pump_output_arr1[pump_output_arr1 < 0] = 0
            electric_data_dict['pump_discharge'] = pump_output_arr1.sum()

        # 储能电量
        battery_mask = net.stogen['type'].str.contains('battery', case=False, na=False) | net.stogen[
            'type'].str.contains('energy', case=False, na=False)
        battery_index = net.stogen[battery_mask].index.tolist()
        if len(battery_index) > 0:
            battery_output_arr = all_result_dict['stogen_output'][battery_index, :]
            line_chart_data_dict['stogen_battery'] = battery_output_arr.sum(axis=0)
            # 储能放电电量
            battery_output_arr1 = battery_output_arr.copy()
            battery_output_arr1[battery_output_arr1 < 0] = 0
            electric_data_dict['battery_discharge'] = battery_output_arr1.sum()

    # gen
    if area_name == GLOBAL_QW_NAME:
        cond1 = True
    else:
        cond1 = net.gen['zone'] == area_name
    nuclear_index = net.gen.loc[(net.gen.type == 'nuclear') & cond1, :].index.tolist()
    if len(nuclear_index) > 0:
        nuclear_output_arr = all_result_dict['gen_output'][nuclear_index, :].sum(axis=0)
        line_chart_data_dict['nuclear_output'] = nuclear_output_arr
        electric_data_dict['nuclear'] = nuclear_output_arr.sum()  # 核电机组

    coal_index = net.gen.loc[(net.gen.type == 'coal') & cond1, :].index.tolist()
    if len(coal_index) > 0:
        coal_output_arr = all_result_dict['gen_output'][coal_index, :].sum(axis=0)
        line_chart_data_dict['coal_output'] = coal_output_arr
        electric_data_dict['coal'] = coal_output_arr.sum()  # 煤电机组

    gas_index = net.gen.loc[(net.gen.type == 'gas') & cond1, :].index.tolist()
    if len(gas_index) > 0:
        gas_output_arr = all_result_dict['gen_output'][gas_index, :].sum(axis=0)
        line_chart_data_dict['gas_output'] = gas_output_arr
        electric_data_dict['gas'] = gas_output_arr.sum()  # 燃气机组

    gen_hydropower_index = net.gen.loc[(net.gen.type.isin(['hydro', 'hydropower'])) & cond1, :].index
    if len(gen_hydropower_index) > 0:
        gen_hydropower_output = all_result_dict['gen_output'][gen_hydropower_index, :].sum(axis=0)
        line_chart_data_dict['hydro_output'] = gen_hydropower_output
        electric_data_dict['hydro'] = gen_hydropower_output.sum()  # 水电
        # 计算水电的弃电--没有电量约束的水电不存在弃水;
        # line_chart_data_dict['hydro_cutailment'] = self.get_gen_hydro_cutailment_power(net, result_dict, config)
    else:
        gen_hydropower_output = np.zeros(config['sum_snap'])
        electric_data_dict['hydro'] = 0.0
        # line_chart_data_dict['hydro_cutailment'] = np.zeros(config['sum_snap'])

    # hydropower
    if config['n_element']['hydropower'] > 0 and len(result_dict['hydropower_output']) > 0:
        all_hydropower_output = result_dict['hydropower_output'].sum(axis=0) + gen_hydropower_output
        line_chart_data_dict['hydro_output'] = all_hydropower_output
        electric_data_dict['hydro'] = all_hydropower_output.sum()  # 水电-发电量;
        # # 统计水电的弃水电力(2部分,hydropower + gen_hydro部分);--每个机组可能并不一样;
        # hydropower_cutail_volume = result_dict['out_volume'] - result_dict['hydropower_volume']  # shape: m*8760
        # hydro_cost_coef = np.array(net.hydropower["water_cost_m3_per_second_per_mw"].values)
        # hydropower_cutailment = np.multiply(hydropower_cutail_volume, hydro_cost_coef)
        # line_chart_data_dict['hydro_cutailment'] += hydropower_cutailment  # 水电两部分弃电电力: gen/hydro + hydropower中

    ord_gen_index = net.gen.loc[(~net.gen.type.isin(['nuclear', 'hydro', 'hydropower'])) & cond1, :].index
    if len(ord_gen_index) > 0:
        gen_output_arr = all_result_dict['gen_output'][ord_gen_index, :].sum(axis=0)
        line_chart_data_dict['gen_output'] = gen_output_arr  # 火电机组;
        electric_data_dict['gen'] = gen_output_arr.sum()

    # 根据机组gen表中的机组开停机状态,计算上调节能力(上旋) = on_max - on_p 和下调节能力(下旋) = on_p -on_min--利用gen_state
    # 所有开机机组的装机容量； TODO: 此处可能不全面;max_p_mw可以通过时序指定; 需要根据关联时序曲线然后确定最大出力;
    gen_p_max = np.absolute(net.gen.loc[:, 'max_p_mw'].values.reshape(-1, 1))
    gen_p_min = np.absolute(net.gen.loc[:, 'min_p_mw'].values.reshape(-1, 1))
    if area_name == GLOBAL_QW_NAME:
        gen_on_max = np.multiply(all_result_dict['gen_state'], gen_p_max).sum(axis=0)
        gen_on_min = np.multiply(all_result_dict['gen_state'], gen_p_min).sum(axis=0)
    else:
        area_gen_idx = net.gen.loc[net.gen.zone == area_name, :].index
        gen_on_max = np.multiply(all_result_dict['gen_state'], gen_p_max)[area_gen_idx, :].sum(axis=0)
        gen_on_min = np.multiply(all_result_dict['gen_state'], gen_p_min)[area_gen_idx, :].sum(axis=0)
    # 调节能力计算
    gen_all_on_p = result_dict['gen_output'].sum(axis=0)  # 'gen'表中所有的机组出力
    reserve_up_p = gen_on_max - gen_all_on_p  # 上调节能力
    reserve_up_p[reserve_up_p < 0] = 0
    line_chart_data_dict['reserve_up'] = reserve_up_p
    reserve_down_p = gen_all_on_p - gen_on_min  # 下调节能力
    reserve_down_p[reserve_down_p < 0] = 0
    line_chart_data_dict['reserve_down'] = reserve_down_p

    # 最小可调出力 = feedin_data+technical_output_min+renewable-loss_and_gen_cut-0
    line_chart_data_dict['adjustable_p_min'] = feedin_output_arr + gen_on_min + wind_output_arr + solar_output_arr
    # 最大电力资源
    line_chart_data_dict['max_on_power'] = feedin_output_arr + gen_on_max + (wind_output_arr + wind_curt_arr) + (
            solar_output_arr + solar_curt_arr)

    # 所有数据均保留2为小数,并转为万千瓦
    for i_key, val in line_chart_data_dict.items():
        line_chart_data_dict[i_key] = (np.array(val) * cof_power).round(2).tolist()

    # 所有电量数据均保留2为小数-并转为亿千瓦时
    for i_key, val in electric_data_dict.items():
        electric_data_dict[i_key] = round(val * cof_energy, 2)

    # 所有日电量数据均保留2为小数-MWh
    for i_key, val in daily_electric_data.items():
        daily_electric_data[i_key] = np.array(val).round(2).tolist()

    # 存储结果输出相关的时序数据;# 机组年发电量数据保存;
    return line_chart_data_dict, electric_data_dict


def cal_sys_loadcutailment_val(config: dict, result_dict: dict):
    """
    计算全网的供电缺口(即负荷削减量，需求侧响应负荷单独建模)
    result_dict:teap的计算结果
    """
    n_snapshots = config['sum_snap']
    zero_arr = np.zeros(n_snapshots)
    if 'load_curtailment' not in result_dict:
        return zero_arr
    else:
        power_supply_arr = result_dict['load_curtailment'].sum(axis=0)
        power_supply_arr[power_supply_arr < 0.1] = 0  # 反向提升负荷的置为0
        power_supply_arr = (power_supply_arr * cof_power).round(1)  # MW->万千瓦
        return power_supply_arr


def cal_sys_peaking_val(net, config: dict, result_dict: dict):
    """
    计算全网的调峰缺口时序数据
    net: topo网络
    config: 配置参数
    result_dict: teap的计算结果dict
    return:list()
    """
    # 调峰缺口--(净负荷 - 机组的最小技术出力)

    # newenergy_curtail_arr = np.zeros(config['sum_snap'])
    # if config['n_element']['wind'] > 0:
    #     newenergy_curtail_arr += result_dict['wind_curtailment'].sum(axis=0)
    # if config['n_element']['solar'] > 0:
    #     newenergy_curtail_arr += result_dict['solar_curtailment'].sum(axis=0)
    # newenergy_max_index = np.argmax(newenergy_curtail_arr)  # 负荷缺口最大值索引
    # max_new_curtail = round(float(newenergy_curtail_arr[newenergy_max_index] * 0.1), 2)  # 万千瓦,2位小数
    # 计算净负荷;
    load_curtailment_arr = result_dict['load_curtailment'].sum(axis=0)
    load_ori_arr = result_dict['load'].sum(axis=0) + load_curtailment_arr

    # 计算开机机组的最小出力;
    gen_p_min = np.absolute(net.gen.loc[:, 'min_p_mw'].values.reshape(-1, 1))
    gen_on_min_arr = np.multiply(result_dict['gen_state'], gen_p_min).sum(axis=0)

    num_snaps = config['sum_snap']
    if 'wind_output' in result_dict:
        wind_arr = result_dict['wind_output'].sum(axis=0) + result_dict['wind_curtailment'].sum(axis=0)
    else:
        wind_arr = np.zeros(num_snaps)
    if 'solar_output' in result_dict:
        solar_arr = result_dict['solar_output'].sum(axis=0) + result_dict['solar_curtailment'].sum(axis=0)
    else:
        solar_arr = np.zeros(num_snaps)

    if 'feedin_output' in result_dict:
        feedin_arr = result_dict['feedin_output'].sum(axis=0)
    else:
        feedin_arr = np.zeros(num_snaps)

    if 'hydropower_output' in result_dict:
        hydropower_output_arr = result_dict['hydropower_output'].sum(axis=0)
    else:
        hydropower_output_arr = np.zeros(num_snaps)

    if 'stogen_output' in result_dict:
        stogen_output_arr = result_dict['stogen_output'].sum(axis=0)
    else:
        stogen_output_arr = np.zeros(num_snaps)

    if config['reserve_slack'] is False:
        load_reserve_needcap = load_ori_arr * config['down_reserve_cof']
    else:
        load_reserve_needcap = np.zeros(num_snaps)
    payload_arr = (gen_on_min_arr + load_reserve_needcap) + hydropower_output_arr + (wind_arr + solar_arr) \
                  + feedin_arr + stogen_output_arr - load_ori_arr  # >0,缺口,调峰困难 = 开机机组最小出力 - 总负荷
    payload_arr[payload_arr < 0.01] = 0
    payload_arr = (payload_arr * cof_power).round(1)  # MW->万千瓦,保留1位小数;
    return payload_arr


def func_newcutailment_reason_ana(config, wind_curtail, solar_curtail, load_init_data, peaking_gap_arr):
    """
    # 需要的数据调峰缺口; self.peaking_gap_arr
    # 风光的弃电原因分析: 弃电类型: 弃风/弃光/弃风弃光，弃电原因:调峰/网架, 弃电量: wind/solar, 弃电时刻;
    wind_curtail, solar_curtail, load_init_data : 风/光/原始负荷时序，单位万千瓦;
    peaking_gap_arr: 调峰缺口时序;单位万千瓦;
    """
    new_curtail_data = dict()
    # 新能源的弃电(风光弃电)时序
    # wind_curtail = np.zeros(config['sum_snap'])  # 时序数目
    # if 'wind_curtailment' in result_dict:
    #     wind_curtail = result_dict['wind_curtailment'].sum(axis=0)
    # solar_curtail = np.zeros(config['sum_snap'])
    # if 'solar_curtailment' in result_dict:
    #     solar_curtail = result_dict['solar_curtailment'].sum(axis=0)
    # load_init_data = result_dict['load'].sum(axis=0) + result_dict['load_curtailment'].sum(axis=0)  # 原始负荷时序

    curtail_df = pd.DataFrame()
    curtail_df['wind_curtail'] = wind_curtail  # 弃风
    curtail_df['solar_curtail'] = solar_curtail  # 弃光
    curtail_df['total_curtail'] = wind_curtail + solar_curtail
    curtail_df['load_ori'] = load_init_data  # 原始负荷时序

    # 弃电类型
    curtail_df['curtail_type'] = 0
    curtail_df.loc[(curtail_df['wind_curtail'] >= 1.0) & (curtail_df['solar_curtail'] >= 1.0), 'curtail_type'] = 3
    curtail_df.loc[(curtail_df['wind_curtail'] >= 1.0) & (curtail_df['solar_curtail'] < 1.0), 'curtail_type'] = 1
    curtail_df.loc[(curtail_df['wind_curtail'] < 1.0) & (curtail_df['solar_curtail'] >= 1.0), 'curtail_type'] = 2

    # 弃电原因分析
    curtail_df['curtail_reason'] = 0
    load_reserve_needcap = load_init_data * config['down_reserve_cof']  # 下备用需要值;
    curtail_df['reserve_down_need'] = load_reserve_needcap

    # 调峰缺口计算 # >0,缺口,调峰困难 = 开机机组最小出力 - 总负荷
    curtail_df['peaking_gap_arr'] = peaking_gap_arr  # 万千瓦
    # peaking_gap_arr > 0.1 && total_curtail < peaking_gap_arr + 1.0 curtail_df['curtail_reason'] = 1(调峰)
    curtail_df.loc[(curtail_df['peaking_gap_arr'] > 0.1) & (
            curtail_df['total_curtail'] < curtail_df['peaking_gap_arr'] + 1.0), 'curtail_reason'] = 1
    # peaking_gap_arr > 0.1 && total_curtail > peaking_gap_arr + 1.0 curtail_df['curtail_reason'] = 3(调峰 + 网架)
    curtail_df.loc[(curtail_df['peaking_gap_arr'] > 0.1) & (
            curtail_df['total_curtail'] > curtail_df['peaking_gap_arr'] + 1.0), 'curtail_reason'] = 3
    # peaking_gap_arr < 0.1 && total_curtail > 0 curtail_df['curtail_reason'] = 2(网架)
    curtail_df.loc[(curtail_df['peaking_gap_arr'] < 0.1) & (curtail_df['total_curtail'] > 0), 'curtail_reason'] = 2

    # 筛选有弃电的时刻;
    curtail_df = curtail_df[curtail_df['curtail_type'] > 0]

    new_cutail_type_dict = {0: '无', 1: '弃风', 2: '弃光', 3: '弃风弃光'}
    new_cutail_reason_dict = {0: '无', 1: '调峰', 2: '网架', 3: '调峰/网架'}
    new_curtail_data['curtail_type_define'] = new_cutail_type_dict  # 弃电类型定义
    new_curtail_data['curtail_reason_define'] = new_cutail_reason_dict  # 弃电原因定义

    new_curtail_data['curtail_time'] = curtail_df.index.tolist()  # 弃电时刻
    new_curtail_data['curtail_type'] = curtail_df['curtail_type'].values.tolist()  # 弃电类型
    new_curtail_data['curtail_reason'] = curtail_df['curtail_reason'].values.tolist()  # 弃电原因
    new_curtail_data['curtail_power'] = (curtail_df['total_curtail'].values).round(1).tolist()  # 弃电电力--万千瓦;

    return new_curtail_data


def grid_psm_distribute_ana(net, config: dict, result_dict: dict):
    """

    """
    # 计算调峰缺口;
    peaking_gap_arr = cal_sys_peaking_val(net, config, result_dict)

    # 计算供电缺口
    power_supply_slack_arr = cal_sys_loadcutailment_val(config, result_dict)

    mon_margin_dict, mon_day_margin_dict = get_grid_psm_distribute_info(config, peaking_gap_arr, power_supply_slack_arr)
    return mon_margin_dict, mon_day_margin_dict, power_supply_slack_arr, peaking_gap_arr


if __name__ == '__main__':
    ## 对算例进行预统计分析
    case_id = '2123214455656766'

    import time

    h5_file = 'D:\\code\\baogong_new\\result_data\\case_id_2123214455656766\\all_power_flow_data_2123214455656766.hdf5'
    h5_file2 = 'D:\\code\\baogong_new\\result_data\\case_id_2123214455656766\\all_power_flow_data_2123214455656766-2.h5'

    # 打开 HDF5 文件
    start_time = time.time()
    with h5py.File(h5_file2, 'r') as f:
        # 读取 'gen' dataset
        gen_data = f['line_power'][:]

    execution_time = (time.time() - start_time) * 1000  # 转换为毫秒
    print(f"h5py 代码块执行时间: {execution_time:.3f} 毫秒")
