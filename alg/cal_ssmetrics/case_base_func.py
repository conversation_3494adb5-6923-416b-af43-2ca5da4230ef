"""
teapcase的相关公共操作函数
"""
import pandas as pd
import numpy as np
from pandapower import pandapowerNet
from alg.cal_ssmetrics.base_func import get_time_range


class CTeapCase():
    def __init__(self, net: pandapowerNet, config_f: dict = dict()):
        self.net = net
        self.config = config_f

    def get_case_timerange(self):
        """
        获取算例时序;
        :return:
        """
        return get_time_range(time_start=self.config["start_time"], num_snaps=self.config["sum_snap"], freq='h')

    @classmethod
    def get_inf_collections(cls, net):
        """
        获取collection 2级断面的设备组成索引--线路/主变;
        """
        inf_dict = dict()  # 断面dict
        line_inters = net.line.loc[:, ['interface_direction', 'interface_name', 'interface_collection_name']].dropna()
        trafo_inters = net.trafo.loc[:, ['interface_direction', 'interface_name', 'interface_collection_name']].dropna()
        line_inters_unique = list(line_inters['interface_collection_name'].unique())
        trafo_inters_unique = list(trafo_inters['interface_collection_name'].unique())
        inters_collections = line_inters_unique + trafo_inters_unique
        for coll in inters_collections:
            inf_dict[coll] = dict()
            # line
            inf_c_line = line_inters[line_inters['interface_collection_name'] == coll]
            inf_dict[coll]['line'] = inf_c_line.index.tolist()
            # trafo
            inf_c_trafo = trafo_inters[trafo_inters['interface_collection_name'] == coll]
            inf_dict[coll]['trafo'] = inf_c_trafo.index.tolist()
            # relation--具体的关联关系;
            inf_dict[coll]['relation'] = dict()
            # 将inf_c_line按'interface_name'进行group分组;
            line_inf = dict()
            line_inf_direction = dict()
            if not inf_c_line.empty:
                for grp_key, group_val in inf_c_line.groupby('interface_name'):
                    line_inf[grp_key] = group_val.index.tolist()
                    group_val['interface_direction'].fillna(1, inplace=True)
                    line_inf_direction[grp_key] = group_val['interface_direction'].tolist()
            inf_dict[coll]['relation']["line"] = line_inf
            inf_dict[coll]['relation']["line_direction"] = line_inf_direction

            trafo_inf = dict()
            trafo_inf_direction = dict()
            if not inf_c_trafo.empty:
                for grp_key, group_val in inf_c_line.groupby('interface_name'):
                    trafo_inf[grp_key] = group_val.index.tolist()
                    group_val['interface_direction'].fillna(1, inplace=True)
                    trafo_inf_direction[grp_key] = group_val['interface_direction'].tolist()
            inf_dict[coll]['relation']["trafo"] = trafo_inf
            inf_dict[coll]['relation']["trafo_direction"] = trafo_inf_direction

        return inf_dict

    @classmethod
    def get_ele_key_series(cls, net, config, ele_: str, timeseries_key):
        """
        考虑时序gen_p_max时序曲线,修正机组的实际max_p_mw曲线;
        """
        # 获取self.net的'timeseries'列数据;
        ele_df = net[ele_]
        if ele_df.empty:
            return None

        # 生成一个shape=(len(self.net.gen), self.config['sum_snap'])的全部为nan的数组;
        ele_series = np.full((len(ele_df), config['sum_snap']), np.nan)
        # 使用items 遍历 self.ele
        i_no: int = -1
        for index, row in ele_df.iterrows():
            i_no += 1
            gen_i_timeseries = row['timeseries']
            if pd.isna(gen_i_timeseries):
                continue
            else:
                pass

            cond1 = isinstance(gen_i_timeseries, str)
            # 将gen_i_timeseries的中文'，'替换成','
            if not pd.isna(gen_i_timeseries) and isinstance(gen_i_timeseries, str):
                gen_i_timeseries = gen_i_timeseries.replace('，', ',')
            if (cond1 and ',' not in gen_i_timeseries) or (isinstance(gen_i_timeseries, int)):
                geni_series_no = int(gen_i_timeseries)
                geni_series_type = net.timeseries.loc[geni_series_no, 'type']
                if geni_series_type == timeseries_key:
                    ele_series[i_no, :] = net.timeseries.loc[geni_series_no].values[2:]
                else:
                    pass
            elif (cond1 and ',' in gen_i_timeseries) or (isinstance(gen_i_timeseries, list)):
                if cond1:
                    geni_series_no_list = gen_i_timeseries.split(',')
                else:
                    geni_series_no_list = gen_i_timeseries
                for gen_i_series_i in geni_series_no_list:
                    geni_series_no = int(gen_i_series_i)
                    geni_series_type = net.timeseries.loc[geni_series_no, 'type']
                    if geni_series_type == timeseries_key:
                        ele_series[i_no, :] = net.timeseries.loc[geni_series_no].values[2:]
                        break
                    else:
                        pass
        return ele_series

    @classmethod
    def get_network_topo_elements(cls):
        """
        获取网络拓扑数据;
        :return:
        dict,返回: {"point_data":[],"line_data":[]}
        """
        return dict()


#

