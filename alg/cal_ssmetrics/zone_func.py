"""
实现分区的关联及根据分区对结果的筛选和处理
"""
import pandas as pd


def get_area_level_df_ind(net, area_name, area_details: dict, res):
    """
    筛选出指定分区的结果，机组，风光场站序号
    area_details: 所有分区的关联设备集合dict
    """
    ### 如果有指定分区，先筛选输入词典中供电分区内信息
    # 额外增加在feedin中的‘type’= '营销分布式'的光伏 key: 'feedin_output_solar'

    if area_name is not None and area_name != '全省':
        if not area_details:
            raise ValueError(f"获取分区设备: 输入的分区:{area_name} ,传入的分区关联设备字典为空!")
        res_area = filter_per_supply_area(net, area_name,
                                          res.copy(),
                                          area_details)
    else:
        res_area = res.copy()
        # 额外增加在feedin中的‘type’= '营销分布式'的光伏 key: 'feedin_output_solar'
        if 'feedin_output' in res_area.keys():
            feedin_solar_idx = net['feedin'][net['feedin']['type'] == '营销分布式'].index
            if isinstance(res['feedin_output'], pd.DataFrame):
                res_area['feedin_output_solar'] = res['feedin_output'].loc[feedin_solar_idx, :]
            else:
                res_area['feedin_output_solar'] = res['feedin_output'][feedin_solar_idx, :]
    return res_area


def filter_per_supply_area(net, area_name, res_dict_mod, area_details):
    """
    筛选出指定分区的结果
    res_dict_mod: 全网所有的设备的计算结果;
    """
    area_key_mapping = {'line_power': 'line',  # 两个节点都在分区内
                        'trafo_power': 'trafo',
                        'gen_state': 'gen',
                        'gen_output': 'gen',
                        'oper_hour': 'gen',
                        'load': 'load',
                        'load_curtailment': 'load',
                        'feedin_output': 'feedin',
                        'stogen_output': 'stogen',
                        'wind_output': 'wind',
                        'wind_curtailment': 'wind',
                        'solar_output': 'solar',
                        'solar_curtailment': 'solar',
                        'dcline_power_from': 'dcline',
                        'dcline_power_to': 'dcline',
                        }

    res_dict_op =dict()
    for df_name in area_key_mapping.keys():
        if df_name in res_dict_mod.keys():
            target_index = area_details[area_name][area_key_mapping[df_name]]  # int
            if isinstance(res_dict_mod[df_name], pd.DataFrame):
                res_dict_op[df_name] = res_dict_mod[df_name].loc[target_index, :]
            else:
                res_dict_op[df_name] = res_dict_mod[df_name][target_index, :]

    # 额外增加在feedin中的‘type’= '营销分布式'的光伏 key: 'feedin_output_solar'
    if 'feedin_output' in res_dict_mod.keys():
        zone_feedin_index = area_details[area_name][area_key_mapping['feedin_output']]
        feedin_solar_idx = net['feedin'][net['feedin']['type'] == '营销分布式'].index
        # 筛选出 zone_feedin_index 在feedin_solar_idx中的元素
        zone_feedin_solar_index = list(set(zone_feedin_index).intersection(set(feedin_solar_idx)))
        if isinstance(res_dict_mod['feedin_output'], pd.DataFrame):
            res_dict_op['feedin_output_solar'] = res_dict_mod['feedin_output'].loc[zone_feedin_solar_index, :]
        else:
            res_dict_op['feedin_output_solar'] = res_dict_mod['feedin_output'][zone_feedin_solar_index, :]

    return res_dict_op
