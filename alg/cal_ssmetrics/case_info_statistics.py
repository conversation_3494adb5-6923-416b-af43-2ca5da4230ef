"""统计算例相关的静态数据信息
可输出至: case_info_case_id.json 文件;
"""
import os
import numpy as np
import json
import shelve
import pandas as pd
import pandapower as pp
from pandapower import pandapowerNet
from alg.get_graph_info.get_net_topo import get_format_graph_data
from alg.get_graph_info.zone_relay_devices import GetNetZoneRelayDeviesInfo
from alg.cal_ssmetrics.base_func import (GLOBAL_QW_NAME, cof_power, cof_energy)
from alg.cal_ssmetrics.case_base_func import CTeapCase
from alg.ProductionCostSimulation.utils.make_PTDF import (make_PTDF)  # make_PTDF_LODF

__all__ = ()


def CheckDirExist(dir: str):
    if not os.path.exists(dir):
        os.makedirs(dir)


class CCaseInfo():
    """算例的静态数据统计--返回json文件,保存,全网全局静态信息的文件;
    return:
    json: 'key': 'value', key:case_name
    """

    def __init__(self, net: pandapowerNet, result_dict: dict, config_f: dict = dict(),
                 area_bus_map_dict: dict = dict()):
        # read_data_from_dot_teap(dot_teap_file)
        self.net = net
        self.result_dict = result_dict
        self.statistics_dict = dict()  ##统计结果结构体;
        self.config = config_f
        self.case_id = config_f['case_id']
        self.save_dir = self.config['case_rlt_dir']

        self.ptdf = None  # topo数据;

        self.start_time = pd.to_datetime(self.config['start_time'])  # 算例计算开始时间;
        self.sim_freq = self.config['sim_freq']

        self.gen_types = list()  # case的机组类型list ;
        self.zone_list = list()  # case的分区信息;
        self.snapshot = len(self.net['timeseries'].columns.to_list()) - 2  # 算例的时序点数;
        self.zero_arr = np.zeros(self.snapshot)
        self.zero2_arr = np.zeros((1, self.snapshot))

        self.area_zone_map = area_bus_map_dict  # 分区设备映射母线索引;

        # 时序;数据--(n,self.series_num);
        self.series_arrs = dict()

        # conbine,时序--(self.series_num,);
        self.conb_arrs = dict()

    def save_case_configInfo(self):
        self.statistics_dict['config'] = self.config
        # 检查目录是否存在;'case_rlt_dir'
        CheckDirExist(self.config['case_rlt_dir'])

    def GetSimTimeSeries(self):
        """
        时间索引字符串;
        Returns:
        """
        _periods = pd.period_range(
            start=self.start_time, periods=self.snapshot, freq=self.sim_freq
        ).tolist()
        self.statistics_dict['time_range'] = [item.strftime("%Y-%m-%d %H:%M:%S") for item in _periods]

    def GetDeviceSeriesData(self, dev_type: str, col_name: str = ''):
        """
        获取设备的时序索引list;
        dev_type: wind/solar/feedin/load
        Returns: arrays: (m * n)
        """
        if dev_type not in self.net or self.net[dev_type].empty:
            return self.zero2_arr

        devs_timeseries = self.net[dev_type]['timeseries'].astype(int)  # 获取时序
        seriese_col_labels = self.net['timeseries'].columns[2:]
        devs_series = self.net['timeseries'].loc[devs_timeseries, seriese_col_labels].values  # 获取时序对应的timeseries

        if col_name != '' and col_name in self.net[dev_type]:
            device_col_val = self.net[dev_type][col_name].values  # 获取负荷时序;
            dev_val_series = device_col_val[:, np.newaxis] * devs_series
        else:
            device_col_val = None
            dev_val_series = devs_series
        return dev_val_series, device_col_val

    def get_case_zones(self):
        """
        获取系统的所有分区列表;
        Returns: list
        """
        # zones = list()
        # zones_ori = self.net.bus['zone'].unique()
        # for i_zone in zones_ori:
        #     if pd.isna(i_zone) or i_zone == '':
        #         continue
        #     zones.append(i_zone)
        # self.zone_list = zones
        zones = self.statistics_dict['zone']['device_relay'].keys()
        self.zone_list = zones
        return zones

    def get_case_gen_types(self):
        """
        获取机组类型list
        Returns:
            list,gen_type+ wind+solar+feedin
        """
        if 'gen' in self.net.keys() and 'type' in self.net.gen.columns.to_list():
            self.gen_types += list(self.net.gen['type'].unique())
        if 'wind' in self.net.keys() and not self.net.wind.empty:
            self.gen_types.append('wind')
        if 'solar' in self.net.keys() and not self.net.solar.empty:
            self.gen_types.append('solar')
        if 'feedin' in self.net.keys() and not self.net.feedin.empty:
            self.gen_types.append('feedin')
        return

    # 统计设备数目清单; 500kV及以上主变台数，线路条数; 分区数目;
    def get_grid_device_num(self, vn_kv: int = 500):
        """
        统计设备数目清单; 500kV及以上主变台数，线路条数; 分区数目;
        Returns:
        """
        self.statistics_dict['deviceNum'] = dict()

        # 统计500kV以上主变台数; 只找"vn_hv_kv">=0.95 * 500 & "in_service" ==True的数目
        trafo_valid_df = self.net.trafo[(self.net.trafo['vn_hv_kv'] >= 0.95 * vn_kv) & self.net.trafo['in_service']]
        self.statistics_dict['deviceNum']['trafo'] = len(trafo_valid_df)  # 主变台数;
        # 统计line, 'in_service' ==True & 'vn_kv'>= 0.95 * vn_kv
        line_valid_df = self.net.line[(self.net.line['vn_kv'] >= 0.95 * vn_kv) & self.net.line['in_service']]
        self.statistics_dict['deviceNum']['line'] = len(line_valid_df)
        # 分区数目
        self.statistics_dict['deviceNum']['zone'] = len(self.zone_list)

        return

    def get_gentype_capacity(self):
        """
        获取系统的不同类型机组的装机信息;
        Returns:
        """
        self.statistics_dict['capacity'] = dict()
        self.statistics_dict['genNumber'] = dict()
        all_capacity_dict = dict()  # 装机信息
        all_gennumber_dict = dict()  # 机组数目信息
        self.get_case_gen_types()

        # 根据'type'列的值分组，并计算'val'列的和
        gen_grouped = self.net.gen.groupby('type')
        for i_grp, grp_val in gen_grouped:
            grp_val = grp_val[grp_val['in_service']]
            all_capacity_dict[i_grp] = float(grp_val.loc[:, 'max_p_mw'].sum())
            all_gennumber_dict[i_grp] = len(grp_val)

        # 集中式/分布式光伏统计;
        if 'solar' in self.gen_types:
            solar_df = self.net.solar[self.net.solar['in_service']]
            all_capacity_dict['solar'] = float(solar_df['max_p_mw'].sum())
            all_gennumber_dict['solar'] = len(solar_df)
            # 'type' == 'centralized' / 'distributed' # 获取 'type' == 'centralized' 的记录的'max_p_mw'和
            central_solar_df = solar_df.loc[solar_df['type'] == 'centralized', 'max_p_mw'].sum()
            all_capacity_dict['centralized_solar'] = float(central_solar_df)
            dg_max_p = solar_df.loc[solar_df['type'] == 'distributed', 'max_p_mw'].sum()
            all_capacity_dict['distributed_solar'] = float(dg_max_p)
        else:
            all_capacity_dict['solar'] = 0
            all_gennumber_dict['solar'] = 0
            all_capacity_dict['centralized_solar'] = 0
            all_capacity_dict['distributed_solar'] = 0

        # 光伏的机组数目需要加上feedin中的营销光伏数目
        if not self.net.feedin.empty:
            solar_feedin_df = self.net.feedin[(self.net.feedin['type'] == '营销分布式') & self.net.feedin['in_service']]
            solar_feedin_num = len(solar_feedin_df)
            all_capacity_dict['solar'] += solar_feedin_df['max_p_mw'].sum()
            all_gennumber_dict['solar'] += solar_feedin_num

        # 集中式风电/分散式风电统计;
        if 'wind' in self.gen_types:
            wind_df = self.net.wind[self.net.wind['in_service']]
            all_capacity_dict['wind'] = float(wind_df['max_p_mw'].sum())
            all_gennumber_dict['wind'] = len(wind_df)
            # 'type' == 'centralized' / 'distributed'
            central_wind_df = wind_df.loc[wind_df['type'] == 'centralized', 'max_p_mw'].sum()
            all_capacity_dict['centralized_wind'] = float(central_wind_df)
            dis_wind_max_p = wind_df.loc[wind_df['type'] == 'distributed', 'max_p_mw'].sum()
            all_capacity_dict['distributed_wind'] = float(dis_wind_max_p)
        else:
            all_capacity_dict['wind'] = 0
            all_gennumber_dict['wind'] = 0
            all_capacity_dict['centralized_wind'] = 0
            all_capacity_dict['distributed_wind'] = 0
        if 'feedin' in self.gen_types:
            feedin_df = self.net.feedin[self.net.feedin['in_service'] & (self.net.feedin['type'] != '营销分布式')]
            all_capacity_dict['feedin'] = float(feedin_df['max_p_mw'].sum())
            all_gennumber_dict['feedin'] = len(feedin_df)
        else:
            all_capacity_dict['feedin'] = 0
            all_gennumber_dict['feedin'] = 0

        # stogen的容量合计
        if 'stogen' in self.net.keys() and not self.net.stogen.empty:
            stogen_pump_df = self.net.stogen[
                self.net.stogen['in_service'] & (self.net.stogen['type'].str.contains('pump'))]
            all_capacity_dict['stogen_pump'] = float(stogen_pump_df.loc[:, 'max_p_discharge_mw'].sum())
            all_gennumber_dict['stogen_pump'] = len(stogen_pump_df)
            stogen_battery_df = self.net.stogen[self.net.stogen['in_service'] & (
                    self.net.stogen['type'].str.contains('battery') | self.net.stogen['type'].str.contains('energy'))]
            all_capacity_dict['stogen_battery'] = float(stogen_battery_df.loc[:, 'max_p_discharge_mw'].sum())
            all_gennumber_dict['stogen_battery'] = len(stogen_battery_df)
        else:
            all_capacity_dict['stogen_pump'] = 0
            all_gennumber_dict['stogen_pump'] = 0
            all_capacity_dict['stogen_battery'] = 0
            all_gennumber_dict['stogen_battery'] = 0

        if self.config['power_unit'] != 'M':
            # 所有数据单位转换;MW->万千瓦,并保留0小数;
            for key, _ in all_capacity_dict.items():
                all_capacity_dict[key] = round(all_capacity_dict[key] * cof_power, 0)
        else:
            for key, _ in all_capacity_dict.items():
                all_capacity_dict[key] = round(all_capacity_dict[key], 0)
        self.statistics_dict['capacity'][GLOBAL_QW_NAME] = all_capacity_dict  # 全网装机信息
        self.statistics_dict['genNumber'][GLOBAL_QW_NAME] = all_gennumber_dict  # 全网机组数目信息

        # 增加统计分区的容量信息
        self.get_case_zones()
        for i_zone in self.zone_list:
            zone_capacity_dict = dict()  # 装机信息
            zone_gennumber_dict = dict()  # 机组数目信息

            # zones = self.statistics_dict['zone']['zone_map'][i_zone]
            zones_bus = self.statistics_dict['zone']['device_relay'][i_zone]['bus']

            for i_grp, grp_val in gen_grouped:
                grp_val_1 = grp_val[grp_val['bus'].isin(zones_bus) & grp_val['in_service']]
                zone_capacity_dict[i_grp] = float(grp_val_1.loc[:, 'max_p_mw'].sum())
                zone_gennumber_dict[i_grp] = len(grp_val_1)

            if 'wind' in self.gen_types:
                wind_df = self.net.wind[self.net.wind['bus'].isin(zones_bus) & self.net.wind['in_service']]
                zone_capacity_dict['wind'] = float(wind_df.loc[:, 'max_p_mw'].sum())
                zone_gennumber_dict['wind'] = len(wind_df)
            else:
                zone_capacity_dict['wind'] = 0
                zone_gennumber_dict['wind'] = 0

            if 'solar' in self.gen_types:
                solar_df = self.net.solar[self.net.solar['bus'].isin(zones_bus) & self.net.solar['in_service']]
                zone_capacity_dict['solar'] = float(solar_df.loc[:, 'max_p_mw'].sum())
                zone_gennumber_dict['solar'] = len(solar_df)
            else:
                zone_capacity_dict['solar'] = 0
                zone_gennumber_dict['solar'] = 0
            if 'feedin' in self.gen_types:
                feedin_df = self.net.feedin
                zn_feedin = feedin_df.loc[
                    feedin_df['bus'].isin(zones_bus) & feedin_df['in_service'] & (~(feedin_df['type'] == '营销分布式'))]
                zone_capacity_dict['feedin'] = float(zn_feedin.loc[:, 'max_p_mw'].sum())
                zone_gennumber_dict['feedin'] = len(zn_feedin)
                # 营销分布式
                zn_feedin_solar_df = feedin_df.loc[
                    feedin_df['bus'].isin(zones_bus) & feedin_df['in_service'] & (feedin_df['type'] == '营销分布式')]
                zone_capacity_dict['solar'] += float(zn_feedin_solar_df.loc[:, 'max_p_mw'].sum())
                zone_gennumber_dict['solar'] += len(zn_feedin_solar_df)
            else:
                zone_capacity_dict['feedin'] = 0
                zone_gennumber_dict['feedin'] = 0

            # 增加分区储能/抽蓄的统计;
            if 'stogen' in self.net.keys() and not self.net.stogen.empty:
                zn_pump_df = self.net.stogen[self.net.stogen['bus'].isin(zones_bus) &
                    self.net.stogen['in_service'] & (self.net.stogen['type'].str.contains('pump'))]
                zone_capacity_dict['stogen_pump'] = float(zn_pump_df.loc[:, 'max_p_discharge_mw'].sum())
                zone_gennumber_dict['stogen_pump'] = len(zn_pump_df)
                zn_battery_df = self.net.stogen[self.net.stogen['bus'].isin(zones_bus) & self.net.stogen['in_service'] & (
                        self.net.stogen['type'].str.contains('battery') | self.net.stogen['type'].str.contains(
                    'energy'))]
                zone_capacity_dict['stogen_battery'] = float(zn_battery_df.loc[:, 'max_p_discharge_mw'].sum())
                zone_gennumber_dict['stogen_battery'] = len(zn_battery_df)
            else:
                zone_capacity_dict['stogen_pump'] = 0
                zone_gennumber_dict['stogen_pump'] = 0
                zone_capacity_dict['stogen_battery'] = 0
                zone_gennumber_dict['stogen_battery'] = 0

            if self.config['power_unit'] != 'M':
                # 所有数据单位转换;MW->万千瓦,并保留0小数;
                for key, _ in zone_capacity_dict.items():
                    zone_capacity_dict[key] = round(zone_capacity_dict[key] * cof_power, 0)
            else:
                for key, _ in zone_capacity_dict.items():
                    zone_capacity_dict[key] = round(zone_capacity_dict[key], 0)

            self.statistics_dict['capacity'][i_zone] = zone_capacity_dict  # 装机信息
            self.statistics_dict['genNumber'][i_zone] = zone_gennumber_dict  # 机组数目信息
        return

    def get_devs_series_arrs(self):
        """
        获取相关数据的时序;
        Returns:
        """
        self.statistics_dict['series'] = dict()  # 算例原始聚合时序数据

        type_list = ['load', 'wind', 'solar', 'feedin']
        for i_type in type_list:
            devs_val_series, _ = self.GetDeviceSeriesData(i_type, 'max_p_mw')
            self.series_arrs[i_type] = devs_val_series
            conb_arr = devs_val_series.sum(axis=0)
            self.conb_arrs[i_type] = conb_arr
            self.statistics_dict['series'][i_type] = conb_arr.tolist()

            # wind/solar
            if i_type in ['wind', 'solar']:
                # 增加集中式\分布式新能源的信息获取
                dis_ele_name = f'distributed_{i_type}'  # 分布式
                cen_ele_name = f'centralized_{i_type}'  # 集中式
                # 获取对应索引
                dis_ele_index = self.net[i_type].loc[self.net[i_type]['type'] == 'distributed'].index.tolist()
                cen_ele_index = self.net[i_type].loc[~self.net[i_type].index.isin(dis_ele_index)].index.tolist()
                # distributed
                dis_devs_val_series = devs_val_series[dis_ele_index, :]
                self.series_arrs[dis_ele_name] = dis_devs_val_series
                dis_conb_arr = dis_devs_val_series.sum(axis=0)
                self.conb_arrs[dis_ele_name] = dis_conb_arr
                self.statistics_dict['series'][dis_ele_name] = dis_conb_arr.tolist()
                # centered
                cen_devs_val_series = devs_val_series[cen_ele_index, :]
                self.series_arrs[cen_ele_name] = cen_devs_val_series
                cen_conb_arr = cen_devs_val_series.sum(axis=0)
                self.conb_arrs[cen_ele_name] = cen_conb_arr
                self.statistics_dict['series'][cen_ele_name] = cen_conb_arr.tolist()
        return

    def get_dev_maxvals(self):
        """
        分析系统的风光/load/峰谷差的最大值及时刻;
        Returns:
        """
        self.statistics_dict['max_val'] = dict()
        self.statistics_dict['ori_elec'] = dict()

        for i_ele in ['load', 'wind', 'solar']:
            ele_maxp_dict, ele_eleric_dict = funcGetmax_p_mw(self.net, self.config, i_ele, self.conb_arrs)
            self.statistics_dict['max_val'].update(ele_maxp_dict)
            self.statistics_dict['ori_elec'].update(ele_eleric_dict)

        return

    def CalLoadPeakingInfo(self):
        """
        计算负荷的日峰谷差信息;
        Returns:
        """
        ##load
        load_conb_arr = self.conb_arrs['load']  # 获取总负荷时序;
        tuple_peaking_valley = caldaliyload_peak_valley_max(self.config, load_conb_arr, start_time_no=0,
                                                            snapshot=self.config['sum_snap'])
        self.statistics_dict['max_val']['load_peak_valley'] = tuple_peaking_valley
        return

    def GetZoneRelayDeviesInfo(self):
        """
        规整分区关联的所有设备索引;
        分区包含bus中表中的zone,owner,area中不为空的;
        Returns:
        """
        zone_dict, zone_device_dict = GetNetZoneRelayDeviesInfo(self.net, self.config, self.area_zone_map)
        self.statistics_dict['zone'] = dict()
        self.statistics_dict['zone']['zone_map'] = zone_dict
        self.statistics_dict['zone']['device_relay'] = zone_device_dict

    def get_collection_inf_relationship(self, net):
        """
        获取2级通道(interface_collection_name) 和 普通断面的从属关系
        :return:
        dict
        """
        self.statistics_dict['inf_collection'] = CTeapCase.get_inf_collections(net)  # 二级断面

    def GetNetTopo(self, net, config):
        """
        获取电网拓扑信息(全电压等级,由前端过滤);--节点线路已经经过经纬度进行了聚合;
        Returns:
        """
        selected_vn_kv = min(220, int(config['topo_dis_vn_kv']))  # 预处理时保留220kV及以上的全部设备;取小兼容配网拓扑情形;
        net_topo_dict = get_format_graph_data(net, selected_vn_kv)

        return net_topo_dict

    def GetNetTopoInfo(self):
        """
        获取电网拓扑信息(全电压等级,由前端过滤);--节点线路已经经过经纬度进行了聚合;
        Returns:
        """
        self.statistics_dict['topo_info'] = self.GetNetTopo(self.net, self.config)

        # case_net_file =os.path.join(self.save_dir, f"case_net_{self.case_id}.p")
        # pp.to_pickle(self.net, case_net_file)  # absolute path

        # case_net_json_file =os.path.join(self.save_dir, f"case_net_{self.case_id}.json")
        # pp.to_json(self.net, case_net_json_file)  # absolute path

        # 计算静态的ptdf# 使用h5存储numpy矩阵
        self.ptdf = make_PTDF(self.net, select_idx=self.config['selected_branch_idx'], config=self.config,
                              distributed_slack=False)
        import h5py
        h5file = os.path.join(self.save_dir, f'net_ptdf_{self.case_id}.hdf5')
        with h5py.File(h5file, 'w') as f:
            p_out_grp = f.create_group("net_ptdf")  # 创建一个名为'my_group'的group
            p_out_grp.create_dataset('ptdf', data=self.ptdf)  # 写入dataset

        # 使用pickle保存网络
        import pickle
        case_net_pkl_file = os.path.join(self.save_dir, f"case_net_{self.case_id}.pkl")
        with open(case_net_pkl_file, 'wb') as f:
            pickle.dump(self.net, f)
        return  # net_topo_dict

    @classmethod
    def get_gen_pmax_series(cls, net, config, timeseries_key: str = 'gen_p_max'):
        """
        考虑时序gen_p_max时序曲线,修正机组的实际max_p_mw曲线;
        """
        # 获取self.net的'timeseries'列数据;
        if net.gen.empty:
            return None

        # 生成一个shape=(len(self.net.gen), self.config['sum_snap'])的全部为1的数组;
        gen_pmax_series = np.ones((len(net.gen), config['sum_snap']))
        # 使用items 遍历 self.net.gen
        i_no: int = -1
        for index, row in net.gen.iterrows():
            i_no += 1
            gen_i_timeseries = row['timeseries']
            if pd.isna(gen_i_timeseries):
                continue
            else:
                pass

            cond1 = isinstance(gen_i_timeseries, str)
            # 将gen_i_timeseries的中文'，'替换成','
            if not pd.isna(gen_i_timeseries) and cond1:
                gen_i_timeseries = gen_i_timeseries.replace('，', ',')
            if (cond1 and ',' in gen_i_timeseries) or (isinstance(gen_i_timeseries, list)):
                if cond1:
                    geni_series_no_list = gen_i_timeseries.split(',')
                else:
                    geni_series_no_list = gen_i_timeseries
                for gen_i_series_i in geni_series_no_list:
                    geni_series_no = int(gen_i_series_i)
                    geni_series_type = net.timeseries.loc[geni_series_no, 'type']
                    if geni_series_type == timeseries_key:
                        gen_pmax_series[i_no, :] = net.timeseries.loc[geni_series_no].values[2:]
                        break
                    else:
                        pass
            else:
                geni_series_no = int(gen_i_timeseries)   # 检验索引是否有效;
                geni_series_type = net.timeseries.loc[geni_series_no, 'type']
                if geni_series_type == timeseries_key:
                    gen_pmax_series[i_no, :] = net.timeseries.loc[geni_series_no].values[2:]
                else:
                    pass
        return gen_pmax_series

    def get_case_info(self, tofile: bool = False):
        """
        统计算例的静态数据： (1)装机信息; (2)最大负荷/时刻.最大峰谷差\日;
        tofile:True将结果存成文件;
        """
        # logger
        print("开始进行算例基础分析")

        # save case config info
        self.save_case_configInfo()

        # 时间索引str-list
        self.GetSimTimeSeries()

        # 规整分区关联的设备索引;
        self.GetZoneRelayDeviesInfo()

        # 分析(是否有)2级通道和1级断面的关联关系
        self.get_collection_inf_relationship(self.net)

        # 统计系统各类型机组的初始装机/数目信息;
        self.get_gentype_capacity()

        # 统计设备数目清单; 500kV及以上主变台数，线路条数; 分区数目;
        self.get_grid_device_num()

        # 获取并填充相关设备的时序数据;
        # 负荷/风/光伏/火电/区外的功率时序曲线(未聚合+聚合)；
        self.get_devs_series_arrs()

        # 统计风光、负荷 差的最大值及时刻;--及时序电量和
        self.get_dev_maxvals()

        # 计算负荷日峰谷差
        self.CalLoadPeakingInfo()

        # # 获取拓扑数据信息;
        self.GetNetTopoInfo()

        self.caseinfo_tojson(self.statistics_dict, tofile)

    def caseinfo_tojson(self, data_dict: dict, tofile: bool = False, key_wd: str = 'basic_info', file_format='shelve'):
        """将统计结果保存为json文件
        file_format: 文件存储格式;
        """
        if tofile:
            # 获取self.save_file的目录
            if not os.path.exists(self.save_dir):
                os.makedirs(self.save_dir)
            if file_format == 'json':
                # 打开一个文件并将字典直接写入为JSON格式
                case_info_file = os.path.join(self.save_dir, f"{key_wd}_{self.case_id}.json")
                with open(case_info_file, "w") as f:
                    json.dump(data_dict, f)
            elif file_format == 'shelve':
                # 创建或打开一个shelf文件
                case_info_file = os.path.join(self.save_dir, f"{key_wd}_{self.case_id}.db")
                with shelve.open(case_info_file) as shelf:
                    shelf['data'] = data_dict
            else:
                raise ValueError(f"输入的保存格式暂不支持:{file_format}")
                # # 读取数据
                # with shelve.open(case_info_file) as shelf:
                #     print(shelf['data'])
        else:
            #  json_str = json.dumps(data_dict)
            pass
        return

    def casenet_tojson(self, net_file: str):
        """将算例网络保存为json文件
        """
        pp.to_json(self.net, net_file)
        return

    ##################################################################


"""
非类函数定义
"""


def caldaliyload_peak_valley_max(config: dict, load_conb_arr, start_time_no: int = 0, snapshot: int = 8760):
    """
    计算日最大负荷峰谷差
    """
    sim_snapshot = config['sum_snap']
    start_time = config['start_time']
    sim_freq = config['sim_freq']

    # #统计峰谷差;先按日聚合;然后日负荷最大值;日负荷最小值，做差; 统计峰谷差;
    # 从开始开始，到2025年12月31日结束，按日聚合，然后日负荷最大值;日负荷最小值，做差; 统计峰谷差;
    _df = pd.DataFrame({"data": range(sim_snapshot)})
    _periods = pd.period_range(
        start=start_time, periods=len(_df), freq=sim_freq
    )
    _df.index = _periods  # 将周期索引设置为DataFrame的索引

    # 根据start_time_no选择
    if start_time_no != 0 or snapshot != sim_snapshot:
        _df = _df[start_time_no:start_time_no + snapshot]

    _df['value'] = load_conb_arr  # 负荷时序值;

    # 按日聚合;使用resample方法按日('D')重采样，并取'val'列的最大值
    load_daily_max = _df.resample('D')['value'].max()
    load_daily_min = _df.resample('D')['value'].min()

    delta_daily_val = load_daily_max - load_daily_min
    max_range_date = (delta_daily_val.idxmax()).strftime('%Y-%m-%d')

    if config['power_unit'] == 'M':
        max_range_value = round(float(delta_daily_val.max()), 1)
    else:
        max_range_value = round(float(delta_daily_val.max() * cof_power), 1)
    return [max_range_value, max_range_date]


def funcGetmax_p_mw(case_net, config, ele_type, series_arr_dict, start_idx: int = 0):
    """
    统计相关的电力极值与总电量;支持任意的起止时间段的series_arrs
    """
    # devs_val_series = self.series_arrs[type]
    # total_series = devs_val_series.sum(axis=0)  # (n,)
    max_p_dict = dict()
    ori_elec = dict()

    ele_series_arr = np.array(series_arr_dict[ele_type])  # (n,)
    if config['power_unit'] == 'M':
        max_p = round(float(np.max(ele_series_arr)), 1)
        all_total = round(float(ele_series_arr.sum()), 1)
    else:
        max_p = round(float(np.max(ele_series_arr) * cof_power), 1)  # 万千瓦;
        all_total = round(float(ele_series_arr.sum() * cof_energy), 1)  # 亿千瓦时
    max_index = int(np.argmax(ele_series_arr))
    shift_max_index = max_index + int(start_idx)  # 表示最大值在全年0点开始时刻的时序序号;
    max_p_dict[ele_type] = [max_p, shift_max_index]  # 最大值,时序时刻;
    # 增加统计分布式光伏和集中式光伏的电量和出力
    if ele_type in ['solar', 'wind']:
        if len(case_net[ele_type]) > 0 and f"centralized_{ele_type}" in series_arr_dict.keys():
            # 分布式索引
            dis_ele_arrs = np.array(series_arr_dict[f"distributed_{ele_type}"])
            dis_ele_p_mw = float(dis_ele_arrs[max_index])  # 分布式出力
            # 集中式索引
            cen_ele_arrs = np.array(series_arr_dict[f"centralized_{ele_type}"])
            central_ele_p_mw = float(cen_ele_arrs[max_index])  # 分布式出力
            if config['power_unit'] == 'M':
                dis_ele_p_mw = round(dis_ele_p_mw, 1)
                central_ele_p_mw = round(central_ele_p_mw, 1)
            else:
                dis_ele_p_mw = round(dis_ele_p_mw * cof_power, 1)
                central_ele_p_mw = round(central_ele_p_mw * cof_power, 1)
            max_p_dict[f'distributed_{ele_type}'] = [dis_ele_p_mw, shift_max_index]  # 光伏最大值,时序时刻对应的分布式光伏出力;
            max_p_dict[f'centralized_{ele_type}'] = [central_ele_p_mw, shift_max_index]  # 光伏最大值,时序时刻对应的统调式光伏出力;
        else:
            max_p_dict[f'distributed_{ele_type}'] = [0, shift_max_index]
            max_p_dict[f'centralized_{ele_type}'] = [0, shift_max_index]
    ori_elec[ele_type] = [all_total, '亿kWh']  # 总电量; float
    return max_p_dict, ori_elec
