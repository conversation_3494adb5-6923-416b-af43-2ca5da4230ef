"""
分析结果的地市数据分布特性
分析小时.日.月的数据特征分布
"""
import numpy as np
import pandas as pd
from alg.cal_ssmetrics.base_func import get_time_range, cof_energy, cof_power


def result_distribute_cal(net, config, result_dict, rlt_conbin_dict: dict, peaking_gap_arr):
    """
    统计分析结果数据的小时、月分布特性,输入的单位是万千瓦
    result_dict: teap计算结果;
    rlt_conbin_dict: 算例结果的所有聚合的8760时序曲线
    peaking_gap_arr: 调峰缺口时序;
    Returns:
    """
    num_snaps = config['sum_snap']
    zero_arr = np.zeros(num_snaps)

    case_start_time = config['start_time']
    _tm_df = get_time_range(case_start_time, num_snaps)
    if config['n_element']['wind'] > 0:
        _tm_df['wind_output'] = rlt_conbin_dict['wind_output']
        _tm_df['wind_curtailment'] = rlt_conbin_dict['wind_curtailment']

        centralized_wind_idx = net.wind[net.wind['type'] == 'centralized'].index.tolist()
        _tm_df['centralized_wind'] = result_dict['wind_output'][centralized_wind_idx, :].sum(
            axis=0) * cof_power  # 集中式风电
        distributed_wind_idx = net.wind[net.wind['type'] == 'distributed'].index.tolist()
        _tm_df['distributed_wind'] = result_dict['wind_output'][distributed_wind_idx, :].sum(axis=0) * cof_power
    else:
        _tm_df['wind_output'] = zero_arr
        _tm_df['wind_curtailment'] = zero_arr
        _tm_df['centralized_wind'] = zero_arr
        _tm_df['distributed_wind'] = zero_arr

    if config['n_element']['solar'] > 0:
        _tm_df['solar_output'] = rlt_conbin_dict['solar_output']
        _tm_df['solar_curtailment'] = rlt_conbin_dict['solar_curtailment']
        centralized_solar_idx = net.solar[net.solar['type'] == 'centralized'].index.tolist()
        _tm_df['centralized_solar'] = result_dict['solar_output'][centralized_solar_idx, :].sum(axis=0) * cof_power
        distributed_solar_idx = net.solar[net.solar['type'] == 'distributed'].index.tolist()
        _tm_df['distributed_solar'] = result_dict['solar_output'][distributed_solar_idx, :].sum(axis=0) * cof_power
    else:
        _tm_df['solar_output'] = zero_arr
        _tm_df['solar_curtailment'] = zero_arr
        _tm_df['centralized_solar'] = zero_arr
        _tm_df['distributed_solar'] = zero_arr

    _tm_df['load'] = rlt_conbin_dict['load_data']  # 负荷原始, 接纳负荷 + 负荷削减值
    _tm_df['load_curtailment'] = rlt_conbin_dict['load_curtailment']

    # 增加 备用的数据列
    if config['n_element']['stogen'] > 0:
        _tm_df['stogen_output'] = rlt_conbin_dict['stogen_output']  # 储能数据
    else:
        _tm_df['stogen_output'] = zero_arr
    _tm_df['reserve_up'] = rlt_conbin_dict['reserve_up']  # 正备用数据
    _tm_df['reserve_down'] = rlt_conbin_dict['reserve_down']  # 负备用数据
    up_reserve_cof = config['up_reserve_cof']
    # up_reserve_cof = 0.05 if up_reserve_cof < 0.001 else up_reserve_cof
    _tm_df['reserve_up_need'] = np.array(rlt_conbin_dict['load_data']) * up_reserve_cof  # 正备用数据
    down_reserve_cof = config['down_reserve_cof']
    # down_reserve_cof = 0.05 if down_reserve_cof < 0.001 else down_reserve_cof
    _tm_df['reserve_down_need'] = np.array(rlt_conbin_dict['load_data']) * down_reserve_cof

    # 增加调峰缺口数据列, 调峰缺口 = gen_on_min + 风光水+受入 - 负荷 > 0
    _tm_df['peaking_gap'] = peaking_gap_arr

    # 增加抽蓄放电/充电时序;
    bpump = False
    battery = False
    pump_max_discharge_mw = 0.1
    battery_max_discharge_mw = 0.1
    if config['n_element']['stogen'] > 0:
        # 抽蓄
        pump_mask = net.stogen['type'].str.contains('pump', case=False, na=False)
        if not pump_mask.empty:
            bpump = True
            pump_index = net.stogen[pump_mask].index.tolist()
            pump_max_discharge_mw = sum(net.stogen.loc[pump_index, 'max_p_discharge_mw'].values)
            pump_output_arr = result_dict['stogen_output'][pump_index, :]
            # 抽蓄:max_p_discharge_mw
            # 抽蓄放电电量
            pump_output_arr1 = pump_output_arr.copy()
            pump_output_arr1[pump_output_arr1 < 0] = 0
            pump_dis_sum_arr = pump_output_arr1.sum(axis=0)
            # 抽蓄充电电量
            pump_output_arr2 = pump_output_arr.copy()
            pump_output_arr2[pump_output_arr2 > 0] = 0
            pump_charge_sum_arr = pump_output_arr2.sum(axis=0) * (-1)
            _tm_df['pump_discharge'] = pump_dis_sum_arr
            _tm_df['pump_charge'] = pump_charge_sum_arr
        # 储能
        battery_mask = net.stogen['type'].str.contains('battery', case=False, na=False) | net.stogen[
            'type'].str.contains('energy', case=False, na=False)
        if not battery_mask.empty:
            battery = True
            battery_index = net.stogen[battery_mask].index.tolist()
            battery_max_discharge_mw = sum(net.stogen.loc[battery_index, 'max_p_discharge_mw'].values)
            battery_output_arr = result_dict['stogen_output'][battery_index, :]
            # 抽蓄:max_p_discharge_mw
            # 抽蓄放电电量
            battery_output_arr1 = battery_output_arr.copy()
            battery_output_arr1[battery_output_arr1 < 0] = 0
            battery_dis_sum_arr = battery_output_arr1.sum(axis=0)
            # 抽蓄充电电量
            battery_output_arr2 = battery_output_arr.copy()
            battery_output_arr2[battery_output_arr2 > 0] = 0
            battery_charge_sum_arr = battery_output_arr2.sum(axis=0) * (-1)
            _tm_df['battery_discharge'] = battery_dis_sum_arr
            _tm_df['battery_charge'] = battery_charge_sum_arr
    if not bpump:
        _tm_df['pump_discharge'] = np.zeros(num_snaps)
        _tm_df['pump_charge'] = np.zeros(num_snaps)
    if not battery:
        _tm_df['battery_discharge'] = np.zeros(num_snaps)
        _tm_df['battery_charge'] = np.zeros(num_snaps)

    # 按日聚合;
    daily_electric_data = dict()
    _tm_daily_sum_df = _tm_df.resample('D').sum()
    daily_electric_data['wind_output'] = _tm_daily_sum_df['wind_output'].values.tolist()
    daily_electric_data['wind_curtailment'] = _tm_daily_sum_df['wind_curtailment'].values.tolist()
    daily_electric_data['centralized_wind'] = _tm_daily_sum_df['centralized_wind'].values.tolist()
    daily_electric_data['distributed_wind'] = _tm_daily_sum_df['distributed_wind'].values.tolist()
    daily_electric_data['solar_output'] = _tm_daily_sum_df['solar_output'].values.tolist()
    daily_electric_data['solar_curtailment'] = _tm_daily_sum_df['solar_curtailment'].values.tolist()
    daily_electric_data['centralized_solar'] = _tm_daily_sum_df['centralized_solar'].values.tolist()
    daily_electric_data['distributed_solar'] = _tm_daily_sum_df['distributed_solar'].values.tolist()
    daily_electric_data['load'] = _tm_daily_sum_df['load'].values.tolist()
    daily_electric_data['load_curtailment'] = _tm_daily_sum_df['load_curtailment'].values.tolist()
    daily_electric_data['pump_discharge'] = (
            _tm_daily_sum_df['pump_discharge'].values / pump_max_discharge_mw).tolist()  # pump已经转为利用小时
    daily_electric_data['pump_charge'] = (
            _tm_daily_sum_df['pump_charge'].values / pump_max_discharge_mw).tolist()  # pump已经转为利用小时
    daily_electric_data['battery_discharge'] = (
            _tm_daily_sum_df['battery_discharge'].values / battery_max_discharge_mw).tolist()  # battery放电已经转为利用小时
    daily_electric_data['battery_charge'] = (
            _tm_daily_sum_df['battery_charge'].values / battery_max_discharge_mw).tolist()  # battery充电转为利用小时

    def cal_electric_distribute(_tm_df, freq_h):
        if freq_h not in ['hour', 'month']:
            raise ValueError(f"输入的cal_electric_distribute功能的{freq_h}参数不支持")
        num_list = 24 if freq_h == 'hour' else 12
        distribute_dict = {
            'wind': dict(),
            'solar': dict(),
        }
        for i_type in ['wind', 'solar']:
            for i_key in ['electric', 'curtailment']:
                distribute_dict[i_type][i_key] = [0] * num_list  # np.zeros(24)

        def func_getdata_df(data_dict, period_i, group_h_i):
            if 'wind_output' in group_h_i.columns.tolist():
                data_dict['wind']['electric'][period_i] = round(float(
                    group_h_i['wind_output'].sum()) * 10 * cof_energy, 1)  # 转为亿千瓦时,万千瓦时->亿千瓦时
                data_dict['wind']['curtailment'][period_i] = round(float(
                    group_h_i['wind_curtailment'].sum()) * 10 * cof_energy, 1)
            if 'solar_output' in group_h_i.columns.tolist():
                data_dict['solar']['electric'][period_i] = round(float(
                    group_h_i['solar_output'].sum()) * 10 * cof_energy, 1)  # 转为亿千瓦时
                data_dict['solar']['curtailment'][period_i] = round(float(
                    group_h_i['solar_curtailment'].sum()) * 10 * cof_energy, 1)

        if freq_h == 'hour':
            for period, group_h in _tm_df.groupby(_tm_df.index.hour):
                func_getdata_df(distribute_dict, period, group_h)
        else:
            for period_m, group_m in _tm_df.groupby(_tm_df.index.month):
                func_getdata_df(distribute_dict, period_m - 1, group_m)  # 月份是从1开始,需要-1
        return distribute_dict

    hours_distribute_dict = cal_electric_distribute(_tm_df, 'hour')  # 按小时分组--统计风光的电量和弃电;
    month_distribute_dict = cal_electric_distribute(_tm_df, 'month')  # 按月分组;

    # 处理小时分布统计
    hours_num = 24
    reserve_up_low_hours = [0] * hours_num  # 正备用不足小时数据
    load_cutailment_max = [0] * hours_num  # 最大供电缺口
    down_reserve_low_hours = [0] * hours_num  # 负备用不足小时数据
    peaking_gap_max = [0] * hours_num  # 最大调峰缺口
    powersupply_slack_electric = [0] * hours_num  # 累计供电缺口电量数据
    peak_gap_electric = [0] * hours_num  # 累计调峰弃电电量数据
    for period, group_h in _tm_df.groupby(_tm_df.index.hour):
        # 计算正备用裕度不足小时数; reserve_up < reserve_up_need + 1
        reserve_up_low_hours[period] = np.sum(
            group_h['load_curtailment'] > 0.1)
        load_cutailment_max[period] = round(group_h['load_curtailment'].max(), 1)
        powersupply_slack_electric[period] = round(group_h['load_curtailment'].sum() * 1e-4, 1)
        # np.sum(group_h['reserve_down'] > group_h['reserve_down_need'])
        down_reserve_low_hours[period] = np.sum(group_h['peaking_gap'] > 1e-2)
        peaking_gap_max[period] = round(group_h['peaking_gap'].max(), 1)
        peak_gap_electric[period] = round(group_h['peaking_gap'].sum() * 1e-4, 1)

    hour_distri = dict()
    hour_distri['up_reserve_low_hours'] = reserve_up_low_hours  # 正用备用不足小时数据--供电有缺口小时
    hour_distri['power_slack_max'] = load_cutailment_max  # 最大供电缺口
    hour_distri['power_slack_electric'] = powersupply_slack_electric  # 供电缺口累计电量 亿千瓦时
    hour_distri['down_reserve_low_hours'] = down_reserve_low_hours  # 负备用不足小时数据
    hour_distri['peaking_gap_max'] = peaking_gap_max  # 最大调峰缺口
    hour_distri['peak_gap_electric'] = peak_gap_electric  # 调峰引起新能源弃电电量

    # 处理月分布统计-供电缺口/调峰缺口
    mon_num = 12
    mon_balance_distri = dict()
    mon_reserve_up_low_hours = [0] * mon_num  # 正备用不足小时数据
    mon_load_cutailment_max = [0] * mon_num  # 最大供电缺口
    mon_down_reserve_low_hours = [0] * mon_num  # 负备用不足小时数据
    mon_peaking_gap_max = [0] * mon_num  # 最大调峰缺口
    mon_powersupply_slack_electric = [0] * mon_num  # 累计供电缺口电量数据
    mon_peak_gap_electric = [0] * mon_num  # 累计调峰弃电电量数据
    for period, group_mon in _tm_df.groupby(_tm_df.index.month):
        # 计算正备用裕度不足小时数; reserve_up < reserve_up_need + 1
        # int((group_mon['reserve_up'] < group_mon['reserve_up_need']).sum())
        mon_reserve_up_low_hours[period - 1] = np.sum(group_mon['load_curtailment'] > 0.1)
        mon_load_cutailment_max[period - 1] = round(group_mon['load_curtailment'].max(), 1)
        mon_powersupply_slack_electric[period - 1] = round(group_mon['load_curtailment'].sum() * 1e-4, 1)
        # np.sum(group_mon['reserve_down'] > group_mon['reserve_down_need'])
        mon_down_reserve_low_hours[period - 1] = np.sum(group_mon['peaking_gap'] > 0.1)
        mon_peaking_gap_max[period - 1] = round(group_mon['peaking_gap'].max(), 1)
        mon_peak_gap_electric[period - 1] = round(group_mon['peaking_gap'].sum() * 1e-4, 1)

    mon_balance_distri['up_reserve_low_hours'] = mon_reserve_up_low_hours  # 正用备用不足小时数据--供电有缺口小时
    mon_balance_distri['power_slack_max'] = mon_load_cutailment_max  # 最大供电缺口
    mon_balance_distri['power_slack_electric'] = mon_powersupply_slack_electric  # 供电缺口累计电量 亿千瓦时
    mon_balance_distri['down_reserve_low_hours'] = mon_down_reserve_low_hours  # 负备用不足小时数据
    mon_balance_distri['peaking_gap_max'] = mon_peaking_gap_max  # 最大调峰缺口
    mon_balance_distri['peak_gap_electric'] = mon_peak_gap_electric  # 调峰引起新能源弃电电量

    # 小时分布数据,日分布数据,月分布数据
    return hours_distribute_dict, hour_distri, daily_electric_data, month_distribute_dict, mon_balance_distri


def data_df_distribute_base(_tm_df, distri_type: str = "month", func_type: str = "num_count"):
    """
    统计分析结果数据的小时、月分布特性
    result_dict: teap计算结果;
    distri_type: "hours": 按小时分布统计; "month":按月度分布统计
    func_type: 统计分析的方法; "num_count","sum_count","max_count"
    Returns:
    """
    # 按日聚合;
    if distri_type == 'hour':
        groupby_key = _tm_df.index.hour
    elif distri_type == 'month':
        groupby_key = _tm_df.index.month
    else:
        raise ValueError("distri_type must be 'hours' or 'month'")

    distribute_dict = dict()
    if func_type == "num_count":
        # 统计>0的个数;
        count_result = _tm_df.groupby(groupby_key).apply(lambda x: (x > 0).sum())
        distribute_dict = count_result.to_dict(orient='list')
    elif func_type == "sum_count":
        # 统计和;
        sum_result = _tm_df.groupby(groupby_key).apply(lambda x: x.sum())
        distribute_dict = sum_result.to_dict(orient='list')
    elif func_type == "max_count":
        # 统计最大值;
        max_result = _tm_df.groupby(groupby_key).apply(lambda x: x.max())
        distribute_dict = max_result.to_dict(orient='list')
    return distribute_dict


def result_zone_distribute_cal(net, result_dict, config, zn_flag: str = 'city', area_details_dict: dict = dict(),
                               time_step: int = None):
    """
    计算所有地市的新能源和可再生能源消纳率和电量/利用小时/弃电时长，按wind/solar中的'city'进行分组计算
    net: 网络算例
    result_dict: teap结果: data['result_output']
    zn_flag: city or zone
    area_details_dict: 如果是zone,需要传入分区关联设备索引
    time_step: 如果是None,计算全年整体消纳率;如果是int,则计算时刻的新能源瞬时消纳率;
    """
    num_snaps = config['sum_snap']

    if zn_flag == 'city':
        if config['n_element']['wind'] > 0:
            wind_citys = [x for x in net['wind']['city'].unique() if x is not None]  # 去除None
        else:
            wind_citys = list()

        if config['n_element']['solar'] > 0:
            solar_citys = [x for x in net['solar']['city'].unique() if x is not None]
        else:
            solar_citys = list()
        citys = wind_citys + solar_citys  # 所有的存在的地市列表;
    else:
        citys = list(area_details_dict.keys())

    def Is_time_step(time_step_i: int = None):
        if time_step_i is None:
            return False
        else:
            return True

    def get_city_new_idx(net, city_i, zn_i_flag, i_type: str, area_details: dict):
        if i_type not in ['wind', 'solar']:
            raise ValueError(f"输入的get_city_new_idx函数的i_type参数{i_type}不支持")
        if config['n_element'][i_type] > 0:
            if city_i == 'all':
                _tm_wind_idx = net[i_type].index
                _tm_cen_wind_idx = net[i_type][net[i_type]['type'] == 'centralized'].index.tolist()  # 集中式索引
                _tm_dis_wind_idx = net[i_type][net[i_type]['type'] == 'distributed'].index.tolist()  # 分散式索引
            else:
                if zn_i_flag == 'city':
                    _tm_wind_idx = net[i_type][net[i_type]['city'] == city_i].index
                    _tm_cen_wind_idx = net[i_type][
                        (net[i_type]['type'] == 'centralized') & (
                                net[i_type]['city'] == city_i)].index.tolist()  # 集中式索引
                    _tm_dis_wind_idx = net[i_type][
                        (net[i_type]['type'] == 'distributed') & (
                                net[i_type]['city'] == city_i)].index.tolist()  # 分散式风电索引
                else:
                    _tm_wind_idx = area_details[city_i][i_type]
                    _tm_cen_wind_idx = net[i_type][
                        (net[i_type]['type'] == 'centralized')].index
                    _tm_dis_wind_idx = net[i_type][
                        (net[i_type]['type'] == 'distributed')].index
                    # 进一步筛选_tm_cen_wind_idx在_tm_wind_idx的元素
                    _tm_cen_wind_idx = _tm_cen_wind_idx[_tm_cen_wind_idx.isin(_tm_wind_idx)].values.tolist()
                    _tm_dis_wind_idx = _tm_dis_wind_idx[_tm_dis_wind_idx.isin(_tm_wind_idx)].values.tolist()
        else:
            _tm_wind_idx = list()
            _tm_cen_wind_idx = list()
            _tm_dis_wind_idx = list()
        return _tm_wind_idx, _tm_cen_wind_idx, _tm_dis_wind_idx  # 所有的.集中式.分布式 风/光

    index_dict = dict()  # all--表示全网
    curtailment_hours_dict = dict()  # 新能源弃电时长;
    for city in ['all'] + citys:
        # wind
        _tm_wind_idx, _tm_cen_wind_idx, _tm_dis_wind_idx = get_city_new_idx(net, city, zn_flag, 'wind',
                                                                            area_details=area_details_dict)

        def get_wind_proportion(_tm_w_idx: list, time_step_i: int = None):
            type_cutail_hours = 0  # 风电弃电时长;
            if len(_tm_w_idx) > 0:
                if not Is_time_step(time_step_i):
                    wind_output = result_dict['wind_output'][_tm_w_idx, :].sum()
                    wind_curtailment_arrs = result_dict['wind_curtailment'][_tm_w_idx, :].sum(axis=0)
                    wind_curtailment = wind_curtailment_arrs.sum()
                    # type_cutail_hours 等于 wind_curtailment_arrs>0.1的数目
                    type_cutail_hours = wind_curtailment_arrs[wind_curtailment_arrs > 0.1].shape[0]
                else:
                    wind_output = result_dict['wind_output'][_tm_w_idx, :].sum(axis=0)[time_step_i]
                    wind_curtailment = result_dict['wind_curtailment'][_tm_w_idx, :].sum(axis=0)[time_step_i]
                widn_total = wind_output + wind_curtailment
                type_proportion = round(wind_output / widn_total, 4) if widn_total > 0 else 0  # 消纳率
            else:
                wind_output = 0
                wind_curtailment = 0
                type_proportion = 0
            return wind_output, wind_curtailment, type_proportion, type_cutail_hours

        wind_output, wind_curtailment, wind_proportion, wind_cutail_hours = get_wind_proportion(_tm_wind_idx,
                                                                                                time_step)  # 风电
        index_dict[city] = dict()
        curtailment_hours_dict[city] = dict()
        index_dict[city]['wind_proportion'] = wind_proportion
        curtailment_hours_dict[city]['wind_curtail_hours'] = wind_cutail_hours
        # 统计分析统调/非统调的新能源消纳率;
        _, _, cen_wind_proportion, _ = get_wind_proportion(_tm_cen_wind_idx, time_step)  # 集中式风电
        index_dict[city]['centralized_wind_proportion'] = cen_wind_proportion
        _, _, dis_wind_proportion, _ = get_wind_proportion(_tm_dis_wind_idx, time_step)  # 分散式风电
        index_dict[city]['distributed_wind_proportion'] = dis_wind_proportion

        # solar
        _tm_solar_idx, _tm_cen_solar_idx, _tm_dis_solar_idx = get_city_new_idx(net, city, zn_flag, 'solar',
                                                                               area_details=area_details_dict)

        def get_solar_proportion(_tm_s_idx: list, time_step_i: int = None):
            type_cutail_hours = 0  # 弃电时长统计
            if len(_tm_s_idx) > 0:
                if not Is_time_step(time_step_i):
                    solar_output = result_dict['solar_output'][_tm_s_idx, :].sum()
                    solar_curtailment_arrs = result_dict['solar_curtailment'][_tm_s_idx, :].sum(axis=0)
                    solar_curtailment = solar_curtailment_arrs.sum()
                    type_cutail_hours = solar_curtailment_arrs[solar_curtailment_arrs > 0.1].shape[0]
                else:
                    solar_output = result_dict['solar_output'][_tm_s_idx, :].sum(axis=0)[time_step_i]
                    solar_curtailment = result_dict['solar_curtailment'][_tm_s_idx, :].sum(axis=0)[time_step_i]
                solar_total = solar_output + solar_curtailment
                s_proportion = round(solar_output / solar_total, 4) if solar_total > 0 else 0  # 消纳率
            else:
                solar_output = 0
                solar_curtailment = 0
                s_proportion = 0
            return solar_output, solar_curtailment, s_proportion, type_cutail_hours

        solar_output, solar_curtailment, solar_proportion, solar_cutail_hours = get_solar_proportion(_tm_solar_idx,
                                                                                                     time_step)  # 光伏
        index_dict[city]['solar_proportion'] = solar_proportion
        curtailment_hours_dict[city]['solar_curtail_hours'] = solar_cutail_hours
        # 统计分析统调/非统调的光伏消纳率;
        o1, c1, cen_solar_proportion, _ = get_solar_proportion(_tm_cen_solar_idx, time_step)  # 集中式光伏
        index_dict[city]['centralized_solar_proportion'] = cen_solar_proportion
        o2, c2, dis_solar_proportion, _ = get_solar_proportion(_tm_dis_solar_idx, time_step)  # 分布式光伏
        index_dict[city]['distributed_solar_proportion'] = dis_solar_proportion

        # 新能源消纳率
        newenergy_output = wind_output + solar_output
        newenergy_total = newenergy_output + (wind_curtailment + solar_curtailment)
        index_dict[city]['newenergy_proportion'] = round(newenergy_output / newenergy_total,
                                                         4) if newenergy_total > 0 else 0
        # 新能源总弃电时长:
        if 'wind_curtailment' in result_dict.keys():
            wind_curtailment_arrs = result_dict['wind_curtailment'][_tm_wind_idx, :].sum(axis=0)
        else:
            wind_curtailment_arrs = np.zeros(num_snaps)
        if 'solar_curtailment' in result_dict.keys():
            solar_curtailment_arrs = result_dict['solar_curtailment'][_tm_solar_idx, :].sum(axis=0)
        else:
            solar_curtailment_arrs = np.zeros(num_snaps)
        new_total_curtail = wind_curtailment_arrs + solar_curtailment_arrs
        curtailment_hours_dict[city]['newenergy_curtail_hours'] = new_total_curtail[new_total_curtail > 0.1].shape[0]

        # hydro hydropower + gen/hydro
        # 水电发电量
        if len(net.gen.loc[net.gen.type.isin(['hydro', 'hydropower']), :]) > 0:
            if city == 'all':
                gen_hydro_index = net.gen.loc[net.gen.type.isin(['hydro', 'hydropower']), :].index
            else:
                if zn_flag == 'city':
                    gen_hydro_index = net.gen.loc[(net.gen.type.isin(['hydro', 'hydropower']) & (net.gen.city == city)),
                                      :].index
                else:
                    gen_hydro_index = net.gen.loc[(net.gen.type.isin(['hydro', 'hydropower']) & (
                        net.gen.index.isin(area_details_dict[city]['gen']))), :].index
            gen_hydro_output = result_dict['gen_output'][gen_hydro_index, :].sum(axis=0)
            if not Is_time_step(time_step):
                gen_hydro_electric = gen_hydro_output.sum()  # 水电
            else:
                gen_hydro_electric = gen_hydro_output[time_step]
            # 计算弃水电量--有电量约束的电站的发电量和理论电量和的差值;
            gen_hydro_cutailment = 0.0
            # 找出水电中关联电量时序的机组索引,计算这些机组的总目标电量 - 总发电量,就是弃水电量
            gen_hydro_acc_idx = list()  # 有电量约束的水电的索引
            gen_hydro_timeseries_idx = list()  # 有电量约束的水电的对应的timeseries的索引
            for i_dx in gen_hydro_index:
                i_dx_series = net.gen.loc[i_dx, 'timeseries']
                if i_dx_series is None or pd.isna(i_dx_series) or i_dx_series == '':
                    continue
                else:
                    # 将中文'，'替换成','
                    i_dx_series = str(i_dx_series).replace('，', ',')
                    i_dx_series_list = str(i_dx_series).split(',')  # 字符串根据','分割
                    for i_series_no in i_dx_series_list:
                        # 如果 i_series_no对应的net.timeseries.loc[i_series_no,'type'] == 'gen_p_daily_accum' 是电量约束,将i_series_no加入gen_hydro_acc_idx，break
                        if net.timeseries.loc[int(i_series_no), 'type'] == 'gen_p_daily_accum':
                            gen_hydro_acc_idx.append(i_dx)
                            gen_hydro_timeseries_idx.append(int(i_series_no))
                            break
            if len(gen_hydro_acc_idx) > 0:
                # 从result_dict['gen']中取出gen_hydro_acc_idx行的所有列,并按行列求总和--有电量约束的电站的发电量
                # 从net['gen']表中取出en_hydro_acc_idx行的'max_p_mw' ,并从net.timeseries中获取gen_hydro_timeseries_idx的行的t{1}_pu到t{num_snaps}_pu的列
                gen_hydro_max_p_mw = net.gen.loc[gen_hydro_acc_idx, 'max_p_mw'].values
                gen_hydro_max_p_mw_timeseries = net.timeseries.loc[gen_hydro_timeseries_idx,
                                                't1_pu':'t' + str(num_snaps) + '_pu'].values
                if not Is_time_step(time_step):
                    gen_hydro_cutailment = (gen_hydro_max_p_mw_timeseries * (gen_hydro_max_p_mw[:, np.newaxis]) -
                                            result_dict['gen_output'][gen_hydro_acc_idx, :]).sum(axis=0).sum()
                else:
                    gen_hydro_cutailment = (gen_hydro_max_p_mw_timeseries * (gen_hydro_max_p_mw[:, np.newaxis]) -
                                            result_dict['gen_output'][gen_hydro_acc_idx, :]).sum(axis=0)[time_step]
        else:
            gen_hydro_electric = 0.0
            gen_hydro_cutailment = 0.0
        # hydropower
        if config['n_element']['hydropower'] > 0:
            if city == 'all' or 'city' not in net['hydropower'].columns:
                hydropower_output = result_dict['hydropower_output'].sum(axis=0)
            else:
                if zn_flag == 'city':
                    _tm_idx = net['hydropower'][net['hydropower']['city'] == city].index
                else:
                    _tm_idx = area_details_dict[city]['hydropower']
                hydropower_output = result_dict['hydropower_output'][_tm_idx, :].sum(axis=0)
            if not Is_time_step(time_step):
                hydropower_electric = hydropower_output.sum()  # 库容式水电-发电量;
            else:
                hydropower_electric = hydropower_output[time_step]
            # 计算有电量约束的水电的弃水电量,(2部分,hydropower + gen_hydro部分);--每个机组可能并不一样;
            hydropower_cutail_volume = result_dict['out_volume'] - result_dict['hydropower_volume']  # shape: m*8760
            hydro_cost_coef = np.array(net.hydropower["water_cost_m3_per_second_per_mw"].values)
            if not Is_time_step(time_step):
                hydropower_cutailment = np.multiply(hydropower_cutail_volume, hydro_cost_coef).sum()
            else:
                hydropower_cutailment = np.multiply(hydropower_cutail_volume[:, time_step], hydro_cost_coef).sum()
        else:
            hydropower_electric = 0.0
            hydropower_cutailment = 0.0  # 库容式水电弃水电量

        # 计算水电消纳率
        hydro_output = hydropower_electric + gen_hydro_electric  # 所有水电的电量
        hydro_curtailment = gen_hydro_cutailment + hydropower_cutailment  # 所有水电的弃水电量--按日统计;
        hydro_total = hydro_output + hydro_curtailment
        index_dict[city]['hydro_proportion'] = hydro_output / hydro_total if hydro_total > 0 else 0

        # 可再生能源
        renewable_output = newenergy_output + hydro_output
        renewable_total = newenergy_total + hydro_output + hydro_curtailment
        index_dict[city]['renewable_proportion'] = renewable_output / renewable_total if renewable_total > 0 else 0

        # 保留4位小数据
        for key in index_dict[city].keys():
            index_dict[city][key] = round(index_dict[city][key], 4)

    return index_dict, curtailment_hours_dict


def get_topo_device_rate_period(topo_info: dict, config: dict, result_dict: dict, period: str = 'week',
                                pf_type: str = 'mean'):
    """
    计算分区/全网拓扑的设备的日、周、月平均负载率分布;
    net: 网络拓扑;
    result_dict: teap的计算结果
    period: 统计分析周期;
    pf_type: 设备负载率类型: 'mean' or 'max'
    """
    if period not in ['week', 'month']:  # 'hour', 'day',
        raise ValueError("获取设备平衡负载率时间分布的period参数错误!")

    period_flag = 'W' if period == 'week' else 'M'

    # get_topo:
    topo_data_dict = {
        'point_data': dict(),
        'line_data': dict()
    }
    # 电网拓扑
    # if 'topo_info' not in case_info_dict:
    #     raise ValueError("算例信息分析结果中没有电网拓扑数据")

    bus_data_list = topo_info['point_data']
    line_data_list = topo_info['line_data']

    # 获取时间范围
    sum_snap = config['sum_snap']
    _tm_df = get_time_range(config['start_time'], sum_snap)

    if 'line_power' in result_dict:
        line_power = result_dict['line_power']
    else:
        line_power = result_dict['line_power']

    if 'trafo_power' in result_dict:
        trafo_power = result_dict['trafo_power']
        for i_bus in bus_data_list:
            if len(i_bus['related_trafo_idx']) == 0:
                continue
            # 计算更新功率;i_point['value'][2]的值-单值; i_point['detail']
            pf_list = trafo_power[i_bus['related_trafo_idx']] / np.array(i_bus['limit'])[:, np.newaxis]

            # 进行按周处理;
            _tm_df['point_value'] = np.max(pf_list, axis=0)
            # 按period分组
            _tm_df_period_group = _tm_df.resample(period_flag).mean()
            # save
            key_i_bus = i_bus['name']
            topo_data_dict['point_data'][key_i_bus] = _tm_df_period_group['point_value'].round(
                4).tolist()  # 平均/最大负载率的周/月统计
    else:
        _tm_df_period_group = _tm_df.resample(period_flag).mean()
        for i_bus in bus_data_list:
            topo_data_dict['point_data'][i_bus['name']] = np.zeros(len(_tm_df_period_group))

    if 'line_power' in result_dict:
        for row in line_data_list:
            # 更新线路潮流;i_line['value']
            _line_flow = np.sum(line_power[row['related_line_idx']['pos']], axis=0) - np.sum(
                line_power[row['related_line_idx']['neg']], axis=0)
            row_value = (np.abs(_line_flow) / row['limit']).round(4)

            # 进行按周处理;
            _tm_df['line_value'] = row_value
            # 按period分组
            _tm_df_period_group = _tm_df.resample(period_flag).mean()

            # key_i_line = (row['fromName'], row['toName'])
            key_i_line = f"{row['fromName']}_{row['toName']}"
            topo_data_dict['line_data'][key_i_line] = _tm_df_period_group['line_value'].round(4).tolist()
    else:
        _tm_df_period_group = _tm_df.resample(period_flag).mean()
        for row in line_data_list:
            topo_data_dict['point_data'][row['name']] = np.zeros(len(_tm_df_period_group))
    return topo_data_dict


def result_zone_newerg_consump_rate(result_dict, area_details_dict: dict = dict(), time_step: int = None):
    """
    计算所有地市的新能源和可再生能源消纳率和电量/利用小时，按wind/solar中的'city'进行分组计算
    result_dict: teap结果: data['result_output']
    zn_flag: city or zone
    area_details_dict: 如果是zone,需要传入分区关联设备索引
    time_step: 如果是None,计算全年整体消纳率;如果是int,则计算时刻的新能源瞬时消纳率;
    """
    citys = list(area_details_dict.keys())

    def Is_time_step(time_step_i: int = None):
        if time_step_i is None:
            return False
        else:
            return True

    index_dict = dict()  # all--表示全网
    for city in ['all'] + citys:
        if 'wind_output' in result_dict:
            if city == 'all':
                _tm_wind_idx = slice(None)
            else:
                _tm_wind_idx = area_details_dict[city]['wind']
        else:
            _tm_wind_idx = list()

        index_dict[city] = dict()

        if 'wind_output' in result_dict:
            if not Is_time_step(time_step):
                wind_output = result_dict['wind_output'][_tm_wind_idx, :].sum()
                wind_curtailment = result_dict['wind_curtailment'][_tm_wind_idx, :].sum()
            else:
                wind_output = result_dict['wind_output'][_tm_wind_idx, :].sum(axis=0)[time_step]
                wind_curtailment = result_dict['wind_curtailment'][_tm_wind_idx, :].sum(axis=0)[time_step]
            widn_total = wind_output + wind_curtailment
            index_dict[city]['wind_proportion'] = round(wind_output / widn_total, 4) if widn_total > 0 else 1  # 风电消纳率
        else:
            wind_output = 0
            wind_curtailment = 0
            index_dict[city]['wind_proportion'] = 1
        # solar
        if 'solar_output' in result_dict:
            if city == 'all':
                _tm_solar_idx = slice(None)
            else:
                _tm_solar_idx = area_details_dict[city]['solar']
        else:
            _tm_solar_idx = list()
        if 'solar_output' in result_dict:
            if not Is_time_step(time_step):
                solar_output = result_dict['solar_output'][_tm_solar_idx, :].sum()
                solar_curtailment = result_dict['solar_curtailment'][_tm_solar_idx, :].sum()
            else:
                solar_output = result_dict['solar_output'][_tm_solar_idx, :].sum(axis=0)[time_step]
                solar_curtailment = result_dict['solar_curtailment'][_tm_solar_idx, :].sum(axis=0)[time_step]
            solar_total = solar_output + solar_curtailment
            index_dict[city]['solar_proportion'] = round(solar_output / solar_total, 4) if solar_total > 0 else 1  # 光伏
        else:
            solar_output = 0
            solar_curtailment = 0
            index_dict[city]['solar_proportion'] = 1

        # 新能源消纳率
        newenergy_output = wind_output + solar_output
        newenergy_total = newenergy_output + (wind_curtailment + solar_curtailment)
        index_dict[city]['newenergy_proportion'] = round(newenergy_output / newenergy_total,
                                                         4) if newenergy_total > 0 else 1
        # 保留4位小数据
        for key in index_dict[city].keys():
            index_dict[city][key] = round(index_dict[city][key], 4)

    return index_dict


def get_grid_psm_distribute_info(config: dict, peaking_gap_arr, power_supply_slack_arr):
    """
    分析全网的供电裕度不足、调峰裕度不足的年/日，月/日/小时分布
    net:
    cofig:
    result_dict:
    return:
    month_day_flag:{"1":{"peakSlackFlag":[],"peakSlackVal":[],"powerSupplyFlag":[],"powerSupplyVal":[]}}   # key: 1~12,value:月天数
    day_hours:{"1":{"1":{"peakSlackFlag":[],"peakSlackVal":[],"powerSupplyFlag":[],"powerSupplyVal":[]}}}  # key1: 1~12,key2: 1~31,value:24小时，0-23
    """
    # # 计算调峰缺口;
    # peaking_gap_arr = cal_sys_peaking_val(net, config, result_dict)
    #
    # # 计算供电缺口
    # power_supply_slack_arr = cal_sys_loadcutailment_val(config, result_dict)

    # 获取时间范围
    time_start = config['start_time']
    n_snapshots = config['sum_snap']
    _tm_df = get_time_range(time_start, n_snapshots)

    # 生成index索引为时间的df
    _tm_df['peaking_gap'] = peaking_gap_arr
    _tm_df['power_supply_slack'] = power_supply_slack_arr  # >0,缺电;

    # 计算年/月/日/时分布
    _tm_df['month'] = _tm_df.index.month
    _tm_df['day'] = _tm_df.index.day
    _tm_df['hour'] = _tm_df.index.hour

    # 按月、日聚合(用resample不用group)生成新的_tm_monthday_df，相同月/peaking_gap\power_supply_slack的最大值
    _tm_months = _tm_df['month'].unique().tolist()
    _tm_daily_max_df = _tm_df.resample('D').max()
    _tm_daily_max_df['peaking_gap_flag'] = _tm_daily_max_df['peaking_gap'].apply(lambda x: 1 if x > 0.1 else 0)
    # _tm_daily_max_df['power_supply_slack_flag'] = _tm_daily_max_df['power_supply_slack'].apply(
    #     lambda x: 1 if x > 0.1 else 0)
    _tm_daily_max_df['power_supply_slack_flag'] = np.where(_tm_daily_max_df['power_supply_slack'] > 0, 2, 0)
    sys_month_margin_dict = dict()
    for i_m in _tm_months:
        mon_i_margin = dict()
        mon_i_margin['peakSlackFlag'] = _tm_daily_max_df[_tm_daily_max_df['month'] == i_m]['peaking_gap_flag'].astype(
            int).tolist()
        mon_i_margin['peakSlackVal'] = _tm_daily_max_df[_tm_daily_max_df['month'] == i_m]['peaking_gap'].tolist()
        mon_i_margin['powerSupplyFlag'] = _tm_daily_max_df[_tm_daily_max_df['month'] == i_m][
            'power_supply_slack_flag'].astype(int).tolist()
        mon_i_margin['powerSupplyVal'] = _tm_daily_max_df[_tm_daily_max_df['month'] == i_m][
            'power_supply_slack'].tolist()
        sys_month_margin_dict[i_m] = mon_i_margin
    # 补充其他没有数据的月份;
    for no_i in range(1, 13):
        if no_i not in sys_month_margin_dict.keys():
            sys_month_margin_dict[no_i] = dict()
            sys_month_margin_dict[no_i]['peakSlackFlag'] = []
            sys_month_margin_dict[no_i]['peakSlackVal'] = []
            sys_month_margin_dict[no_i]['powerSupplyFlag'] = []
            sys_month_margin_dict[no_i]['powerSupplyVal'] = []

    # 处理小时级的数据;
    # 如果peaking_gap>0.1,'peaking_gap_flag'=1
    _tm_df['peaking_gap_flag'] = _tm_df['peaking_gap'].apply(lambda x: 1 if x > 0.1 else 0)
    # _tm_df['power_supply_slack_flag'] = _tm_df['power_supply_slack'].apply(lambda x: 1 if x > 0.1 else 0)
    _tm_df['power_supply_slack_flag'] = np.where(_tm_df['power_supply_slack'] > 0, 2, 0)
    sys_month_day_margin_dict = dict()
    # 将_tm_df 按 mont,day进行group分组
    _tm_grp_mon_df = _tm_df.groupby(['month'])
    # 遍历_tm_grp_mon_df
    for i_m in _tm_grp_mon_df.groups.keys():
        # 对grp_i进行按'day'分组
        grp_month = _tm_grp_mon_df.get_group(i_m)
        _tm_grp_day_df = grp_month.groupby(['day'])

        mon_i_days_margin = dict()
        for i_d in _tm_grp_day_df.groups.keys():
            grp_day_df = _tm_grp_day_df.get_group(i_d)
            mon_i_day_hours_margin = dict()
            mon_i_day_hours_margin['timeIndex'] = grp_day_df['data'].values.astype(int).tolist()
            mon_i_day_hours_margin['peakSlackFlag'] = grp_day_df['peaking_gap_flag'].values.astype(int).tolist()
            mon_i_day_hours_margin['peakSlackFlag'] = grp_day_df['peaking_gap_flag'].values.astype(int).tolist()
            mon_i_day_hours_margin['peakSlackVal'] = grp_day_df['peaking_gap'].values.tolist()
            mon_i_day_hours_margin['powerSupplyFlag'] = grp_day_df['power_supply_slack_flag'].values.astype(
                int).tolist()
            mon_i_day_hours_margin['powerSupplyVal'] = grp_day_df['power_supply_slack'].values.tolist()
            mon_i_days_margin[i_d] = mon_i_day_hours_margin
        sys_month_day_margin_dict[i_m] = mon_i_days_margin
    return sys_month_margin_dict, sys_month_day_margin_dict
