import pandas as pd
import numpy as np
from datetime import datetime
from alg.conf.config_extern import (config_exter_dict, dc_feedin_dict, scene_config_dict)

# 一年四季的月份;
spring_month = [3, 4, 5]
summer_month = [6, 7, 8]
autumn_month = [9, 10, 11]
winter_month = [1, 2, 12]
daytime_hours = [11, 12, 13, 14]  # 白天光伏应该大发时刻;

cof_energy = 0.00001  # MWh-> 亿千瓦时
cof_power = 0.1  # MW-> 万千瓦

GLOBAL_QW_NAME = '全省'

gen_type_disname_dict = {
    "gen": "常规",
    "wind": "风电",
    "solar": "光伏",
    "coal": "煤电",
    "gas": "燃气",
    "biomass": "生物质",
    "csp": "光热",
    "hydro": "水电",
    "hydropower": "库容水电",
    "nuclear": "核电",
    "stogen": "储能",
    "pump": "抽蓄",
    "battery": "电化学储能",
    "feedin": "区外来电",
    # "centralized_solar": "集中式光伏",
    # "distributed_solar": "分布式光伏",
    # "centralized_wind": "集中式风电",
    # "distributed_wind": "分散式风电",
}

result_menu_name_dict = {
    "power_output": "功率曲线",
    "on_off_state": "启停状态",
    "gen_state": "机组启停状态",
    "gen_output": "常规机组出力",
    "wind_output": "风电出力",
    "wind_curtailment": "弃风功率",
    "solar_output": "光伏出力",
    "solar_curtailment": "弃光功率",
    "feedin_output": "区外来电",
    "hydropower_output": "水电出力",
    "load_output": "负荷功率",
    "load_curtailment": "弃负荷功率",
    "stogen_output": "储能/抽蓄出力",
    "storage_energy": "储能/抽蓄容量",
}

_output_dict = {
    'on_off_cost': 'gen',
    'oper_hour': 'gen',
    'gen_state': 'gen',
    'gen_power_cost': 'gen',
    'gen_output': 'gen',
    'wind_output': 'wind',
    'wind_curtailment': 'wind',
    'solar_output': 'solar',
    'solar_curtailment': 'solar',
    'feedin_output': 'feedin',
    'stogen_output': 'stogen',
    'storage_energy': 'storage',
    'hydropower_output': 'hydropower',
    'load': 'load',
    'load_curtailment': 'load',
    'line_power': 'line',
    'trafo_power': 'trafo',
    "dcline_power_from": "dcline",
    "dcline_power_to": "dcline",
}


def get_time_range(time_start, num_snaps, freq='h', snaps_col: str = "data"):
    """
    以freq为间隔，从time_start开始，创建时间索引;
    Args:
        time_start:
        num_snaps:
    Returns:
        df
    """
    _df = pd.DataFrame({snaps_col: range(num_snaps)})
    # 创建周期索引
    _periods = pd.period_range(start=pd.to_datetime(time_start), periods=len(_df), freq=freq)
    _df.index = _periods  # 将周期索引设置为DataFrame的索引
    # _df['month'] = pd.to_datetime(_df.index).month
    return _df


def get_ana_config_info(case_address: str = ''):
    """
    根据传入的case_address 读取config_extern中的相关配置参数
    """
    config = dict()
    if case_address in scene_config_dict.keys():
        config = scene_config_dict[case_address]
    else:
        config['config_exter_dict'] = config_exter_dict
        config['dc_feedin_dict'] = dc_feedin_dict
    return config


def get_time_step_hours(time1_str: str, time2_str: str):
    """
    计算两个时间的小时差;如果time2早于time1，则返回False,-1
    """
    # 输入的datetime字符串
    # time1_str = "2020-01-01 00:00:00"
    # time2_str = "2020-01-02 12:00:00"

    # 将字符串转换为datetime对象
    time1 = datetime.strptime(time1_str, "%Y-%m-%d %H:%M:%S")
    time2 = datetime.strptime(time2_str, "%Y-%m-%d %H:%M:%S")

    if time1 > time2:
        return False, -1

    # 计算时间差，这将返回一个timedelta对象
    time_difference = time2 - time1

    # timedelta对象有一个total_seconds()方法，但我们可以直接计算小时数
    hours_difference = time_difference.total_seconds() // 3600  # 使用整除来获取小时数
    # print(f"time1与time2相隔的小时数为: {hours_difference}")
    return True, int(hours_difference)


def get_list1_ele_in_list2(list1: list, list2: list):
    """
    返回list1中的元素在list2中的子list;
    """
    # 将列表转换为集合
    set1 = set(list1)
    set2 = set(list2)

    # 使用集合的交集操作
    common_elements = list(set1.intersection(set2))
    return common_elements


def process_row_str(row, type_dict, row_key: str, key: str):
    """
    根据可分割的字符串,分割后的字字符串在字典中查询对应的需要的值类型,如果满足条件返回，否则返回-1
    :param row:
    :param type_dict:
    :param key:
    :return:
    """
    values = row[row_key].split(',')
    for value in values:
        if value == '':
            continue
        val_int = int(float(value))
        if val_int in type_dict and type_dict[val_int] == key:
            return val_int  # 返回第一个满足条件的 key
    return -1


def mean_greater_than_one(row, over_mk: float = 1.0):
    """
    # 定义一个函数来计算每行中大于 1 的数据的平均值
    :param row:
    :return:
    """
    filtered_values = row[row >= over_mk]
    if filtered_values.size > 0:
        return np.mean(filtered_values)
    else:
        return 0.0  # 如果没有大于 1 的值，返回 0
