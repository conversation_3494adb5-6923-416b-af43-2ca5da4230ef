"""
将teap的结果存入.h5文件,支持以便于快速读取;
dataset:
feedin_output、gen_output、gen_state、interface_power、line_power、load、load_curtailment、solar_curtailment
solar_output、stogen_output、trafo_power、wind_curtailment、wind_output
"""
import os
import h5py
import numpy as np





def save_equipment_ts_in_h5(d_types_seq=None,
                            d_meta_seq=None,
                            d_rate_seq=None,
                            d_power_seq=None,
                            file_name=None):
    """
    线路/主变/断面潮流数据写入.h5文件;
    group: power_data,rate_data,metadata
    dataset:['line', 'trafo', 'interface']
    """

    h5f = h5py.File(file_name, "w")
    # h5f.create_dataset('time', data=d_time)
    for type_ind, type_name in enumerate(d_types_seq):
        h5f.require_group('power_data').create_dataset(type_name,
                                                       data=d_power_seq[type_ind])
        h5f.require_group('rate_data').create_dataset(type_name,
                                                      data=d_rate_seq[type_ind])

        for col_name in d_meta_seq[type_ind].columns:
            # 某一列如果既有字符串又有missiong value，会数据类型会被标记为'object', h5py不会保存这类数据
            if d_meta_seq[type_ind][col_name].values.dtype.name == 'object':
                v = d_meta_seq[type_ind][col_name].fillna('').astype(str).values
            else:
                v = d_meta_seq[type_ind][col_name].values

            h5f.require_group('metadata/' + type_name).create_dataset(
                col_name,
                data=v)

    h5f.close()

def save_line_trafo_data(net, config, result_df_dict):
    """
    梳理整合设备数据保存到h5文件中
    """
    num_of_snapshots = config['sum_snap']
    result_file_path = config['case_rlt_dir']   # .h5目标文件的路径;
    cae_id = config['case_id']

    time_columns = [_time for _time in range(num_of_snapshots)]
    line_power_data = None
    trafo_power_data = None
    interface_power_data = None
    line_power_rate = None
    trafo_power_rate = None
    interface_power_rate = None
    if 'line_power' in result_df_dict.keys():
        line_max = np.absolute(result_df_dict['line_power'].loc[:, 'Pbr_max'].values.reshape(-1, 1))
        line_max[line_max < 0.0001] = 0.0001
        line_power_data = result_df_dict['line_power'].loc[:, time_columns].values
        line_power_data_abs = np.absolute(result_df_dict['line_power'].loc[:, time_columns].values)
        line_power_rate = line_power_data_abs / line_max
        # line_overloading = (line_power_rate - 1).clip(min=0)
    if 'trafo_power' in result_df_dict.keys():
        trafo_max = np.absolute(result_df_dict['trafo_power'].loc[:, 'Pbr_max'].values.reshape(-1, 1))
        trafo_max[trafo_max < 0.0001] = 0.0001
        trafo_power_data = result_df_dict['trafo_power'].loc[:, time_columns].values
        trafo_power_data_abs = np.absolute(result_df_dict['trafo_power'].loc[:, time_columns].values)
        trafo_power_rate = trafo_power_data_abs / trafo_max
        # trafo_overloading = (trafo_power_rate - 1).clip(min=0)
    if 'interface_power' in result_df_dict.keys():
        interface_max = np.absolute(result_df_dict['interface_power'].loc[:, 'max_p_mw'].values.reshape(-1, 1))
        interface_max[interface_max < 0.0001] = 0.0001
        interface_power_data = result_df_dict['interface_power'].loc[:, time_columns].values
        interface_power_data_abs = np.absolute(result_df_dict['interface_power'].loc[:, time_columns].values)
        interface_power_rate = interface_power_data_abs / interface_max
        # interface_overloading = (interface_power_rate - 1).clip(min=0)

    ## 线路元数据
    line_meta = net.line.merge(net.bus.loc[:, ['name', 'vn_kv']], left_on='from_bus', right_index=True)
    line_meta = line_meta.rename(columns={'name_x': 'line_name',
                                          'name_y': 'from_bus_name',
                                          'vn_kv': 'from_bus_vn_kv'})
    line_meta = line_meta.merge(net.bus.loc[:, ['name', 'vn_kv']], left_on='to_bus', right_index=True)
    line_meta = line_meta.rename(columns={'name': 'to_bus_name',
                                          'vn_kv': 'to_bus_vn_kv'})
    line_meta = line_meta.sort_index(axis=0)

    ## 变压器元数据
    trafo_meta = net.trafo.merge(net.bus.loc[:, ['name', 'vn_kv']], left_on='hv_bus', right_index=True)
    trafo_meta = trafo_meta.rename(columns={'name_x': 'trafo_name',
                                            'name_y': 'hv_bus_name'})
    trafo_meta = trafo_meta.merge(net.bus.loc[:, ['name', 'vn_kv']], left_on='lv_bus', right_index=True)
    trafo_meta = trafo_meta.rename(columns={'name': 'lv_bus_name'})
    trafo_meta = trafo_meta.sort_index(axis=0)

    ## 断面元数据
    interface_meta = net.interface

    ## 装入h5文件
    equipment_types = ['line', 'trafo', 'interface']
    file_name = 'equipment_data_' + cae_id + '.h5'
    path_file_name = os.path.join(result_file_path,
                                  file_name)
    save_equipment_ts_in_h5(d_types_seq=equipment_types,
                            d_meta_seq=[line_meta, trafo_meta, interface_meta],
                            d_rate_seq=[line_power_rate, trafo_power_rate, interface_power_rate],
                            d_power_seq=[line_power_data, trafo_power_data, interface_power_data],
                            file_name=path_file_name)
    return file_name