"""
执行保供相关数据指标的计算功能
"""
from pandapower import pandapowerNet
from alg.cal_ssmetrics.case_result_statis import CCaseResultAna
from alg.cal_ssmetrics.supply_index_cal import (get_typical_operations,
                                                indicator_metrics)


class CIndicatorAna(CCaseResultAna):
    """算例的相关指标统计;
    return:
    json: 'key': 'value', key:case_name
    """

    def __init__(self, net: pandapowerNet, result_dict: dict, config: dict = dict()):
        super().__init__(net, result_dict, config)

        self.indicator_rlt_dict = dict()  # 指标分析计算结果;

    def start_identify_alerts(self, net, config, res, area_details_dict: dict = dict(), ptdf=None,
                              tofile: bool = False):
        """
        执行算例结果指标分析统计功能; 需要保存的数据：
        1) 指标计算结果数据;
        2) 指标计算所需的数据;
        3) 分区的出力/负荷等边界数据;
        Args:
            net:
            config:
            res:
            area_details_dict:
            tofile:

        Returns:

        """
        # logger
        print("开始进行算例指标计算处理分析")

        # 识别典型工况时点; + 特殊工况(夏季无风,冬季无光等)
        self.indicator_rlt_dict['typical_mod'], self.indicator_rlt_dict['special_mod'] \
            = get_typical_operations(config, res)

        # 计算全网指标
        # self.indicator_rlt_dict['indicator_data'], self.indicator_rlt_dict['indicator_base_data'], \
        #     self.indicator_rlt_dict['indicator_thresholds'] = identify_alerts(net, config,
        #                                                                       res,
        #                                                                       None,
        #                                                                       area_details_dict)

        # 计算全网/分区的指标-指标计算结果数据、指标计算所需的基础数据、指标分级的门槛数据、原始边界数据--都是8760的数据; area_types: 分区类型划分指标计算
        self.indicator_rlt_dict['indicator_data'], self.indicator_rlt_dict['indicator_base_data'], \
            self.indicator_rlt_dict['indicator_thresholds'], self.indicator_rlt_dict[
            'boundary_data'], self.indicator_rlt_dict['area_types'] = indicator_metrics(net, config, res,
                                                                                        area_details_dict, ptdf)

        # 缓存成文件
        if tofile:
            self.caseinfo_tojson(self.indicator_rlt_dict, tofile, key_wd='indicators_cal')
