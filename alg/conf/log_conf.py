import logging.config
import logging
import os
import platform

from alg.ssmetrics_config import log_dir, solver_debug

try:
    import codecs
except ImportError:
    codecs = None

DEBUG = False

if DEBUG or platform.system() == "Darwin":
    level = "DEBUG"
else:
    level = "INFO"


class FullLogFilter(logging.Filter):

    def filter(self, record):
        if record.name in (
            'tornado.access', 'tornado.application', 'tornado.general',
            'peewee',
            'flask_apscheduler',
            'apscheduler', 'apscheduler.scheduler', 'apscheduler.jobstores', 'apscheduler.executors',
            'manager',
            'debug_mode',
        ):
            return False
        elif record.funcName == 'after_request' and record.msg.startswith('code [1'):
            return False
        return True


logging.config.dictConfig({
    'version': 1,
    'disable_existing_loggers': False,
    'filters': {
        'full_log_filter': {
            '()': FullLogFilter,
        }
    },
    'formatters': {
        'default': {
            'format': '[%(asctime)s.%(msecs)03d]> %(message)s',
            'datefmt': '%Y-%m-%d %H:%M:%S'
        },
        'standard': {
            'format': '[%(levelname)s][%(asctime)s][%(filename)s][%(funcName)s][%(lineno)d]> %(message)s',
            'datefmt': '%Y-%m-%d %H:%M:%S'
        },
        'full': {
            'format': '[%(levelname)s][%(asctime)s][%(filename)s][%(funcName)s][%(lineno)d][%(process)d][%(thread)d]> '
                      '%(message)s',
            'datefmt': '%Y-%m-%d %H:%M:%S'
        },
        'full_pid': {
            'format': '[%(levelname)s][%(asctime)s][%(process)d][%(thread)d]> %(message)s',
            'datefmt': '%Y-%m-%d %H:%M:%S'
        },
        'simple': {
            'format': '[%(levelname)s]> %(message)s',
            'datefmt': '%Y-%m-%d %H:%M:%S'
        },
    },
    'handlers': {
        'console': {
            'level': 'DEBUG',
            'class': 'logging.StreamHandler',
            'formatter': 'default'
        },
        'default_log_handler': {
            'level': level,
            'class': 'concurrent_log_handler.ConcurrentRotatingFileHandler',
            'filename': os.path.join(log_dir, 'teap'),
            'formatter': 'standard',
            'encoding': 'utf-8',
            'maxBytes': 1024 * 1024 * 10,
            'backupCount': 1
        },
        # 前端页面上的日志
        'calculation_log_handler': {
            'level': 'DEBUG',
            'class': 'concurrent_log_handler.ConcurrentRotatingFileHandler',
            'filename': os.path.join(log_dir, 'calculation_frontend'),
            'formatter': 'simple',
            'encoding': 'utf-8',
            'maxBytes': 1024 * 1024 * 10,
            'backupCount': 1
        },
        # 后台计算日志
        'calculation_backend_log_handler': {
            'level': 'DEBUG',
            'class': 'concurrent_log_handler.ConcurrentRotatingFileHandler',
            'filename': os.path.join(log_dir, 'calculation_backend'),
            'formatter': 'full',
            'encoding': 'utf-8',
            'maxBytes': 1024 * 1024 * 10,
            'backupCount': 1
        },
        'task_listener_log_handler': {
            'level': level,
            'class': 'concurrent_log_handler.ConcurrentRotatingFileHandler',
            'filename': os.path.join(log_dir, 'task_listener'),
            'formatter': 'default',
            'encoding': 'utf-8',
            'maxBytes': 1024 * 1024 * 10,
            'backupCount': 1
        },
        'api_log_handler': {
            'level': level,
            'class': 'concurrent_log_handler.ConcurrentRotatingFileHandler',
            'filename': os.path.join(log_dir, 'api_log'),
            'formatter': 'standard',
            'encoding': 'utf-8',
            'maxBytes': 1024 * 1024 * 10,
            'backupCount': 1
        },
        'web_log_handler': {
            'level': "DEBUG",
            'class': 'concurrent_log_handler.ConcurrentRotatingFileHandler',
            'filename': os.path.join(log_dir, 'web_log'),
            'formatter': 'standard',
            'encoding': 'utf-8',
            'maxBytes': 1024 * 1024 * 10,
            'backupCount': 1
        },
        'db_log_handler': {
            'level': "DEBUG",
            'class': 'concurrent_log_handler.ConcurrentRotatingFileHandler',
            'filename': os.path.join(log_dir, 'db_log'),
            'formatter': 'standard',
            'encoding': 'utf-8',
            'maxBytes': 1024 * 1024 * 10,
            'backupCount': 1
        },
        'scheduler_log_handler': {
            'level': "DEBUG",
            'class': 'concurrent_log_handler.ConcurrentRotatingFileHandler',
            'filename': os.path.join(log_dir, 'scheduler_log'),
            'formatter': 'standard',
            'encoding': 'utf-8',
            'maxBytes': 1024 * 1024 * 10,
            'backupCount': 1
        },
        'deletion_log_handler': {
            'level': level,
            'class': 'concurrent_log_handler.ConcurrentRotatingFileHandler',
            'filename': os.path.join(log_dir, 'deletion'),
            'formatter': 'standard',
            'encoding': 'utf-8',
            'maxBytes': 1024 * 1024 * 10,
            'backupCount': 1
        },
        'load_case_log_handler': {
            'level': level,
            'class': 'concurrent_log_handler.ConcurrentRotatingFileHandler',
            'filename': os.path.join(log_dir, 'load_case'),
            'formatter': 'full',
            'encoding': 'utf-8',
            'maxBytes': 1024 * 1024 * 10,
            'backupCount': 1
        },
        'listener_handler': {
            'level': level,
            'class': 'concurrent_log_handler.ConcurrentRotatingFileHandler',
            'filename': os.path.join(log_dir, 'listener'),
            'formatter': 'default',
            'encoding': 'utf-8',
            'maxBytes': 1024 * 1024 * 10,
            'backupCount': 1
        },
        'manager_handler': {
            'level': level,
            'class': 'concurrent_log_handler.ConcurrentRotatingFileHandler',
            'filename': os.path.join(log_dir, 'manager'),
            'formatter': 'default',
            'encoding': 'utf-8',
            'maxBytes': 1024 * 1024 * 10,
            'backupCount': 1
        },
        'context_handler': {
            'level': level,
            'class': 'concurrent_log_handler.ConcurrentRotatingFileHandler',
            'filename': os.path.join(log_dir, 'context'),
            'formatter': 'default',
            'encoding': 'utf-8',
            'maxBytes': 1024 * 1024 * 10,
            'backupCount': 1
        },
        'debug_mode_handler': {
            'level': "DEBUG",
            'class': 'concurrent_log_handler.ConcurrentRotatingFileHandler',
            'filename': os.path.join(log_dir, 'debug_mode'),
            'formatter': 'default',
            'encoding': 'utf-8',
            'maxBytes': 1024 * 1024 * 50,
            'backupCount': 4
        },
        'error_log_handler': {
            'level': "ERROR",
            'class': 'concurrent_log_handler.ConcurrentRotatingFileHandler',
            'filename': os.path.join(log_dir, 'error'),
            'formatter': 'full',
            'encoding': 'utf-8',
            'maxBytes': 1024 * 1024 * 10,
            'backupCount': 2,
        },
        'full_log_handler': {
            'level': "DEBUG",
            'class': 'concurrent_log_handler.ConcurrentRotatingFileHandler',
            'filename': os.path.join(log_dir, 'full'),
            'formatter': 'full',
            'encoding': 'utf-8',
            'maxBytes': 1024 * 1024 * 20,
            'backupCount': 25,
            'filters': ['full_log_filter']
        },

    },
    'loggers': {
        'default': {
            'handlers': ['default_log_handler', 'console', 'error_log_handler'],
            'level': level,
            'propagate': True,
        },
        'calculation_log': {
            'handlers': ['calculation_log_handler', 'console'],
            'level': 'DEBUG',
            'propagate': True,
        },
        'calculation_backend_log': {
            'handlers': ['calculation_backend_log_handler', 'console', 'error_log_handler'],
            'level': 'DEBUG',
            'propagate': True,
        },
        **(
            {
                '__cvxpy__': {
                    'handlers': ['calculation_backend_log_handler', 'error_log_handler'],
                    'level': 'DEBUG',
                    'propagate': True,
                },
            } if (solver_debug is True) else dict()
        ),
        'task_listener_log': {
            'handlers': ['task_listener_log_handler', 'error_log_handler'],
            'level': level,
            'propagate': True,
        },
        'api_log': {
            'handlers': ['api_log_handler'],
            'level': level,
            'propagate': True,
        },
        # --*-- redefine tornado log to single file begin --*--
        'tornado.access': {
            'handlers': ['web_log_handler'],
            'level': "DEBUG",
            'propagate': True,
        },
        'tornado.application': {
            'handlers': ['web_log_handler'],
            'level': "DEBUG",
            'propagate': True,
        },
        'tornado.general': {
            'handlers': ['web_log_handler'],
            'level': "DEBUG",
            'propagate': True,
        },
        # --*-- redefine tornado log to single file end  --*--
        # --*-- redefine peewee log to single file begin --*--
        'peewee': {
            'handlers': ['db_log_handler'],
            'level': "DEBUG",
            'propagate': True,
        },
        # --*-- redefine peewee log to single file end  --*--
        # --*-- redefine apscheduler log to single file begin --*--
        'flask_apscheduler': {
            'handlers': ['scheduler_log_handler'],
            'level': "DEBUG",
            'propagate': True,
        },
        'apscheduler.scheduler': {
            'handlers': ['scheduler_log_handler'],
            'level': "DEBUG",
            'propagate': True,
        },
        'apscheduler.jobstores': {
            'handlers': ['scheduler_log_handler'],
            'level': "DEBUG",
            'propagate': True,
        },
        'apscheduler.executors': {
            'handlers': ['scheduler_log_handler'],
            'level': "DEBUG",
            'propagate': True,
        },
        # --*-- redefine apscheduler log to single file end   --*--
        'deletion': {
            'handlers': ['deletion_log_handler', 'error_log_handler'],
            'level': level,
            'propagate': True,
        },
        'load_case': {
            'handlers': ['load_case_log_handler'],
            'level': level,
            'propagate': True,
        },
        'listener': {
            'handlers': ['listener_handler', 'error_log_handler'],
            'level': level,
            'propagate': True,
        },
        'manager': {
            'handlers': ['manager_handler'],
            'level': level,
            'propagate': True,
        },
        'context_log': {
            'handlers': ['context_handler', 'console'],
            'level': level,
            'propagate': True,
        },
        'debug_mode': {
            'handlers': ['debug_mode_handler'],
            'level': "DEBUG",
            'propagate': True,
        },
        '': {
            'handlers': ['full_log_handler', 'error_log_handler'],
            'level': "DEBUG",
            'propagate': True,
        },
    }
})

logger = logging.getLogger("default")
calculation_log_logger = logging.getLogger("calculation_log")
calculation_backend_log_logger = logging.getLogger("calculation_backend_log")
debug_logger = logging.getLogger("debug_mode")
deletion_logger = logging.getLogger("deletion")
api_logger = logging.getLogger("api_log")
context_logger = logging.getLogger("context_log")
task_listener_logger = logging.getLogger("task_listener_log")
