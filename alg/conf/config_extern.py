# 公共参数设置
public_para_dict = {
    'reserve_slack': False,
    'zone_power_supply_series_mk': 100,  # 分区供电裕度紧张门槛值
}

henan_area_name_mapping = {
    '瀛洲-陕州': '三门峡',
    '瀛州-陕州': '三门峡',
    '广成-香山': '平顶山北',
    '嘉和-汉都-瀛洲': '洛南',
    '嘉和-汉都-瀛州': '洛南',
    '嘉和-汉都-济源-瀛州-牡丹': '洛阳-济源',
    '墨公-姚孟-湛河': '平顶山南',
    '墨公-姚孟厂-湛河': '平顶山南',
    '姚孟厂-湛河': '平顶山南',
    '春申-浉河-金牛': '信阳',
    '春申-浉河': '信阳',
    '周南-周口-迟营': '周口',
    '周口-迟营': '周口',
    '周口-迟营-郸城': '周口',
    '嫘祖-嵖岈-挚亭-驻西': '驻马店',
    '嫘祖-嵖岈-挚亭': '驻马店',
    '邵陵-郾城': '漯河',
    '彰德-朝歌-洹安': '安鹤',
    '仓颉-卫都': '濮阳',
    '博爱-竹贤': '焦作',
    '冀州-塔铺-潞南-获嘉': '新乡',
    '冀州-塔铺-获嘉': '新乡',
    '济源-牡丹': '济源-洛北',
    '嵩山-武周-港东-郑州': '郑州南',
    '嵩山-武周-郑州': '郑州南',
    '嵩山-武周-航空-郑州':'郑州南',
    '中牟-官渡-建新-怀德-惠济': '郑州北',
    '官渡-惠济': '郑州北',
    '官渡-怀德': '郑州北',
    '祥符-菊城-龙亭': '开封',
    '祥符-菊城': '开封',
    '圣临-庄周-沙盟': '商丘',
    '圣临-庄周-沙盟-祥符-菊城-龙亭': '商丘+开封',
    '圣临-庄周': '商丘',
    '涂会-花都': '许昌',
    '奚贤-玉都-白河-群英': '南阳'}

henan_area_region_mapping = {
    '三门峡': '豫西',
    '平顶山北': '豫南',
    '洛南': '豫西',
    '洛阳-济源': '豫西',
    '平顶山南': '豫南',
    '信阳': '豫南',
    '周口': '豫南',
    '驻马店': '豫南',
    '漯河': '豫南',
    '安鹤': '豫北',
    '濮阳': '豫北',
    '焦作': '豫北',
    '新乡': '豫北',
    '济源-洛北': '豫西',
    '郑州南': '豫中东',
    '郑州北': '豫中东',
    '开封': '豫中东',
    '商丘': '豫中东',
    '商丘+开封':'豫中东',
    '许昌': '豫南',
    '南阳': '豫南'}

jiangsu_area_name_mapping = {
    '徐圩-艾塘-花果': '连云港',
    '伊芦-徐圩-艾塘-花果': '连云港',
    '伊芦-徐圩': '连云港',
    '上党-廻峰': '廻上',
    '东善-廻峰': '南京南',
    '上党': '镇江西',
    '熟北': '常熟',
    '木渎-熟南': '苏州西',
    '木渎-熟南-越溪': '苏州西',
    '熟南': '苏州西',
    '木渎-越溪': '苏州西南',
    '玉山-车坊': '车坊',
    '东善-青龙-龙王': '东龙',
    '东善-青石-龙王': '东龙',
    '东善-建康-青石-龙王': '东龙',
    '三官-东洲-新丰': '通东南',
    '斗山-陆桥': '斗陆',
    '凤城-高邮': '泰扬北',
    '凤城': '泰州中',
    '高邮': '扬泰北',
    '滨响-潘荡': '盐城北',
    '射阳-盐西-盐都': '盐城中',
    '盐都-高荣-龙口': '盐城中',
    '盐西-盐都': '盐城中',
    '盐都-龙口': '盐城中',
    '丰汇-双草': '盐城南',
    '惠泉': '惠泉',
    '仪征-江都': '扬州',
    '锦丰': '张家港',
    '大港-晋陵-访仙': '访晋',
    '晋陵-梦溪-访仙': '访晋',
    '秋藤': '秦淮',
    '秦淮-龙王': '秦淮',
    '秋藤-秦淮-龙王': '秦淮',
    '天目-茅山': '茅溧',
    '梅里-鼋渚': '梅里',
    '映月-梅里': '梅里',
    '三堡-任庄-黄集': '徐州西',
    '三汊-秋藤': '宁北',
    '山江-三汊': '宁北北',
    '山江-秋藤': '宁北南',
    '上河-安澜-旗杰': '淮安',
    '上河': '淮安',
    '安澜-旗杰': '淮安',
    '全福-石牌': '石牌',
    '石牌': '石牌',
    '双泗-姚湖-宿豫-岱山-沭阳-钟吾': '徐宿',
    '泰兴-胜利': '泰南',
    '大泗-泰兴-胜利': '泰南',
    '泰兴-海阳-胜利': '泰南',
    '泰兴-行知-胜利': '泰南',
    '太仓': '太仓',
    '吴南-吴江': '吴江西',
    '吴江-笠泽': '吴江西',
    '吴南-吴江东': '吴江东',
    '吴江-笠泽东': '吴江东',
    '武南-青洋': '武南',
    '全福-玉山': '玉山',
    '张家-晨阳': '张家港',
    '仲洋-扶海-胜利-通海': '通西北',
    '仲洋-扶海-胜利': '通西北',
    '扶海-通海': '通西北',
    '岷珠-阳羡': '岷珠',
}

jiangsu_area_region_mapping = {
    '连云港': '苏北',
    '廻上': '苏南',
    '常熟': '苏南',
    '苏州西': '苏南',
    '车坊': '苏南',
    '东龙': '苏南',
    '通东南': '苏北',
    '斗陆': '苏南',
    '泰扬北': '苏北',
    '扬泰北': '苏北',
    '泰州中': '苏北',
    '盐城中': '苏北',
    '惠泉': '苏南',
    '扬州': '苏北',
    '张家港': '苏南',
    '访晋': '苏南',
    '秦淮': '苏南',
    '茅溧': '苏南',
    '梅里': '苏南',
    '盐城北': '苏北',
    '徐州西': '苏北',
    '宁北': '苏北',
    '宁北北': '苏北',
    '宁北南': '苏北',
    '南京南': '苏南',
    '镇江西': '苏南',
    '淮安': '苏北',
    '石牌': '苏南',
    '徐宿': '苏北',
    '泰南': '苏北',
    '太仓': '苏南',
    '玉山': '苏南',
    '通西北': '苏北',
    '武南': '苏南',
    '吴江西': '苏南',
    '吴江东': '苏南',
    '岷珠': '苏南',
    '盐城南': '苏北'
}
gansu_area_name_mapping = {}
gansu_area_region_mapping = {}

config_exter_dict = {
    'prov_key': '',
    'zone_col': 'topo_zone',  # 分区使用实际的'zone'分区或拓扑搜索结果的'topo_zone'
    'topo_dis_vn_kv': 500,  # 拓扑显示的默认电压等级;
    'device_dis_vn_kv': 500,  # 重载/越限设备的默认显示的最低电压等级;
    'start_time': '2025-01-01 00:00:00',
    'sim_freq': 'H',
    'trafo_heavy_ratio': 0.85,
    'trafo_over_ratio': 0.99,
    'line_heavy_ratio': 0.85,
    'line_over_ratio': 0.99,
    'interface_heavy_ratio': 0.85,
    'interface_over_ratio': 0.99,
    'cal_overlimit_rate': False,  # 设备显示重载时长占比还是越限次数，False--次数;
    # 针对例如：上备用裕度、调峰裕度、无关联电源依存度、爬坡能力指标,针对分区来说意思不大，在进行分区推演时，也显示全网指标，在此处定义;
    'metrics_all_to_zone': ['reserve_low', 'peak_shaving', 'non_inertia_penetration', 'ramp_cap_upward'],
    'device_rate_step': [0, 0.3, 0.5, 0.8, 1.0],  # 统计设备负载率的分级台阶门槛值;
}
# 如果有直流,直流的所在feed的索引号,及名称定义;
dc_feedin_dict = {}

# 不同省份部署场景的配置选项--内容和上面一样，上面是默认的参数;
scene_config_dict = {
    'jiyuan': {
        'config_exter_dict': {
            'prov_key': '',
            'power_unit': 'M',  # 首页功率单位: 'M':兆瓦;'W':万千瓦;
            'zone_col': 'zone',
            'topo_dis_vn_kv': 35,  # 拓扑显示的默认电压等级;
            'device_dis_vn_kv': 35,  # 重载/越限设备的默认显示的最低电压等级;
            'start_time': '2025-01-01 00:00:00',
            'sim_freq': 'H',
            'trafo_heavy_ratio': 0.8,
            'trafo_over_ratio': 0.998,
            'line_heavy_ratio': 0.8,
            'line_over_ratio': 0.998,
            'interface_heavy_ratio': 0.85,
            'interface_over_ratio': 0.998,
            'cal_overlimit_rate': False,
            'metrics_all_to_zone': ['reserve_low', 'peak_shaving', 'non_inertia_penetration', 'ramp_cap_upward'],
            'device_rate_step': [0, 0.3, 0.5, 0.8, 1.0],
            'psm_trafo_rate': 0.75,  # 计算分区供电裕度时,取下网1级主变容量的百分比;
        },
        'dc_feedin_dict': {},
    },
    'henan': {
        'config_exter_dict': {
            'prov_key': '豫',
            'power_unit': 'W',
            'zone_col': 'topo_zone',
            'zone_name_from_yml': True,
            'area_name_mapping': henan_area_name_mapping,
            'area_region_mapping': henan_area_region_mapping,
            'topo_dis_vn_kv': 500,  # 拓扑显示的默认电压等级;
            'device_dis_vn_kv': 500,  # 重载/越限设备的默认显示的最低电压等级;
            'start_time': '2025-01-01 00:00:00',
            'sim_freq': 'H',
            'trafo_heavy_ratio': 0.85,
            'trafo_over_ratio': 0.998,
            'line_heavy_ratio': 0.85,
            'line_over_ratio': 0.998,
            'interface_heavy_ratio': 0.85,
            'interface_over_ratio': 0.998,
            'device_rate_step': [0, 0.3, 0.5, 0.8, 1.0],
            'cal_overlimit_rate': False,
            'metrics_all_to_zone': ['reserve_low', 'peak_shaving', 'non_inertia_penetration', 'ramp_cap_upward'],
            'psm_trafo_rate': 0.75,  # 计算分区供电裕度时,取下网1级主变容量的百分比;
        },
        'dc_feedin_dict': {
            '灵宝直流': [8, 10],
            '中天直流': [9],
            '青豫直流': [11, 12],
            '南荆特高压': [2, 6],
            '豫武特高压': [0, 1],
            '长南特高压': [7],
        },
    },
    'hunan': {
        'config_exter_dict': {
            'prov_key': '湘',
            'power_unit': 'W',
            'zone_col': 'topo_zone',
            'topo_dis_vn_kv': 500,  # 拓扑显示的默认电压等级;
            'device_dis_vn_kv': 500,  # 重载/越限设备的默认显示的最低电压等级;
            'start_time': '2025-01-01 00:00:00',
            'sim_freq': 'H',
            'trafo_heavy_ratio': 0.85,
            'trafo_over_ratio': 0.998,
            'line_heavy_ratio': 0.85,
            'line_over_ratio': 0.998,
            'interface_heavy_ratio': 0.85,
            'interface_over_ratio': 0.998,
            'cal_overlimit_rate': False,
            'device_rate_step': [0, 0.3, 0.5, 0.8, 1.0],
            'metrics_all_to_zone': ['reserve_low', 'peak_shaving', 'non_inertia_penetration', 'ramp_cap_upward'],
            'psm_trafo_rate': 0.75,  # 计算分区供电裕度时,取下网1级主变容量的百分比;
        },
        'dc_feedin_dict': {
            '祁韶直流': [0, 1],
            '宁湘直流': [2, 3]
        },
    },
    'jiangsu': {
        'config_exter_dict': {
            'prov_key': '苏',
            'power_unit': 'W',
            'zone_col': 'topo_zone',
            'zone_name_from_yml': True,
            'area_name_mapping': jiangsu_area_name_mapping,
            'area_region_mapping': jiangsu_area_region_mapping,
            'topo_dis_vn_kv': 500,  # 拓扑显示的默认电压等级;
            'device_dis_vn_kv': 500,  # 重载/越限设备的默认显示的最低电压等级;
            'start_time': '2024-01-01 00:00:00',
            'sim_freq': 'H',
            'trafo_heavy_ratio': 0.85,
            'trafo_over_ratio': 0.998,
            'line_heavy_ratio': 0.85,
            'line_over_ratio': 0.998,
            'interface_heavy_ratio': 0.85,
            'interface_over_ratio': 0.998,
            'cal_overlimit_rate': True,
            'device_rate_step': [0, 0.3, 0.5, 0.8, 1.0],
            'metrics_all_to_zone': ['reserve_low', 'peak_shaving', 'non_inertia_penetration', 'ramp_cap_upward'],
            'psm_trafo_rate': 0.75,  # 计算分区供电裕度时,取下网1级主变容量的百分比;
        },
        'dc_feedin_dict': {
            '锦苏直流': [0],
            '雁淮直流': [1],
            '龙政直流': [2],
            '建苏直流': [3, 4, 5, 8],
            '锡泰直流': [6, 7]
        },
        # 江苏直流及非对称直流的落地映射;(推演调节和统计时使用;优先'dc_feedin_dict'使用)
        'feedin_name_map': {
            # '建苏直流': ["常熟", "常北", "木渎", "玉山"],
            '建苏直流': {"LCC": ["常熟"], "VSC1": ["常北"], "VSC2": ["木渎"], "VSC3": ["玉山"]},
            '锦苏直流': ["锦苏"],
            '龙政直流': ["龙政"],
            '雁淮直流': ["雁淮"],
            '锡泰直流': ["锡泰"]
        },
    },
    'gansu': {
        'config_exter_dict': {
            'prov_key': '甘',
            'power_unit': 'W',
            'zone_col': 'zone',
            'zone_name_from_yml': False,
            'area_name_mapping': gansu_area_name_mapping,
            'area_region_mapping': gansu_area_region_mapping,
            'topo_dis_vn_kv': 330,  # 拓扑显示的默认电压等级;
            'device_dis_vn_kv': 330,  # 重载/越限设备的默认显示的最低电压等级;
            'start_time': '2025-01-01 00:00:00',
            'sim_freq': 'H',
            'trafo_heavy_ratio': 0.85,
            'trafo_over_ratio': 0.998,
            'line_heavy_ratio': 0.85,
            'line_over_ratio': 0.998,
            'interface_heavy_ratio': 0.85,
            'interface_over_ratio': 0.998,
            'cal_overlimit_rate': False,
            'device_rate_step': [0, 0.3, 0.5, 0.8, 1.0],
            'metrics_all_to_zone': ['reserve_low', 'peak_shaving', 'non_inertia_penetration', 'ramp_cap_upward'],
            'psm_trafo_rate': 0.75,  # 计算分区供电裕度时,取下网1级主变容量的百分比;
        },
        'dc_feedin_dict': {
            '祁韶直流': [0]
        },
    },
}
