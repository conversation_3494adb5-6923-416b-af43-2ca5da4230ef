"""
系统平衡相关统计与分析功能函数
"""
import numpy as np

from alg.cal_ssmetrics.base_func import (GLOBAL_QW_NAME, get_time_range)
import alg.cal_ssmetrics.case_result_base_func as rlt_base
import alg.common_utils.data_utils_deal as ddeal


def get_zone_newenergy_consumpt_rate_distribute(case_info_dict: dict, result_dict: dict, area_details: dict,
                                                period: str = 'week'):
    """
    计算分区/全网新能源消纳率的日、周、月分布;
    net: 网络拓扑;
    result_dict: teap的计算结果
    area_details: 分区关联设备索引;
    period: 新能源消纳统计分析周期;
    """
    if period not in ['week', 'month']:  # 'hour', 'day'
        raise ValueError("获取分区的新能源消纳率分布的period参数错误!")
    area_name_list = list(area_details.keys())

    # 获取时间范围
    sum_snap = case_info_dict['config']['sum_snap']
    _tm_df = get_time_range(case_info_dict['config']['start_time'], sum_snap)
    period_flag = 'W' if period == 'week' else 'M'

    rtn_data = dict()
    for area_name in area_name_list:
        zn_wind_idx = area_details[area_name]['wind']
        zn_solar_idx = area_details[area_name]['solar']

        def get_zone_typenew_series_arr(result_dict, type: str, zn_idx):
            # 获取分析新能源时序
            if len(zn_idx) == 0:
                zn_output_arr = np.zeros(sum_snap)
                zn_curtail_arr = np.zeros(sum_snap)
            else:
                zn_output_arr = result_dict[f'{type}_output'][zn_idx, :].sum(axis=0)
                zn_curtail_arr = result_dict[f'{type}_curtailment'][zn_idx, :].sum(axis=0)
            return zn_output_arr, zn_curtail_arr

        zn_wind_output_arr, zn_wind_curtail_arr = get_zone_typenew_series_arr(result_dict, 'wind', zn_wind_idx)
        zn_solar_output_arr, zn_solar_curtail_arr = get_zone_typenew_series_arr(result_dict, 'solar', zn_solar_idx)
        # _tm_df = pd.concat([_tm_df, pd.DataFrame({
        #     'wind_output': zn_wind_output_arr,
        #     'wind_curtailment': zn_wind_curtail_arr,
        #     'solar_output': zn_solar_output_arr,
        #     'solar_curtailment': zn_solar_curtail_arr,
        #     'new_energy_output': zn_wind_output_arr + zn_solar_output_arr,
        #     'new_energy_curtailment': zn_wind_curtail_arr + zn_solar_curtail_arr,
        # }, index=_tm_df.index)], axis=1)
        _tm_df['wind_output'] = zn_wind_output_arr
        _tm_df['wind_curtailment'] = zn_wind_curtail_arr
        _tm_df['solar_output'] = zn_solar_output_arr
        _tm_df['solar_curtailment'] = zn_solar_curtail_arr
        _tm_df['new_energy_output'] = zn_wind_output_arr + zn_solar_output_arr
        _tm_df['new_energy_curtailment'] = zn_wind_curtail_arr + zn_solar_curtail_arr

        # 按period分组
        _tm_df_period_group = _tm_df.resample(period_flag).sum()
        # wind
        _tm_df_period_group_wind_all = _tm_df_period_group['wind_output'] + _tm_df_period_group['wind_curtailment']
        _tm_df_period_group_wind_all[_tm_df_period_group_wind_all < 0.01] = 0.01
        zn_wind_period_group_consump_rate = (
                _tm_df_period_group['wind_output'] / _tm_df_period_group_wind_all)  # 计算每个分组的 val1/val2 比值
        # solar
        _tm_df_period_group_solar_all = _tm_df_period_group['solar_output'] + _tm_df_period_group[
            'solar_curtailment']
        zn_solar_period_group_consump_rate = (
                _tm_df_period_group['solar_output'] / _tm_df_period_group_solar_all).tolist()
        # new energy
        _tm_df_period_group_wind_all = _tm_df_period_group['new_energy_output'] + _tm_df_period_group[
            'new_energy_curtailment']
        zn_new_period_group_consump_rate = (
                _tm_df_period_group['new_energy_output'] / _tm_df_period_group_wind_all).tolist()

        # 计算消纳率
        rtn_data[area_name] = {
            'wind_consump_rate': zn_wind_period_group_consump_rate.round(4).tolist(),
            # 'wind_curtailment_hours': np.zeros(n_list).tolist(),
            'solar_consump_rate': np.array(zn_solar_period_group_consump_rate).round(4).tolist(),
            # 'solar_curtailment_hours': np.zeros(n_list).tolist(),
            'new_energy_consump_rate': np.array(zn_new_period_group_consump_rate).round(4).tolist(),
            # 'new_energy_curtailment_hours': np.zeros(n_list).tolist(),
        }
    return rtn_data


def get_zone_margin_qr_data(config: dict, indicator_rlt_dict: dict, area_name=GLOBAL_QW_NAME):
    """
    获取分区月-日/月-日-时的供电不足分布二维码;--原始数据---分区供电裕度--时序数据; indicator_data--门槛值:indicator_thresholds
    config: 配置参数dict
    indicator_rlt_dict: case的指标分析结果dict
    valueType: 需要的数据： 'psm': 供电裕度; -1--无数据;0--供电裕度充足;1--供电裕度紧张;2--供电裕度不足(缺电)
    disseries: 是否考虑供电紧张;
    """
    if 'indicator_data' not in indicator_rlt_dict:
        raise ValueError("算例分析结果中无指标分析结果")

    if area_name not in indicator_rlt_dict['indicator_data']:
        raise ValueError(f"算例分析结果中无分区:{area_name}指标分析结果")
    zone_psm_dataseries = indicator_rlt_dict['indicator_data'][area_name]['power_supply_margin']
    # zone_indicator_thresholds_series = indicator_rlt_dict['indicator_thresholds']

    zn_psm_qr = ddeal.exchange_data_to_qrformat(np.array(zone_psm_dataseries), config, mk_val=[0, 320],
                                                flag_val=[2, 1, 0], sta_type="min")
    return zn_psm_qr["month_day"], zn_psm_qr["day_hour"]


def get_newenergycutailment_qr_data(config: dict, result_dict: dict):
    """
    获取新能源弃电二维码数据;
    config: 配置参数dict
    indicator_rlt_dict: case的指标分析结果dict
    data_dataseries: 需要的数据
    """
    data_dataseries = result_dict["wind_curtailment"].sum(axis=0) + result_dict["solar_curtailment"].sum(axis=0)
    new_curtail_qr = ddeal.exchange_data_to_qrformat(data_dataseries, config, mk_val=[0.1], flag_val=[0, 1],
                                                     sta_type="max")
    return new_curtail_qr["month_day"], new_curtail_qr["day_hour"]


def get_channel_ability_qr_data(net, config, result_dict, inf_collection: dict, channel_name: str):
    """
    获取通道的输电能力二维码数据;
    :param net:
    :param result_dict:
    :param inf_collection: case_sta.statistics_dict["inf_collection"]
    :return:
    """
    if channel_name not in inf_collection:
        raise ValueError(f"输入的通道名称不在通道列表中：{channel_name}")

    inf_dict = dict()  # 通道关联断面字典;
    channel_inf_name_list = list(inf_collection[channel_name]["relation"]["line"].keys()) + list(
        inf_collection[channel_name]["relation"]["trafo"].keys())
    for i_c in channel_inf_name_list:
        ele_dict = {"channel_name": channel_name, "interface_name": i_c}
        inf_dict[i_c] = rlt_base.get_channel_power_info(net=net, result_dict=result_dict, channel_dict=inf_collection,
                                                        ele_dict=ele_dict)
    fability_series, over_inf_num = rlt_base.calculate_channel_power_ability(inf_dict)  # 通道输电能力;

    # # 获取时间范围
    _tm_df = get_time_range(config['start_time'], config['sum_snap'])
    # 计算年/月/日/时分布
    _tm_df['month'] = _tm_df.index.month
    _tm_df['day'] = _tm_df.index.day
    _tm_df['hour'] = _tm_df.index.hour
    # 通道输电能力二维图
    # < 1400: 0; 1400-1500:1, > 1500 :2
    ability_qr_dict = ddeal.exchange_data_to_qrformat_v2(_tm_df, np.array(fability_series), mk_val=[1400, 1500],
                                                         flag_val=[0, 1, 2], sta_type="min")
    # 通道越限断面数目二维码; < 1:0 ,1-2:1,>=3:2
    overlimit_num_qr_dict = ddeal.exchange_data_to_qrformat_v2(_tm_df, np.array(over_inf_num), mk_val=[1, 3],
                                                               flag_val=[0, 1, 2], sta_type="max")
    return ability_qr_dict, overlimit_num_qr_dict


def get_region_powerslack_hours(config: dict, indicator_rlt_dict: dict, disType='hour'):
    """
    获取区域/分区的供电缺口小时的0-23小时分布特性;
    indicator_rlt_dict:指标分析结果：
    disType: 分布特性分析类型,'hour': 0-23小时分布; 'month'：1-12月分布;
    return:
    dict,
    key:zone:value=list(),len=23
    """
    # 区域供电不足小时分布
    region_hourdistri = dict()
    if 'area_region_mapping' in config.keys():
        region_list = set(config['area_region_mapping'].values())  # 需要的分析的区域列表
    else:
        region_list = set()
    keys_with_val_dict = dict()
    for region_i in region_list:
        region_hourdistri[region_i] = dict()
        region_hourdistri[region_i]['up_reserve_low_hours'] = [0] * 24  # 正用备用不足小时数据
        region_hourdistri[region_i]['power_slack_max'] = [0] * 24  # 最大供电缺口
        # 获取区域关联的分区;
        # 初始化一个空列表来存储匹配的键
        keys_with_val = []
        # 遍历字典的键值对
        for key, value in config['area_region_mapping'].items():
            # 如果值等于val1，则将键添加到列表中
            if value == region_i:
                keys_with_val.append(key)
        keys_with_val_dict[region_i] = keys_with_val

    # 所有分区的供电裕度时序曲线;--变成0-23
    num_snaps = config['sum_snap']
    case_start_time = config['start_time']
    _tm_df = get_time_range(case_start_time, num_snaps)  # 生成时序;
    zone_psm_slack_flag = dict()

    zone_name_list = list(indicator_rlt_dict['indicator_data'].keys())  # 所有分区名
    for zone_name, zone_data in indicator_rlt_dict['indicator_data'].items():
        zone_psm_arr = np.array(zone_data['power_supply_margin'])  # 万千瓦;
        zone_psm_slack_flag[zone_name] = (zone_psm_arr < 0).astype(int)
        _tm_df[zone_name] = zone_psm_slack_flag[zone_name]

    # 遍历区域,将分区的数据相加;
    _tm_region_df = get_time_range(case_start_time, num_snaps)  # 生成时序;
    for region_i in region_list:
        region_zone = keys_with_val_dict[region_i]
        region_zone = [zone_name for zone_name in region_zone if zone_name in _tm_df.columns]
        _tm_region_df[region_i] = _tm_df[region_zone].sum(axis=1)

    # 加入所有分区
    for i_zone in zone_name_list:
        _tm_region_df[i_zone] = _tm_df[i_zone]

    # 将_tm_region_df 按 索引的hour进行sum聚合--供电不足小时数
    if disType == 'hour':
        _tm_region_df['hour'] = _tm_region_df.index.hour
        _tm_grp_hour_df = _tm_region_df.groupby(['hour']).sum()
    elif disType == 'month':
        _tm_region_df['month'] = _tm_region_df.index.month
        _tm_grp_hour_df = _tm_region_df.groupby(['month']).sum()
    else:
        raise Exception('disType must be hour or month')

    # 转为输出dict:
    _tm_grp_hour_dict = dict()
    for i_col in _tm_grp_hour_df.columns.tolist():
        if i_col == 'data':
            pass
        _tm_grp_hour_dict[i_col] = _tm_grp_hour_df[i_col].values.tolist()

    # 报告用,额外加入盐城中，东龙
    # _tm_region_df['盐城中'] = _tm_df['盐城中']
    # _tm_region_df['东龙'] = _tm_df['东龙']
    # # 将_tm_region_df 按 索引的hour进行sum聚合
    # _tm_region_df['hour'] = _tm_region_df.index.hour
    # _tm_grp_hour_df = _tm_region_df.groupby(['hour']).sum()

    # _tm_grp_hour_df.to_csv(os.path.join("./region_psm_24hour.csv"), index=False, encoding='gbk')

    # _tm_psm_df = get_time_range(case_start_time, num_snaps)  # 生成时序;
    # _tm_psm_df['盐城中'] = indicator_rlt_dict['indicator_data']['盐城中']['power_supply_margin']
    # _tm_psm_df['东龙'] = indicator_rlt_dict['indicator_data']['东龙']['power_supply_margin']
    # _tm_psm_df.to_csv(os.path.join("./zone_psm_series.csv"), index=True,encoding='gbk')

    return _tm_grp_hour_dict


def get_channel_ratio_time_distribute(net, config, result_dict: dict, inf_collection: dict, channel_name: str,
                                      freq_h: str = "season"):
    """
    获取2级通道的 重载时长、过载时长、等效过载时长在时间上的分布;(小时、月度、季度)的分布信息;
    :param net: 网络拓扑
    :param config:  算例配置参数
    :param result_dict: TEAP计算结果;
    :param inf_collection: 2级通道关联关系
    :param channel_name: 需要统计的通道名称 "过江断面"
    :param freq_h: "分布统计方法","hour": 0-23小时分布; "month":月度分布; "season":季度分布;
    :return:
    """
    channel_dict = dict()

    # 构建时序;
    _tm_df = get_time_range(config['start_time'], config['sum_snap'])  # 生成时序;

    # 增加需要统计分布特性的列数据及agg_dict
    # 重载标志("heavy_hours")、过载标志("over_hours")、越限率(<1.0取0.0)("over_eq_hours") 等效时长 = 越限率累加;
    channel_inf_name_list = list(inf_collection[channel_name]["relation"]["line"].keys()) + list(
        inf_collection[channel_name]["relation"]["trafo"].keys())
    for i_c in channel_inf_name_list:
        ele_ratio_dict = {
            "heavy_hours": 0,  # 线路重载时长
            "over_hours": 0,  # 线路过载时长
            "over_eq_hours": 0,  # 线路等效过载时长
            "max_p_rate": 0.0,  # 最大负载率
            "distribute": dict()  # 分布情况, key:list
        }
        ele_dict = {"channel_name": channel_name, "interface_name": i_c}
        inf_dict = rlt_base.get_channel_power_info(net=net, result_dict=result_dict, channel_dict=inf_collection,
                                                   ele_dict=ele_dict)
        inf_rate = np.array(inf_dict['powerflow']) / min(999999.0, inf_dict['limit'])  # 负载率;
        inf_rate_flag = rlt_base.cal_topo_elements_loadrate_flags(inf_rate, rate_mk=[0.8, 1.0], flag_val=[0, 1, 2])
        _tm_inf_df = _tm_df.copy()
        _tm_inf_df["heavy_hours"] = np.where(inf_rate_flag == 1, 1, 0)  # 这里是重载标志，统计时根据标志累加得到重载小时
        _tm_inf_df["over_hours"] = np.where(inf_rate_flag == 2, 1, 0)
        _tm_inf_df["over_eq_hours"] = np.where(inf_rate >= 1.0, inf_rate, 0)
        _tm_inf_df["max_p_rate"] = inf_rate
        agg_dict = {"heavy_hours": "sum", "over_hours": "sum", "over_eq_hours": "sum", "max_p_rate": "max"}

        # 调用分布特性处理函数;data_df_freq_distributed_base
        distri_df = ddeal.data_df_freq_distributed_base(_data_df=_tm_inf_df, freq_h=freq_h, agg_dict=agg_dict)
        for col in distri_df.columns:
            ele_ratio_dict["distribute"][col] = distri_df[col].values.round(4).tolist()
        # 全年数据;
        for i_col in ["heavy_hours", "over_hours", "over_eq_hours"]:
            ele_ratio_dict[i_col] = float(np.sum(distri_df[i_col].values).round(1))
        ele_ratio_dict["max_p_rate"] = float(np.max(inf_rate).round(3))

        channel_dict[i_c] = ele_ratio_dict
    return channel_dict


def get_zone_psm_time_distribute(config, indicator_data: dict, zone_name_list: list = list(),
                                 freq_h: str = "season"):
    """
    获取分区供电裕度的在时间上的分布;(小时、月度、季度)的分布信息;
    :param config:
    :param indicator_data:  指标预处理结果dict,indicator_dict["indicator_data"]
    :param zone_name_list: list; such as :["访晋","泰扬北"]
    :param freq_h: "分布统计方法","hour": 0-23小时分布; "month":月度分布; "season":季度分布;
    :return:
    """
    zone_dict = dict()

    # 构建时序;
    _tm_df = get_time_range(config['start_time'], config['sum_snap'])  # 生成时序;

    # 增加需要统计分布特性的列数据及agg_dict
    for i_c in zone_name_list:
        ele_ratio_dict = {
            "min_psm": 0,  # 最低供电裕度;
            "distribute": dict()  # 分布情况, key:list
        }
        if i_c not in indicator_data.keys():
            zone_dict[i_c] = ele_ratio_dict
            continue

        _tm_data_df = _tm_df.copy()
        zone_psm_series = np.array(indicator_data[i_c]["power_supply_margin"])
        _tm_data_df["min_psm"] = zone_psm_series
        agg_dict = {"min_psm": "min"}

        # 调用分布特性处理函数;data_df_freq_distributed_base
        distri_df = ddeal.data_df_freq_distributed_base(_data_df=_tm_data_df, freq_h=freq_h, agg_dict=agg_dict)
        for col in distri_df.columns:
            ele_ratio_dict["distribute"][col] = distri_df[col].values.round(4).tolist()
        # 全时序数据统计;
        ele_ratio_dict["min_psm"] = float(np.min(zone_psm_series).round(2))  # 万千瓦;
        zone_dict[i_c] = ele_ratio_dict
    return zone_dict


# 直流相关数据获取;
def get_dcline_power_series(net, result_dict: dict, dc_line_idx: list = list(), is_all: bool = True,
                            time_list: list = None):
    """
    获取dcline中的直流相关功率时序曲线;dcline_power_to  (TEAP结果中是直流受入为正)
    :param net: 
    :param dc_line_idx: 直流索引,空的话,为全部数据
    :param result_dict: 
    :param is_all: 
    :param time_list: 
    :return: 
    """
    data_dict = dict()
    if "dcline_power_from" not in result_dict or "dcline" not in net.keys() or len(net["dcline"]) == 0:
        return data_dict, {}

    # 直流名称:索引号关系;
    dc_name_dict = {row["name"]: k for k, row in net["dcline"].iterrows()}

    if len(dc_line_idx) == 0:
        # 直接获取全部直流;
        dc_line_idx_valid = net["dcline"].index.tolist()
    else:
        dc_line_idx_valid = dc_line_idx

    if is_all or len(time_list) == 0:
        for i_idx in dc_line_idx_valid:
            dcline_name = net["dcline"].iloc[i_idx]["name"]
            data_dict[dcline_name] = (result_dict["dcline_power_to"][i_idx, :] * 0.1).round(
                1).tolist()
    else:
        for i_idx in dc_line_idx_valid:
            data_dict[net["dcline"].iloc[i_idx]["name"]] = (
                    result_dict["dcline_power_from"][i_idx, time_list] * 0.1).round(1).tolist()  # MW->万千瓦
    return data_dict, dc_name_dict


def get_casesys_control_info(net, config, result_dict: dict, inf_collection: dict = dict(),
                             str_sysoptimize: str = "系统运行成本最优", freq_h="season"):
    """
    算例控制效果信息:
    # 直流信息,dict :{"usingHours":0,"max_regulation_times_per_day":4,"p_levels":[400, 500, 600, 800, 900, 1000, 1100, 1200]};
    # 利用小时、日最大调节次数、调节档位
    # str_sysoptimize: 系统运行优化目标;
    :return:
    """
    sys_control_dict = {
        "sys_optimize": str_sysoptimize,  # 系统优化目标;
        "newConsump_rate": 0.0,  # 新能源消纳率
        "acrossCJRiverAbility": 0.0,  # 过江通道平均输送能力;
        "dcline": dict(),
        # 直流信息,dict :{"name":dict};
    }
    # 计算新能源消纳率
    new_output = 0.001
    new_curtail = 0
    for i_type in ["wind", "solar"]:
        if f"{i_type}_output" in result_dict:
            new_output += float(result_dict[f"{i_type}_output"].sum())
            new_curtail += float(result_dict[f"{i_type}_curtailment"].sum())
    sys_control_dict["newConsump_rate"] = round(new_output / (new_output + new_curtail), 4)

    # 过江通道平均输送能力
    channel_name = "过江断面"
    if channel_name not in inf_collection:
        acrossCJRiverAbility = 0
    else:
        inf_dict = dict()  # 通道关联断面字典;
        channel_inf_name_list = list(inf_collection[channel_name]["relation"]["line"].keys()) + list(
            inf_collection[channel_name]["relation"]["trafo"].keys())
        for i_c in channel_inf_name_list:
            ele_dict = {"channel_name": channel_name, "interface_name": i_c}
            inf_dict[i_c] = rlt_base.get_channel_power_info(net=net, result_dict=result_dict,
                                                            channel_dict=inf_collection,
                                                            ele_dict=ele_dict)
        fability_series, _ = rlt_base.calculate_channel_power_ability(inf_dict)  # 通道输电能力;
        acrossCJRiverAbility = round(float(np.array(fability_series).mean()), 1)  # 万千瓦;
    sys_control_dict["acrossCJRiverAbility"] = acrossCJRiverAbility

    # 直流信息;
    dcline_dict = dict()
    if "dcline_power_to" not in result_dict or "dcline" not in net.keys() or len(net["dcline"]) == 0:
        pass
    else:
        # 构建时序;
        _tm_df = get_time_range(config['start_time'], config['sum_snap'])  # 生成时序;

        for idx, row in net["dcline"].iterrows():
            # 直流利用小时计算;
            i_dc_limit = min(999999, row["max_p_mw"])  # 直流限额;
            using_rate = np.abs(result_dict["dcline_power_from"][idx, :]) / i_dc_limit

            # 直流利用小时季度分布
            _tm_data_df = _tm_df.copy()
            _tm_data_df["usingHours"] = using_rate
            agg_dict = {"usingHours": "sum"}
            # 调用分布特性处理函数;data_df_freq_distributed_base
            distri_df = ddeal.data_df_freq_distributed_base(_data_df=_tm_data_df, freq_h=freq_h, agg_dict=agg_dict)
            # 直流信息汇总：
            dcline_dict[row["name"]] = {
                "max_regulation_times_per_day": row["max_regulation_times_per_day"],
                "p_levels": row["p_levels"].split(","),
                "usingHours": float(using_rate.sum().round(1)),
                "distribute": {
                    "usingHours": distri_df["usingHours"].values.round(1).tolist()
                }
            }
    sys_control_dict["dcline"] = dcline_dict
    return sys_control_dict
