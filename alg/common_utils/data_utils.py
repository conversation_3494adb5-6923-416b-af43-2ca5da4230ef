"""
正对页面展示的结果数据，封装函数，从结果文件或数据中获取基础数据并进行计算与分析，返回服务端
"""
import copy
import os.path

import h5py
import numpy as np
import pandas as pd
import pandapower as pp
from alg.cal_ssmetrics.case_base_func import (CTeapCase)
import alg.cal_ssmetrics.case_result_base_func as rlt_base
from alg.cal_ssmetrics.base_func import (_output_dict, GLOBAL_QW_NAME, get_time_range, get_ana_config_info,
                                         gen_type_disname_dict, spring_month, summer_month,
                                         autumn_month, winter_month, cof_power, cof_energy)
from alg.ProductionCostSimulation.utils.ptdf_util import (cal_branch_rate_simple, get_Pbr_max, get_inf_unit_idxes)
from alg.ProductionCostSimulation.run_calculator_utils.ss_calculator_base import get_trafo_capability_timestep
from alg.cal_ssmetrics.case_result_statis import (get_power_balance_data, func_newcutailment_reason_ana,
                                                  cal_all_devices_loadratio_df, cal_sys_peaking_val)
from alg.cal_ssmetrics.result_distribute_ana import result_zone_newerg_consump_rate, data_df_distribute_base
import alg.common_utils.data_utils_deal as ddeal


def get_allgen_capacity(case_info_dict: dict, area_name: str = GLOBAL_QW_NAME):
    """
    FUNC:获取所有机组的装机容量;--'capacity' ,转为json格式;
    args:
       case_info_dict: 缓存的case_info的结果;
    return:
       list()
    """
    if 'capacity' not in case_info_dict:
        raise ValueError("算例信息分析结果中没有机组装机数据")
    cap_out_list = list()

    if area_name == GLOBAL_QW_NAME:
        cap_dict = case_info_dict['capacity'][GLOBAL_QW_NAME]
        gen_num_dict = case_info_dict['genNumber'][GLOBAL_QW_NAME]
    else:
        cap_dict = case_info_dict['capacity'][area_name]
        gen_num_dict = case_info_dict['genNumber'][area_name]

    for i_type in cap_dict:
        if i_type in gen_type_disname_dict.keys() and i_type in cap_dict.keys() and i_type in gen_num_dict.keys():
            cap_out_list.append(
                {"name": gen_type_disname_dict[i_type], "value": cap_dict[i_type], "number": gen_num_dict[i_type]})

    # stogen
    if 'stogen_pump' in gen_num_dict and gen_num_dict['stogen_pump'] > 0:
        cap_out_list.append({"name": "抽蓄", "value": cap_dict['stogen_pump'], "number": gen_num_dict['stogen_pump']})
    if 'stogen_battery' in gen_num_dict and gen_num_dict['stogen_battery'] > 0:
        cap_out_list.append(
            {"name": "储能", "value": cap_dict['stogen_battery'], "number": gen_num_dict['stogen_battery']})

    heat_gen = list()
    for i_type in cap_dict:
        if i_type not in ['nuclear', 'wind', 'solar', 'hydro', 'feedin', "centralized_solar", "distributed_solar",
                          "centralized_wind", "distributed_wind", "stogen_battery", "stogen_pump"]:
            heat_gen.append(
                {"name": gen_type_disname_dict[i_type], "value": cap_dict[i_type], "number": gen_num_dict[i_type]})
    cap_out_list.append({"name": "火电", "children": heat_gen})

    # cap_out_js = json.dumps(cap_out_list)  # 转为json
    return cap_out_list


def get_devices_number(case_info_dict: dict):
    """
    获取设备数目明细
    """
    if 'deviceNum' not in case_info_dict:
        raise ValueError("case info has no key:{'deviceNum'}")
    return case_info_dict['deviceNum']


def get_result_power_curve_series(result_dict: dict, key_tab: str, case_net=None, to_sum: bool = True):
    """
    获取某个时序曲线
    case_net: case
    """

    # 映射dict--_output_dict
    if key_tab == "feedin_output":
        if case_net is None:
            raise ValueError(f"when key is {key_tab},must input <case_net> info!")
        if "feedin" not in case_net or case_net["feedin"].empty:
            return None
        # 先筛选出feedin的idx
        feedin_idx = case_net["feedin"][
            (case_net["feedin"]["type"] != "营销分布式") & case_net["feedin"]["in_service"]].index.to_list()
        if to_sum:
            rtn_data_series = result_dict[key_tab][feedin_idx, :].sum(axis=0).tolist()
        else:
            rtn_data_series = result_dict[key_tab][feedin_idx, :]
    else:
        if key_tab in result_dict.keys():
            if to_sum:
                rtn_data_series = result_dict[key_tab].sum(axis=0).tolist()
            else:
                rtn_data_series = result_dict[key_tab]
        else:
            raise ValueError(f"key input error,result_dict has no:{result_dict} key!")
    return rtn_data_series


def get_network_topo(case_info_dict: dict, time_no: int = None, result_dict: dict = dict(),
                     area_name: str = GLOBAL_QW_NAME, valueType: str = "time_no"):
    """
    FUNC: 获取电网拓扑;
    Args:
        case_info_dict:缓存的case_info的结果
        grp_vn_kv: 需要过滤的电压等级(500,即只输出500kV及以上的电站和线路);--放入配置文件conf/config_extern.py中:'topo_dis_vn_kv'
        time_no: 获取的结果潮流数据时刻;
        result_dict: 潮流结果数据;  'line_power',''
        area_name: 分区名称;
        area_details:dict,分区关联设备索引--area_details: dict = dict(),case_info_dict['zone']['device_relay']
        valueType: "average": 展示设备的平均负载率数据; "maxload":展示全网负荷最高的时刻设备负载率; "maxrate":展示全年最大设备负载率; "time_no":展示选择时刻点的设备负载率
        以双回综合负载率为准;
    returns:
        dict()
        point_data:list(), value: {"bus_index":[0],"name": "豫南特","type": "station","vn_kv": 525.0,"zone":'盐城中',"value": [114.717797,33.08905,0.1528,0.4],"info": {},"detail": []}
        # "index":net中的索引号; # value : 经度,纬度,值(什么值?);
        line_data:list(),  value:{"index":[0],"fromName": "金牛","toName": "嵖岈","value": 0.0012,"vn_kv": 525.0,"from_zone":'盐城中',"to_zone":'盐城中',"coords": [[114.414079,32.197161],[114.096559,32.839899]],
            "info": {
                "豫金牛50-豫嵖岈50": 0,
                "豫金牛50-豫浉嵖IK": 1
            }
        }
    """
    topo_data_dict = {
        'point_data': list(),
        'line_data': list(),
        'dcline_data': list(),
    }

    # T站名称不显示--此处将"T站"开头的记录的名称全部置为""

    # 电网拓扑
    if 'topo_info' not in case_info_dict:
        raise ValueError("算例信息分析结果中没有电网拓扑数据")

    bus_data_list = copy.deepcopy(case_info_dict['topo_info']['point_data'])
    line_data_list = copy.deepcopy(case_info_dict['topo_info']['line_data'])
    dcline_data_list = copy.deepcopy(case_info_dict['topo_info']['dcline_data'])

    # 获取分区关联设备索引
    area_details: dict = case_info_dict['zone']['device_relay']

    def has_common_element(list1, list2):
        set1 = set(list1)
        set2 = set(list2)
        return bool(set1.intersection(set2))

    # 根据分区过滤bus_data_list和line_data_list
    if area_name != GLOBAL_QW_NAME:
        if area_name not in area_details:
            raise ValueError(f"分区名称错误,area_name:{area_name},支持的分区名:{area_details.keys()}")
        # 若 bus_data_list中元素dict的'bus_idx'(list)中有任意一个元素在area_details[area_name]['device_relay']中即保留
        bus_data_list = [i_bus for i_bus in bus_data_list if
                         has_common_element(i_bus['bus_index'], area_details[area_name]['bus'])]
        line_data_list = [i_line for i_line in line_data_list if
                          has_common_element(i_line['related_line_idx']['pos'] + i_line['related_line_idx']['neg'],
                                             area_details[area_name]['line'])]
        dcline_data_list = [i_line for i_line in dcline_data_list if
                            has_common_element(i_line['related_line_idx']['pos'] + i_line['related_line_idx']['neg'],
                                               area_details[area_name]['line'])]
    # 根据电压等级过滤;
    if area_name == GLOBAL_QW_NAME:
        # grp_vn_kv_val = grp_vn_kv * 0.95
        grp_vn_kv_val = case_info_dict['config']['topo_dis_vn_kv'] * 0.95  # 全网拓扑时的过滤电压等级
    else:
        grp_vn_kv_val = min(220, case_info_dict['config']['topo_dis_vn_kv']) * 0.95  # 分区拓扑展示到220kV或者更低;

    # bus_data_list
    if not result_dict:
        for i_point in bus_data_list:
            if i_point['vn_kv'] > grp_vn_kv_val:
                # 如果i_point的'name'以'T站'开头,则将'name'置为""
                i_point['name'] = "" if i_point['name'].startswith('T站') else i_point['name']
                topo_data_dict['point_data'].append(i_point)
            else:
                pass
        for i_line in line_data_list:
            if i_line['vn_kv'] > grp_vn_kv_val:
                topo_data_dict['line_data'].append(i_line)
            else:
                pass
        return topo_data_dict  # 直接返回;

    max_load_idx = 0
    if time_no is not None:
        """
        需要补充时刻潮流;
        """
        # if not result_dict:
        #     raise ValueError("获取时刻断面潮流时需要传入潮流结果数据")
        # 获取潮流
        line_power = result_dict['line_power'][:, time_no]  # time_no时刻所有的线路潮流
        trafo_power = result_dict['trafo_power'][:, time_no]  # 主变负载
    else:
        if valueType == 'maxload':
            load_arr = result_dict['load'].sum(axis=0)
            max_load_idx = np.argmax(load_arr)
            # 获取潮流
            line_power = result_dict['line_power'][:, max_load_idx]  # time_no时刻所有的线路潮流
            trafo_power = result_dict['trafo_power'][:, max_load_idx]  # 主变负载
        elif valueType == 'maxrate':
            # 使用全年的最大潮流
            line_power = np.abs(result_dict['line_power']).max(axis=1)
            trafo_power = np.abs(result_dict['trafo_power']).max(axis=1)
            # 最大潮流从--预统计结果数据中获取;因为上述的统计方法有误差;多回线路的max值时刻并不一定是同时刻;
        else:
            # 使用全年的平均潮流
            line_power = np.abs(result_dict['line_power']).mean(axis=1)
            trafo_power = np.abs(result_dict['trafo_power']).mean(axis=1)

    # dcline
    if "dcline_power_from" in result_dict:
        if time_no is not None:
            dcline_power = result_dict['dcline_power_from'][:, time_no]  # time_no时刻所有的线路潮流
        elif valueType == 'maxload':
            dcline_power = result_dict['dcline_power_from'][:, max_load_idx]
        elif valueType == 'maxrate':
            dcline_power = np.abs(result_dict['dcline_power_from']).max(axis=1)
        else:
            dcline_power = np.abs(result_dict['dcline_power_from']).mean(axis=1)
    else:
        dcline_power = None

    # bus
    for i_bus in bus_data_list:
        if i_bus['vn_kv'] > grp_vn_kv_val:
            # 补充更新功率;i_point['value'][2]的值-单值; i_point['detail']
            pf_list = np.abs(np.divide(trafo_power[i_bus['related_trafo_idx']], np.array(i_bus['limit']))).tolist()
            i_bus['value'][2] = float(round(max(pf_list), 4)) if len(pf_list) > 0 else 0
            i_bus['powerflow_detail'] = pf_list

            # 如果i_point的'name'以'T站'开头,则将'name'置为""
            i_bus['name'] = "" if i_bus['name'].startswith('T站') else i_bus['name']
            topo_data_dict['point_data'].append(i_bus)
        else:
            pass

    # line
    for row in line_data_list:
        if row['vn_kv'] > grp_vn_kv_val:
            # 更新线路潮流;i_line['value']
            _line_flow = np.sum(line_power[row['related_line_idx']['pos']], axis=0) - np.sum(
                line_power[row['related_line_idx']['neg']], axis=0)
            row['value'] = round(np.abs(_line_flow) / row['limit'], 4)

            line_idx = [*row['related_line_idx']['pos'], *row['related_line_idx']['neg']]
            row['pRateDetail'] = np.abs(line_power[line_idx] / row['limitDetail']).round(4).tolist()  # 所有线路的负载率明细

            if _line_flow < -0.1:
                # 如果潮流反向,需要将起点和终点交换,将fromName/toName,coords[0]和coords[1]互换
                row['fromName'], row['toName'] = row['toName'], row['fromName']
                row['from_zone'], row['to_zone'] = row['to_zone'], row['from_zone']
                row['coords'] = [row['coords'][1], row['coords'][0]]
            topo_data_dict['line_data'].append(row)
        else:
            pass

    # dcline,不用电压等级过滤
    if dcline_power is None:
        topo_data_dict['dcline_data'] = dcline_data_list
    else:
        for row in dcline_data_list:
            # if row['vn_kv'] > grp_vn_kv_val:
            if True:
                # 更新线路潮流;i_line['value']
                _line_flow = np.sum(dcline_power[row['related_line_idx']['pos']], axis=0) - np.sum(
                    dcline_power[row['related_line_idx']['neg']], axis=0)
                row['value'] = round(np.abs(_line_flow) / row['limit'], 4)

                line_idx = [*row['related_line_idx']['pos'], *row['related_line_idx']['neg']]
                row['pRateDetail'] = np.abs(line_power[line_idx] / row['limitDetail']).round(4).tolist()  # 所有线路的负载率明细

                if _line_flow < -0.1:
                    # 如果潮流反向,需要将起点和终点交换,将fromName/toName,coords[0]和coords[1]互换
                    row['fromName'], row['toName'] = row['toName'], row['fromName']
                    row['from_zone'], row['to_zone'] = row['to_zone'], row['from_zone']
                    row['coords'] = [row['coords'][1], row['coords'][0]]
                topo_data_dict['dcline_data'].append(row)
            else:
                pass

    return topo_data_dict


def get_network_topo_all(case_net, result_dict: dict, time_no: int = 0, area_name: str = GLOBAL_QW_NAME):
    """
    获取全网某时刻的所有节点的关联主变、线路的潮流;  单位MW
    :param case_net:
    :param time_no:
    :param result_dict:
    :param area_name:
    :return:
    {
    "bus"：[{"name":"","index":1},],
    "line":[{"name":"","index":1,"power":100}],
    "transform":[{"name":"","index":1,"power":100]
    }
    """
    topo_data_dict = {
        "line": list(),
        "trafo": list(),
        "dcline": list()
    }

    if not case_net["line"].empty:
        net_line_data_df = pd.DataFrame({
            "index": case_net["line"].index.tolist(),
            "name": case_net["line"]["name"].values.tolist(),
            "power": result_dict["line_power"][:, time_no].round(2),
            "limit": case_net["line"]["stable_limit_mw"].values.tolist(),
        })
        topo_data_dict["line"] = net_line_data_df.to_dict(orient='records')

    if not case_net["trafo"].empty:
        net_trans_data_df = pd.DataFrame({
            "index": case_net["trafo"].index.tolist(),
            "name": case_net["trafo"]["name"].values.tolist(),
            "power": result_dict["trafo_power"][:, time_no].round(2),
            "limit": case_net["trafo"]["stable_limit_mw"].values.tolist(),
        })
        topo_data_dict["trafo"] = net_trans_data_df.to_dict(orient='records')

    if "dcline" in case_net and not case_net["dcline"].empty:
        net_dcline_data_df = pd.DataFrame({
            "index": case_net["dcline"].index.tolist(),
            "name": case_net["dcline"]["name"].values.tolist(),
            "power_from": result_dict["dcline_power_from"][:, time_no],
            "power_to": result_dict["dcline_power_to"][:, time_no],
            "limit": case_net["dcline"]["max_p_mw"].values.tolist(),
        })
        topo_data_dict["dcline"] = net_dcline_data_df.to_dict(orient='records')
    return topo_data_dict


def get_network_topo_all_with_teapresult_format(result_dict: dict, time_no: int = 0, area_name: str = GLOBAL_QW_NAME):
    """
    获取全网某时刻的所有节点的关联主变、线路的潮流;  单位MW
    输入的result_dict的结果是teap计算的结果输出，包含：索引、name	from_bus	to_bus	Pbr_max
    :param time_no:
    :param result_dict:
    :param area_name:
    :return:
    {
    "bus"：[{"name":"","index":1},],
    "line":[{"name":"","index":1,"power":100}],
    "transform":[{"name":"","index":1,"power":100]
    }
    """
    topo_data_dict = {
        "line": list(),
        "trafo": list(),
        "dcline": list()
    }

    for ele in ["line", "trafo"]:
        ele_power = f"{ele}_power"
        if ele_power in result_dict:
            net_ele_data_df = pd.DataFrame({
                "index": result_dict[ele_power].index.tolist(),
                "name": result_dict[ele_power]["name"].values.tolist(),
                "power": result_dict[ele_power].loc[:, str(time_no)].values.round(2).tolist(),
                "limit": result_dict[ele_power]["Pbr_max"].values.tolist(),
            })
            topo_data_dict[ele] = net_ele_data_df.to_dict(orient='records')

    if "dcline_power_from" in result_dict:
        net_dcline_data_df = pd.DataFrame({
            "index": result_dict["dcline_power_from"].index.tolist(),
            "name": result_dict["dcline_power_from"]["name"].values.tolist(),
            "power_from": result_dict["dcline_power_from"][:, str(time_no)].values.round(2).tolist(),
            "power_to": result_dict["dcline_power_to"][:, str(time_no)].values.round(2).tolist(),
            "limit": result_dict["dcline_power_from"]["max_p_mw"].values.tolist(),
        })
        topo_data_dict["dcline"] = net_dcline_data_df.to_dict(orient='records')
    return topo_data_dict


def get_network_topo_attri(case_info_dict: dict, result_dict: dict = dict(),
                           area_name: str = GLOBAL_QW_NAME):
    """
    计算全网拓扑的设备的线路主变的属性;
    # T站名称不显示--此处将"T站"开头的记录的名称全部置为""
    :param case_info_dict:
    :param result_dict:
    :return:
    """
    topo_data_dict = {
        'point_data': list(),
        'line_data': list(),
        'dcline_data': list()
    }
    if 'topo_info' not in case_info_dict:
        raise ValueError("算例信息分析结果中没有电网拓扑数据")

    # 电网拓扑 # 根据电压等级过滤; 后续如果需要增加需要筛选分区关联设备;
    if area_name == GLOBAL_QW_NAME:
        # grp_vn_kv_val = grp_vn_kv * 0.95
        grp_vn_kv_val = case_info_dict['config']['topo_dis_vn_kv'] * 0.95  # 全网拓扑时的过滤电压等级
    else:
        grp_vn_kv_val = min(220, case_info_dict['config']['topo_dis_vn_kv']) * 0.95  # 分区拓扑展示到220kV或者更低;
    bus_data_list = copy.deepcopy(case_info_dict['topo_info']['point_data'])
    bus_data_list = [x for x in bus_data_list if x['vn_kv'] >= grp_vn_kv_val and x['type'] == 'station']
    line_data_list = copy.deepcopy(case_info_dict['topo_info']['line_data'])
    line_data_list = [x for x in line_data_list if x['vn_kv'] >= grp_vn_kv_val]

    # 处理线路--多回线路合并;--和平均
    line_tunnel_rate, trafo_tunnel_rate = rlt_base.get_topo_elements_attri_series(result_dict, line_data_list,
                                                                                  bus_data_list)

    # 统计所有设备的重载/越限时长
    line_heavy_rate_hours, line_over_rate_hours, line_eq_over_hours = rlt_base.cal_topo_elements_loadrate_hours(
        line_tunnel_rate,
        rate_mk=[0.8,
                 1.0])
    trafo_heavy_rate_hours, trafo_over_rate_hours, trafo_eq_over_hours = rlt_base.cal_topo_elements_loadrate_hours(
        trafo_tunnel_rate,
        rate_mk=[0.8, 1.0])

    for i_bus in range(len(bus_data_list)):
        # 补充更新功率;i_point['value'][2]的值-单值; i_point['detail']
        row_i = bus_data_list[i_bus]
        row_i['heavy_hours'] = int(trafo_heavy_rate_hours[i_bus])
        row_i['over_hours'] = int(trafo_over_rate_hours[i_bus])
        row_i['over_eq_hours'] = round(float(trafo_eq_over_hours[i_bus]), 1)
        # 如果i_point的'name'以'T站'开头,则将'name'置为""
        row_i['name'] = "" if row_i['name'].startswith('T站') else row_i['name']
        topo_data_dict['point_data'].append(row_i)

    for i_line in range(len(line_data_list)):
        row_i = line_data_list[i_line]
        row_i['heavy_hours'] = int(line_heavy_rate_hours[i_line])
        row_i['over_hours'] = int(line_over_rate_hours[i_line])
        row_i['over_eq_hours'] = round(float(line_eq_over_hours[i_line]), 1)
        topo_data_dict['line_data'].append(row_i)

    dcline_data_list = copy.deepcopy(case_info_dict['topo_info']['dcline_data'])
    if len(dcline_data_list) > 0:
        dcline_tunnel_rate = rlt_base.get_topo_lines_attri_series(result_dict, dcline_data_list,
                                                                  rlt_key="dcline_power_to")
        dcline_heavy_rate_hours, dcline_over_rate_hours, dcline_eq_over_hours = rlt_base.cal_topo_elements_loadrate_hours(
            dcline_tunnel_rate,
            rate_mk=[0.8, 1.0])
        for i_dcline in range(len(dcline_data_list)):
            row_i = dcline_data_list[i_dcline]
            row_i['heavy_hours'] = int(dcline_heavy_rate_hours[i_dcline])
            row_i['over_hours'] = int(dcline_over_rate_hours[i_dcline])
            row_i['over_eq_hours'] = round(float(dcline_eq_over_hours[i_dcline]), 1)
            topo_data_dict['dcline_data'].append(row_i)

    return topo_data_dict


def get_network_topo_v2(case_info_dict: dict, cal_rlt_topo_rate: dict, time_no: int = None, result_dict: dict = dict(),
                        period: str = 'week', pf_type: str = 'mean'):
    """
    计算分区/全网拓扑的设备的日、周、月平均负载率分布;
    net: 网络拓扑;
    result_dict: teap的计算结果
    cal_rlt_topo_rate :结果预分析的拓扑负载率结果; cal_result_dict['topo_dev_rate']:---cal_rlt_topo_rate:目前statistics_dict里面只有week数据
    period: 统计分析周期;
    pf_type: 设备负载率类型: 'mean' or 'max'
    """
    if period not in ['week', 'month']:  # 'hour', 'day',
        raise ValueError("获取设备平衡负载率时间分布的period参数错误!")

    # period_flag = 'W' if period == 'week' else 'M'

    rtn_topo_data_dict = {
        'point_data': list(),
        'line_data': list()
    }

    # 电网拓扑
    topo_data_dict = get_network_topo(case_info_dict)

    bus_data_list = copy.deepcopy(topo_data_dict['point_data'])
    line_data_list = copy.deepcopy(topo_data_dict['line_data'])

    if time_no is not None:
        line_power = result_dict['line_power'][:, time_no]  # time_no时刻所有的线路潮流
        trafo_power = result_dict['trafo_power'][:, time_no]  # 主变负载
    else:
        line_power = result_dict['line_power'][:, 0]  # 0时刻所有的线路潮流
        trafo_power = result_dict['trafo_power'][:, 0]  # 主变负载

    if 'point_data' in cal_rlt_topo_rate.keys():
        for i_bus in bus_data_list:
            key_i_bus = i_bus['name']
            if key_i_bus in cal_rlt_topo_rate['point_data'].keys():
                i_bus['value_distri'] = cal_rlt_topo_rate['point_data'][key_i_bus]
            else:
                i_bus['value_distri'] = list()

            # 补充更新功率;i_point['value'][2]的值-单值; i_point['detail']
            pf_list = np.abs(np.divide(trafo_power[i_bus['related_trafo_idx']], np.array(i_bus['limit']))).tolist()
            i_bus['value'][2] = float(round(max(pf_list), 4)) if len(pf_list) > 0 else 0
            i_bus['powerflow_detail'] = pf_list
            # 如果i_point的'name'以'T站'开头,则将'name'置为""
            i_bus['name'] = "" if i_bus['name'].startswith('T站') else i_bus['name']
            rtn_topo_data_dict['point_data'].append(i_bus)
    else:
        for i_bus in bus_data_list:
            i_bus['value_distri'] = list()
            rtn_topo_data_dict['point_data'].append(i_bus)

    if 'line_data' in cal_rlt_topo_rate.keys():
        for i_line in line_data_list:
            # key_i_line = (i_line['fromName'], i_line['toName'])
            key_i_line = f"{i_line['fromName']}_{i_line['toName']}"
            if key_i_line in cal_rlt_topo_rate['line_data'].keys():
                i_line['value_distri'] = cal_rlt_topo_rate['line_data'][key_i_line]
            else:
                i_line['value_distri'] = list()

            # 更新线路潮流;i_line['value']
            _line_flow = np.sum(line_power[i_line['related_line_idx']['pos']], axis=0) - np.sum(
                line_power[i_line['related_line_idx']['neg']], axis=0)
            i_line['value'] = round(np.abs(_line_flow) / i_line['limit'], 4)
            line_idx = [*i_line['related_line_idx']['pos'], *i_line['related_line_idx']['neg']]
            i_line['pRateDetail'] = np.abs(line_power[line_idx] / i_line['limitDetail']).round(4).tolist()  # 所有线路的负载率明细
            if _line_flow < -0.1:
                # 如果潮流反向,需要将起点和终点交换,将fromName/toName,coords[0]和coords[1]互换
                i_line['fromName'], i_line['toName'] = i_line['toName'], i_line['fromName']
                i_line['from_zone'], i_line['to_zone'] = i_line['to_zone'], i_line['from_zone']
                i_line['coords'] = [i_line['coords'][1], i_line['coords'][0]]
            rtn_topo_data_dict['line_data'].append(i_line)
    else:
        for i_line in line_data_list:
            i_line['value_distri'] = list()
            rtn_topo_data_dict['line_data'].append(i_line)
    return rtn_topo_data_dict


def get_station_location(net, ele_type: str = "storage"):
    """
    获取设备厂站类型;
    net: 拓扑网络;
    ele_type: 类型 : "storage"
    :return:
    """
    if ele_type not in net.keys():
        raise ValueError("输入的ele_type类型在拓扑中未找到！")
    ele_df = net[ele_type]
    ele_cols = ele_df.columns.tolist()

    col_lon, col_lat = "lon", "lat"
    col_lon = "longitude" if "longitude" in ele_cols else col_lon
    col_lat = "latitude" if "latitude" in ele_cols else col_lat
    if col_lon not in ele_cols or col_lat not in ele_cols:
        return dict()
        # raise ValueError("输入的ele_type类型在拓扑中未找到lon/lat信息！")

    # name \ lon \ lat\max_e_mwh
    if ele_type == "storage" or "max_e_mwh" in ele_cols:
        max_p_col = "max_e_mwh"
    elif ele_type == "stogen" or "max_p_discharge_mw" in ele_cols:
        max_p_col = "max_p_discharge_mw"
    elif "max_p_mw" in ele_cols:
        max_p_col = "max_p_mw"
    else:
        max_p_col = ""

    use_ele_df = ele_df[
        (ele_df[col_lon].notna()) & (ele_df[col_lon] != '') & (ele_df[col_lat].notna()) & (ele_df[col_lat] != '')]
    if max_p_col != "":
        col_list = ["name", col_lon, col_lat, max_p_col]
        use_ele_df[max_p_col] = (use_ele_df[max_p_col].values * 0.1).round(2)  # MW(h)->万千瓦(h)
    else:
        col_list = ["name", col_lon, col_lat]

    ele_dict = {row['name']: {col: row[col] for col in col_list} for _, row in use_ele_df.iterrows()}

    return ele_dict


def get_grid_extreme_value_info(case_info_dict: dict, cal_result_dict: dict):
    """
    获取全网极值信息;
    最大负荷/时刻,最大峰谷差/日，风光最大出力及时刻(区分集中式/分散式光伏类型)
    最大供电缺口/时刻，最大调峰缺口/时刻;
    Args:
        case_info_dict:  算例信息数据dict
        cal_result_dict: 算例计算分析结果数据dict
    Returns:
        dict(), key: [val,time_index]
    """
    grid_extreme_dict = dict()

    grid_extreme_dict['max_load_p'] = case_info_dict['max_val']['load']  # 最大负荷
    grid_extreme_dict['max_wind_P'] = case_info_dict['max_val']['wind']  # 最大风电出力
    grid_extreme_dict['max_solar_p'] = case_info_dict['max_val']['solar']  # 最大光伏出力
    grid_extreme_dict['centralized_wind'] = case_info_dict['max_val']['centralized_wind']  # 最大风电出力时刻对应的统调风电出力
    grid_extreme_dict['distributed_wind'] = case_info_dict['max_val']['distributed_wind']  # 最大风电出力时刻对应的非统调风电出力
    grid_extreme_dict['centralized_solar'] = case_info_dict['max_val']['centralized_solar']  # 最大光伏出力时刻对应的统调光伏出力
    grid_extreme_dict['distributed_solar'] = case_info_dict['max_val']['distributed_solar']  # 最大光伏出力时刻对应的非统调光伏出力
    grid_extreme_dict['max_load_peak_valley'] = case_info_dict['max_val']['load_peak_valley']  # 日最大峰谷差

    # cal_result
    grid_extreme_dict['peak_slack'] = cal_result_dict['max_val']['power_slack']['peaking']  # 最大调峰缺口
    grid_extreme_dict['supply_slack'] = cal_result_dict['max_val']['power_slack']['supply_power']  # 最大供电缺口

    # 缺电时长占比：获取 powerSlack_series中>0的元素的数目
    n_hours = len(cal_result_dict['powerSupply']['series_data'])
    powerSlack_series = np.array(cal_result_dict['powerSupply']['series_data'])
    powerSlack_hours = len(powerSlack_series[powerSlack_series > 0.1])
    grid_extreme_dict['powerSlackHours'] = [round(powerSlack_hours / n_hours, 4), 0]  # [1]数据无效
    grid_extreme_dict['powerSlackHours_h'] = [int(powerSlack_hours), 0]  # 负荷缺口时长

    # 累计缺口电量--亿千瓦时：
    # 计算powerSlack_series > 0.1的元素相加和
    powerSlack_electric = (powerSlack_series[powerSlack_series > 0.1].sum() * 1e-4).round(1)
    grid_extreme_dict['powerSlack_electric'] = [powerSlack_electric, 0]  # [1]数据无效

    # 新能源总体消纳率
    newenergy_proportion = cal_result_dict['consump_rate']['city']['all']['newenergy_proportion']
    grid_extreme_dict['newEnergyConsump'] = [newenergy_proportion, 0]  # --[1]数据无效

    # # 新能源最大弃电功率、弃电(不可消纳)电量
    # grid_extreme_dict['newEnergyCurtailP'], grid_extreme_dict[
    #     'newEnergyCurtailElectric'] = rlt_base.get_newenergy_result_info(cal_result_dict)

    # 电量紧缺时段分析;

    # 计算负荷破亿时刻比例
    load_series_arr = np.array(cal_result_dict['line_curve_data']['load_data'])
    # 统计load_series_arr中值>1e4的个数
    load_over_100millon_count = len(load_series_arr[load_series_arr > 1e4])
    grid_extreme_dict['loadOver100millonRate'] = [round(load_over_100millon_count / n_hours, 4), 0]  # --[1]数据无效

    return grid_extreme_dict


def get_grid_statistic_info(result_dict: dict):
    """
    直接根据TEAP计算结果获取全网极值相关信息;
    :param case_info_dict:
    :param cal_result_dict:
    :return:
    """
    # load
    load_use = result_dict["load"].sum(axis=0)
    load_curtail = result_dict["load_curtailment"].sum(axis=0)
    n_hours = len(load_use)
    max_load_p = round(float(np.max(load_use + load_curtail)) * 0.1, 1)  # 最大原始负荷,万千瓦
    supply_slack = round(float(np.max(load_curtail)) * 0.1, 1)  # 负荷最大缺口,万千瓦
    powerSlackHours_h = int(np.sum(load_curtail > 1e-2))  # 缺电时长
    powerSlackHours = round(powerSlackHours_h / n_hours, 4)  # 缺电时长占比
    electricSlack = round(float(np.sum(load_curtail)) * 1e-5, 3)  # 负荷缺口电量,MWh->亿千瓦时
    # 日峰谷差; caldaliyload_peak_valley_max

    # 结果字典
    grid_extreme_dict = {
        'max_load_p': max_load_p,
        "supply_slack": supply_slack,
        "powerSlackHours_h": powerSlackHours_h,
        "powerSlackHours": powerSlackHours,
        "powerSlack_electric": electricSlack,
    }

    return grid_extreme_dict


def get_newenergy_result_info(result_dict: dict):
    """
    获取新能源弃电功率和弃电量
    :param result_dict:
    :return:
    """
    new_curtail_dict = dict()
    new_curtail_dict['maxCurtailP'], new_curtail_dict[
        'curtailElectric'] = rlt_base.get_newenergy_curtail_info(result_dict)
    return result_dict


def get_power_balance_data_curve(cal_result_dict: dict, is_all: bool = True, time_list: list = None,
                                 area_name: str = GLOBAL_QW_NAME, area_details_dict: dict = dict()):
    """
    FUNC:获取用于页面展示小时粒度的功率平衡曲线;获取结果出力时序(任意时段，时点，全局);
    Args:
        cal_result_dict:case的预处理结果dict
        is_all： 是否获取全部时刻数据;
        time_list：list 如果非全部时刻的数据，传入所需时刻索引的list
        area_name: 分区名称;
        area_details_dict: 分区关联设备索引
    """
    rtn_series_data = dict()
    if area_name == GLOBAL_QW_NAME:
        if 'line_curve_data' not in cal_result_dict:
            raise ValueError("结果分析中没有结果时序数据")
        if is_all or time_list is None:
            # 获取全时序的数据;
            rtn_series_data = cal_result_dict['line_curve_data']
        else:
            for i_key in cal_result_dict['line_curve_data']:
                rtn_series_data[i_key] = (
                    np.array(cal_result_dict['line_curve_data'][i_key])[time_list].round(1)).tolist()
    else:
        # 分区现统计;
        # rtn_series_data = get_power_balance_data(net, all_result_dict, config, area_name=GLOBAL_QW_NAME, area_details_dict)
        if area_name not in area_details_dict:
            raise ValueError(f"zone:{area_name} not in area_details_dict.keys()!")
    return rtn_series_data


def get_zone_balance_data_curve(net, config, result_dict, area_name, area_details_dict: dict = dict(),
                                is_all: bool = True, time_list: list = None, zone_psm_dict: dict = dict()):
    """
    获取分区的时序曲线数据
    area_name : "全省": 全网信息;
    zone_psm_dict: 分区供电裕度时序字典; indicator_rlt_dict["indicator_data"]
    """
    zone_line_curve_dict, _ = get_power_balance_data(net, result_dict, config, area_name=area_name,
                                                     area_details_dict=area_details_dict)
    if not is_all and time_list is not None:
        for key, val in zone_line_curve_dict.items():
            zone_line_curve_dict[key] = (
                np.array(val)[time_list].round(1)).tolist()
    # 如果裕度信息不为空也加入进来;
    if zone_psm_dict and area_name in zone_psm_dict.keys():
        zone_psm = zone_psm_dict[area_name]["power_supply_margin"]
        zone_line_curve_dict["psm"] = np.array(zone_psm)[time_list].round(1).tolist()
    return zone_line_curve_dict


def get_zone_loadinsufficiency_data(result_dict, area_name: str = GLOBAL_QW_NAME,
                                    area_details_dict: dict = dict(), bload_series: bool = False,
                                    bzone_detail: bool = False,
                                    bdetail: bool = False,
                                    indicator_rlt_dict: dict = dict(),
                                    bpsm: bool = False,
                                    ):
    """
    获取所有分区的负荷最大缺口及总缺负荷时长;缺负荷明细;
    result_dict: teap计算结果:
    area_name: 分区名称， "全省"或分区名
    area_details_dict: 分区设备索引
    bload_series: bool,是否需要负荷时序
    bzone_detail：bool, 全网时是否需要分区的负荷削减情况
    bdetail：bool,是否需要负荷削减的具体明细
    indicator_rlt_dict: 分区裕度指标字典;
    bpsm: 是否根据分区裕度来进行供电缺口的统计分析
    return:dict = {
    'load_cutailment':{
         'time_no': 负荷最大缺口时刻，
         'max_val'：负荷最大缺口值，
         'hours'：负荷缺口时长，
         'details'：负荷缺口明细：{'time_no'：时刻，'values':值}
    },
    'load_series': 消纳负荷时序，
    'load_cutailment_series': 削减负荷时序，
    'zone_load_curtail':分区负荷缺口信息{}
    }
    """
    load_data_dict = dict()

    # 获取分区的负荷时序和负荷削减时序;
    def get_load_cutail_info(zone_load_cutail_series, series_detail: bool = False):
        load_curtail = dict()
        zn_load_cutail_idxs = np.where(zone_load_cutail_series > 0.1)
        zn_max_load_idx = np.argmax(zone_load_cutail_series)
        load_curtail['time_no'] = int(zn_max_load_idx)  # 负荷最大缺口时刻
        load_curtail['max_val'] = max(0, round(zone_load_cutail_series[zn_max_load_idx] * 0.1, 1))  # 负荷最大缺口值--万千瓦
        load_curtail['hours'] = len(zn_load_cutail_idxs[0].tolist())  # 负荷缺口小时时长
        load_curtail['details'] = dict()
        if series_detail:
            # 获取zone_load_cutail_series中>0.1的所有值的位置索引及值
            load_curtail['details'] = {
                'time_no': zn_load_cutail_idxs[0].tolist(),
                'values': (zone_load_cutail_series[zn_load_cutail_idxs] * 0.1).round(1).tolist()
            }
        return load_curtail

    # 分区负荷索引
    area_load_idxs = slice(None) if area_name == GLOBAL_QW_NAME else area_details_dict[area_name]['load']
    zone_load_cutail_series = result_dict['load_curtailment'][area_load_idxs, :].sum(axis=0)
    if not bpsm:
        zn_data_dict = get_load_cutail_info(zone_load_cutail_series, series_detail=bdetail)
    else:
        # 直接用裕度
        zone_psm_series = np.array(indicator_rlt_dict["indicator_data"][area_name]["power_supply_margin"])
        zn_data_dict = get_load_cutail_info(zone_psm_series * (-1 * 10), series_detail=bdetail)
    load_data_dict['load_cutailment'] = zn_data_dict

    if bload_series:
        load_data_dict['load_series'] = (result_dict['load'][area_load_idxs, :].sum(axis=0) * 0.1).round(1).tolist()
        load_data_dict['load_cutailment_series'] = (zone_load_cutail_series * 0.1).round(1).tolist()
    else:
        load_data_dict['load_series'] = list()
        load_data_dict['load_cutailment_series'] = list()

    # 分区负荷的缺口情况(根据分区供电裕度或者根据分区负荷削减来判断)
    load_data_dict['zone_load_curtail'] = dict()
    if bzone_detail and area_name == GLOBAL_QW_NAME:
        # 全网时--计算所有分区的负荷缺口情况;
        zone_load_curtail = dict()
        area_names = list(area_details_dict.keys())
        if not bpsm:
            for zone_i in area_names:
                zone_load_idxs = area_details_dict[zone_i]['load']
                zn_load_cutail_series = result_dict['load_curtailment'][zone_load_idxs, :].sum(axis=0)
                zone_load_curtail[zone_i] = get_load_cutail_info(zn_load_cutail_series, series_detail=False)
            load_data_dict['zone_load_curtail'] = zone_load_curtail
        else:
            for zone_i in area_names:
                zone_psm_series = np.array(indicator_rlt_dict["indicator_data"][zone_i]["power_supply_margin"])
                zone_load_curtail[zone_i] = get_load_cutail_info(zone_psm_series * (-1 * 10), series_detail=False)
            load_data_dict['zone_load_curtail'] = zone_load_curtail
    return load_data_dict


def get_newenergy_curtailment_data(config, cal_result_dict):
    """
    获取全网/分区的新能源弃电明细(弃电时刻、弃电电力(wind/solar)、弃电类型(弃风/弃光/弃风弃光)、弃电原因(调峰/网架/调峰+网架))
    cal_result_dict： teap结果分析的dict---case_rlt_cal['cal_result']
    area_name：分区
    area_details_dict： 分区关联设备索引;
    """
    if 'new_cutail_reason' in cal_result_dict:
        return cal_result_dict['new_cutail_reason']

    # 先计算新能源的弃电明细;
    if 'line_curve_data' not in cal_result_dict:
        raise ValueError("结果分析数据中缺少'line_curve_data'数据!")
    if 'peaking' not in cal_result_dict:
        raise ValueError("结果分析数据中缺少'peaking'数据!")

    peaking_arr = np.array(cal_result_dict['peaking'])  # 调峰缺口时序数据

    series_data = cal_result_dict['line_curve_data']  # 时序数据--单位万千瓦;

    wind_curtail = np.zeros(config['sum_snap'])  # 时序数目
    if 'wind_curtailment' in series_data:
        wind_curtail = np.array(series_data['wind_curtailment'])  # 风光弃电电力时序数据
    solar_curtail = np.zeros(config['sum_snap'])
    if 'solar_curtailment' in series_data:
        solar_curtail = np.array(series_data['solar_curtailment'])
    load_init_data = np.array(series_data['load_data'])  # 原始负荷时序

    new_curtail_data = func_newcutailment_reason_ana(config, wind_curtail, solar_curtail, load_init_data, peaking_arr)
    return new_curtail_data


def get_allgen_electric_hours(cal_result_dict: dict, freq_h: str = 'season', config: dict = dict(),
                              area_name: str = GLOBAL_QW_NAME):
    """
    FUNC: 获取所有类型机组的年总发电量/利用小时/风光新能源消纳率信息;---对应'electricity'# 并组织成需要的json格式;
    Args:
        cal_result_dict:case的结果预处理结果dict
        freq_h: web页面上的新能源/负荷的电量/利用小时的默认展示间隔'month':月;'season':季度
        config: case_info_dict中的config信息
        area_name: 全网/分区结果查询
    returns:
        dict:
        key:electricity:所有类型能源的发电量;using_hours:利用小时;consump_rate:新能源消纳率
    """
    data_dict = {
        'electricity': dict(),  # 所有类型能源的总发电量;
        'using_hours': dict(),  # 所有类型能源的利用小时
        'consump_rate': dict(),  # 新能源消纳率  {"all":{},"衡阳"：{}},all:全网
        'electricity_list': dict(),  # 月/季度发电量数据[1,2,3,4]
        'using_hours_list': dict(),  # 月/季度,所有类型能源的利用小时
    }
    if 'electricity' not in cal_result_dict:
        raise ValueError("结果分析中缺少电量数据")
    else:
        if area_name not in cal_result_dict['electricity']:
            raise ValueError(f"电量数据结果中缺少:{area_name}的信息")
        data_dict['electricity'] = cal_result_dict['electricity'][area_name]  # 电量支持按分区进行获取/切换;

    if 'using_hours' not in cal_result_dict:
        raise ValueError("结果分析中缺少利用小时数据")
    else:
        data_dict['using_hours'] = cal_result_dict['using_hours']
        wind_cap = cal_result_dict['using_hours_cap']['wind_cap'] * cof_power  # 日电量是万千瓦,装机也转为万千瓦
        solar_cap = cal_result_dict['using_hours_cap']['solar_cap'] * cof_power
        centralized_wind_cap = cal_result_dict['using_hours_cap']['centralized_wind_cap'] * cof_power
        distributed_wind_cap = cal_result_dict['using_hours_cap']['distributed_wind_cap'] * cof_power
        centralized_solar_cap = cal_result_dict['using_hours_cap']['centralized_solar_cap'] * cof_power
        distributed_solar_cap = cal_result_dict['using_hours_cap']['distributed_solar_cap'] * cof_power

    if 'consump_rate' not in cal_result_dict:
        raise ValueError("结果分析中缺少新能源消纳率数据")
    else:
        data_dict['consump_rate'] = cal_result_dict['consump_rate']['city']

    if 'daily_electricity' not in cal_result_dict:
        raise ValueError("结果分析中缺少新能源/负荷的日电量信息数据")
    else:
        dayily_data_dict = cal_result_dict['daily_electricity']
        if freq_h == 'month' or freq_h == 'season':
            case_start_time = config['start_time']
            num_snaps = config['sum_snap']
            _tm_df = get_time_range(case_start_time, num_snaps, freq='h')
            _tm_df = _tm_df.resample('D').mean()
            if 'wind_output' in dayily_data_dict:
                _tm_df['wind'] = dayily_data_dict['wind_output']  # 风电
            else:
                _tm_df['wind'] = 0
            if 'solar_output' in dayily_data_dict:
                _tm_df['solar'] = dayily_data_dict['solar_output']  # 光伏
            else:
                _tm_df['solar'] = 0
            if 'pump_discharge' in dayily_data_dict:
                _tm_df['pump_discharge'] = dayily_data_dict['pump_discharge']  # 抽蓄放电
            else:
                _tm_df['pump_discharge'] = 0
            if 'pump_charge' in dayily_data_dict:
                _tm_df['pump_charge'] = dayily_data_dict['pump_charge']  # 抽蓄充电
            else:
                _tm_df['pump_charge'] = 0
            if 'battery_discharge' in dayily_data_dict:
                _tm_df['battery_discharge'] = dayily_data_dict['battery_discharge']  # 储能放电
            else:
                _tm_df['battery_discharge'] = 0
            if 'battery_charge' in dayily_data_dict:
                _tm_df['battery_charge'] = dayily_data_dict['battery_charge']  # 储能充电
            else:
                _tm_df['battery_charge'] = 0
            if 'centralized_wind' in dayily_data_dict:
                _tm_df['centralized_wind'] = dayily_data_dict['centralized_wind']
            else:
                _tm_df['centralized_wind'] = 0
            if 'distributed_wind' in dayily_data_dict:
                _tm_df['distributed_wind'] = dayily_data_dict['distributed_wind']
            else:
                _tm_df['distributed_wind'] = 0
            if 'centralized_solar' in dayily_data_dict:
                _tm_df['centralized_solar'] = dayily_data_dict['centralized_solar']
            else:
                _tm_df['centralized_solar'] = 0
            if 'distributed_solar' in dayily_data_dict:
                _tm_df['distributed_solar'] = dayily_data_dict['distributed_solar']
            else:
                _tm_df['distributed_solar'] = 0
            _tm_df['load'] = dayily_data_dict['load']  # 负荷;
            _tm_df['month'] = _tm_df.index.month  # 赋值月度
            if freq_h == 'month':
                data_dict['electricity_list']['load'] = [0] * 12
                data_dict['using_hours_list']['wind_output'] = [0] * 12
                data_dict['using_hours_list']['solar_output'] = [0] * 12
                data_dict['using_hours_list']['centralized_wind'] = [0] * 12  # 集中式风电
                data_dict['using_hours_list']['distributed_wind'] = [0] * 12  # 分布式风电
                data_dict['using_hours_list']['centralized_solar'] = [0] * 12  # 集中式光伏
                data_dict['using_hours_list']['distributed_solar'] = [0] * 12  # 分布式光伏
                data_dict['using_hours_list']['pump_discharge'] = [0] * 12
                data_dict['using_hours_list']['pump_charge'] = [0] * 12
                for i_m in range(1, 13, 1):
                    _tm_i_df = _tm_df[_tm_df['month'] == i_m]  # 循环12个月:
                    data_dict['using_hours_list']['wind_output'][i_m - 1] = (
                            _tm_i_df['wind'].values.sum() / wind_cap).round(1) if wind_cap > 0.01 else 0
                    data_dict['using_hours_list']['centralized_wind'][i_m - 1] = (
                            _tm_i_df['centralized_wind'].values.sum() / centralized_wind_cap).round(
                        1) if centralized_wind_cap > 0.01 else 0
                    data_dict['using_hours_list']['distributed_wind'][i_m - 1] = (
                            _tm_i_df['distributed_wind'].values.sum() / distributed_wind_cap).round(
                        1) if distributed_wind_cap > 0.01 else 0
                    data_dict['using_hours_list']['solar_output'][i_m - 1] = (
                            _tm_i_df['solar'].values.sum() / solar_cap).round(1) if solar_cap > 0.01 else 0
                    data_dict['using_hours_list']['centralized_solar'][i_m - 1] = (
                            _tm_i_df['centralized_solar'].values.sum() / centralized_solar_cap).round(
                        1) if centralized_solar_cap > 0.01 else 0
                    data_dict['using_hours_list']['distributed_solar'][i_m - 1] = (
                            _tm_i_df['distributed_solar'].values.sum() / distributed_solar_cap).round(
                        1) if distributed_solar_cap > 0.01 else 0
                    data_dict['using_hours_list']['pump_discharge'][i_m - 1] = (
                        _tm_i_df['pump_discharge'].values.sum()).round(1)
                    data_dict['using_hours_list']['pump_charge'][i_m - 1] = (
                        _tm_i_df['pump_charge'].values.sum()).round(1)
                    data_dict['electricity_list']['load'][i_m - 1] = (_tm_i_df['load'].values.sum() * cof_energy).round(
                        1)
                    data_dict['using_hours_list']['battery_discharge'][i_m - 1] = (
                        _tm_i_df['battery_discharge'].values.sum()).round(1)
                    data_dict['using_hours_list']['battery_charge'][i_m - 1] = (
                        _tm_i_df['battery_charge'].values.sum()).round(1)
            else:
                data_dict['electricity_list']['load'] = [0] * 4
                data_dict['using_hours_list']['wind_output'] = [0] * 4
                data_dict['using_hours_list']['solar_output'] = [0] * 4
                data_dict['using_hours_list']['centralized_wind'] = [0] * 4  # 集中式风电
                data_dict['using_hours_list']['distributed_wind'] = [0] * 4  # 分布式风电
                data_dict['using_hours_list']['centralized_solar'] = [0] * 4  # 集中式光伏
                data_dict['using_hours_list']['distributed_solar'] = [0] * 4  # 分布式光伏
                data_dict['using_hours_list']['pump_discharge'] = [0] * 4  # 抽蓄
                data_dict['using_hours_list']['pump_charge'] = [0] * 4  # 抽蓄
                data_dict['using_hours_list']['battery_discharge'] = [0] * 4  # 储能
                data_dict['using_hours_list']['battery_charge'] = [0] * 4  # 储能
                season_dict = {'1': spring_month, '2': summer_month, '3': autumn_month, '4': winter_month}
                for i_m in range(1, 5, 1):
                    _tm_i_df = _tm_df[_tm_df['month'].isin(season_dict[str(i_m)])]  # 循环12个月:
                    data_dict['using_hours_list']['wind_output'][i_m - 1] = (
                            _tm_i_df['wind'].values.sum() / wind_cap).round(2) if wind_cap > 0.01 else 0
                    data_dict['using_hours_list']['centralized_wind'][i_m - 1] = (
                            _tm_i_df['centralized_wind'].values.sum() / centralized_wind_cap).round(
                        2) if centralized_wind_cap > 0.01 else 0
                    data_dict['using_hours_list']['distributed_wind'][i_m - 1] = (
                            _tm_i_df['distributed_wind'].values.sum() / distributed_wind_cap).round(
                        2) if distributed_wind_cap > 0.01 else 0
                    data_dict['using_hours_list']['solar_output'][i_m - 1] = (
                            _tm_i_df['solar'].values.sum() / solar_cap).round(2) if solar_cap > 0.01 else 0
                    data_dict['using_hours_list']['centralized_solar'][i_m - 1] = (
                            _tm_i_df['centralized_solar'].values.sum() / centralized_solar_cap).round(
                        2) if centralized_solar_cap > 0.01 else 0
                    data_dict['using_hours_list']['distributed_solar'][i_m - 1] = (
                            _tm_i_df['distributed_solar'].values.sum() / distributed_solar_cap).round(
                        2) if distributed_solar_cap > 0.01 else 0
                    data_dict['using_hours_list']['pump_discharge'][i_m - 1] = (
                        _tm_i_df['pump_discharge'].values.sum()).round(2)
                    data_dict['using_hours_list']['pump_charge'][i_m - 1] = (
                        _tm_i_df['pump_charge'].values.sum()).round(2)
                    data_dict['electricity_list']['load'][i_m - 1] = (_tm_i_df['load'].values.sum() * cof_energy).round(
                        2)
                    data_dict['using_hours_list']['battery_discharge'][i_m - 1] = (
                        _tm_i_df['battery_discharge'].values.sum()).round(2)
                    data_dict['using_hours_list']['battery_charge'][i_m - 1] = (
                        _tm_i_df['battery_charge'].values.sum()).round(2)
        else:
            raise ValueError("暂不支持除月/季度以外的电量和利用小时统计")

    return data_dict


def get_allgen_electric_hours_v2(cal_result_dict: dict, freq_h: str = 'season', config: dict = dict(),
                                 area_name: str = GLOBAL_QW_NAME):
    """
    FUNC: 获取所有类型机组的年总发电量/利用小时/风光新能源消纳率信息;---对应'electricity'# 并组织成需要的json格式;
    Args:
        cal_result_dict:case的结果预处理结果dict
        freq_h: web页面上的新能源/负荷的电量/利用小时的默认展示间隔'month':月;'season':季度
        config: case_info_dict中的config信息
        area_name: 全网/分区结果查询
    returns:
        dict:
        key:electricity:所有类型能源的发电量;using_hours:利用小时;consump_rate:新能源消纳率
    """
    data_dict = {
        'electricity': dict(),  # 所有类型能源的总发电量;
        'using_hours': dict(),  # 所有类型能源的利用小时
        'consump_rate': dict(),  # 新能源消纳率  {"all":{},"衡阳"：{}},all:全网
        'electricity_list': dict(),  # 月/季度发电量数据[1,2,3,4]
        'using_hours_list': dict(),  # 月/季度,所有类型能源的利用小时
    }
    if 'electricity' not in cal_result_dict:
        raise ValueError("结果分析中缺少电量数据")
    else:
        if area_name not in cal_result_dict['electricity']:
            raise ValueError(f"电量数据结果中缺少:{area_name}的信息")
        data_dict['electricity'] = cal_result_dict['electricity'][area_name]  # 电量支持按分区进行获取/切换;

    if 'using_hours' not in cal_result_dict:
        raise ValueError("结果分析中缺少利用小时数据")
    else:
        data_dict['using_hours'] = cal_result_dict['using_hours']
        wind_cap = cal_result_dict['using_hours_cap']['wind_cap'] * cof_power  # 日电量是万千瓦,装机也转为万千瓦
        solar_cap = cal_result_dict['using_hours_cap']['solar_cap'] * cof_power
        centralized_wind_cap = cal_result_dict['using_hours_cap']['centralized_wind_cap'] * cof_power
        distributed_wind_cap = cal_result_dict['using_hours_cap']['distributed_wind_cap'] * cof_power
        centralized_solar_cap = cal_result_dict['using_hours_cap']['centralized_solar_cap'] * cof_power
        distributed_solar_cap = cal_result_dict['using_hours_cap']['distributed_solar_cap'] * cof_power

    if 'consump_rate' not in cal_result_dict:
        raise ValueError("结果分析中缺少新能源消纳率数据")
    else:
        data_dict['consump_rate'] = cal_result_dict['consump_rate']['city']

    if 'daily_electricity' not in cal_result_dict:
        raise ValueError("结果分析中缺少新能源/负荷的日电量信息数据")
    else:
        dayily_data_dict = cal_result_dict['daily_electricity']
        if freq_h == 'month' or freq_h == 'season':
            case_start_time = config['start_time']
            num_snaps = config['sum_snap']
            _tm_df = get_time_range(case_start_time, num_snaps, freq='h')
            _tm_df = _tm_df.resample('D').mean()
            if 'wind_output' in dayily_data_dict:
                _tm_df['wind'] = dayily_data_dict['wind_output']  # 风电
            else:
                _tm_df['wind'] = 0
            if 'solar_output' in dayily_data_dict:
                _tm_df['solar'] = dayily_data_dict['solar_output']  # 光伏
            else:
                _tm_df['solar'] = 0
            if 'pump_discharge' in dayily_data_dict:
                _tm_df['pump_discharge'] = dayily_data_dict['pump_discharge']  # 抽蓄放电
            else:
                _tm_df['pump_discharge'] = 0
            if 'pump_charge' in dayily_data_dict:
                _tm_df['pump_charge'] = dayily_data_dict['pump_charge']  # 抽蓄充电
            else:
                _tm_df['pump_charge'] = 0
            if 'battery_discharge' in dayily_data_dict:
                _tm_df['battery_discharge'] = dayily_data_dict['battery_discharge']  # 储能放电
            else:
                _tm_df['battery_discharge'] = 0
            if 'battery_charge' in dayily_data_dict:
                _tm_df['battery_charge'] = dayily_data_dict['battery_charge']  # 储能充电
            else:
                _tm_df['battery_charge'] = 0
            if 'centralized_wind' in dayily_data_dict:
                _tm_df['centralized_wind'] = dayily_data_dict['centralized_wind']
            else:
                _tm_df['centralized_wind'] = 0
            if 'distributed_wind' in dayily_data_dict:
                _tm_df['distributed_wind'] = dayily_data_dict['distributed_wind']
            else:
                _tm_df['distributed_wind'] = 0
            if 'centralized_solar' in dayily_data_dict:
                _tm_df['centralized_solar'] = dayily_data_dict['centralized_solar']
            else:
                _tm_df['centralized_solar'] = 0
            if 'distributed_solar' in dayily_data_dict:
                _tm_df['distributed_solar'] = dayily_data_dict['distributed_solar']
            else:
                _tm_df['distributed_solar'] = 0
            _tm_df['load'] = dayily_data_dict['load']  # 负荷;
            _tm_df['month'] = _tm_df.index.month  # 赋值月度
            if freq_h == 'month':
                data_dict['electricity_list']['load'] = [0] * 12
                data_dict['using_hours_list']['wind_output'] = [0] * 12
                data_dict['using_hours_list']['solar_output'] = [0] * 12
                data_dict['using_hours_list']['centralized_wind'] = [0] * 12  # 集中式风电
                data_dict['using_hours_list']['distributed_wind'] = [0] * 12  # 分布式风电
                data_dict['using_hours_list']['centralized_solar'] = [0] * 12  # 集中式光伏
                data_dict['using_hours_list']['distributed_solar'] = [0] * 12  # 分布式光伏
                data_dict['using_hours_list']['pump_discharge'] = [0] * 12
                data_dict['using_hours_list']['pump_charge'] = [0] * 12
                for i_m in range(1, 13, 1):
                    _tm_i_df = _tm_df[_tm_df['month'] == i_m]  # 循环12个月:
                    data_dict['using_hours_list']['wind_output'][i_m - 1] = (
                            _tm_i_df['wind'].values.sum() / wind_cap).round(1) if wind_cap > 0.01 else 0
                    data_dict['using_hours_list']['centralized_wind'][i_m - 1] = (
                            _tm_i_df['centralized_wind'].values.sum() / centralized_wind_cap).round(
                        1) if centralized_wind_cap > 0.01 else 0
                    data_dict['using_hours_list']['distributed_wind'][i_m - 1] = (
                            _tm_i_df['distributed_wind'].values.sum() / distributed_wind_cap).round(
                        1) if distributed_wind_cap > 0.01 else 0
                    data_dict['using_hours_list']['solar_output'][i_m - 1] = (
                            _tm_i_df['solar'].values.sum() / solar_cap).round(1) if solar_cap > 0.01 else 0
                    data_dict['using_hours_list']['centralized_solar'][i_m - 1] = (
                            _tm_i_df['centralized_solar'].values.sum() / centralized_solar_cap).round(
                        1) if centralized_solar_cap > 0.01 else 0
                    data_dict['using_hours_list']['distributed_solar'][i_m - 1] = (
                            _tm_i_df['distributed_solar'].values.sum() / distributed_solar_cap).round(
                        1) if distributed_solar_cap > 0.01 else 0
                    data_dict['using_hours_list']['pump_discharge'][i_m - 1] = (
                        _tm_i_df['pump_discharge'].values.sum()).round(1)
                    data_dict['using_hours_list']['pump_charge'][i_m - 1] = (
                        _tm_i_df['pump_charge'].values.sum()).round(1)
                    data_dict['electricity_list']['load'][i_m - 1] = (_tm_i_df['load'].values.sum() * cof_energy).round(
                        1)
                    data_dict['using_hours_list']['battery_discharge'][i_m - 1] = (
                        _tm_i_df['battery_discharge'].values.sum()).round(1)
                    data_dict['using_hours_list']['battery_charge'][i_m - 1] = (
                        _tm_i_df['battery_charge'].values.sum()).round(1)
            else:
                data_dict['electricity_list']['load'] = [0] * 4
                data_dict['using_hours_list']['wind_output'] = [0] * 4
                data_dict['using_hours_list']['solar_output'] = [0] * 4
                data_dict['using_hours_list']['centralized_wind'] = [0] * 4  # 集中式风电
                data_dict['using_hours_list']['distributed_wind'] = [0] * 4  # 分布式风电
                data_dict['using_hours_list']['centralized_solar'] = [0] * 4  # 集中式光伏
                data_dict['using_hours_list']['distributed_solar'] = [0] * 4  # 分布式光伏
                data_dict['using_hours_list']['pump_discharge'] = [0] * 4  # 抽蓄
                data_dict['using_hours_list']['pump_charge'] = [0] * 4  # 抽蓄
                data_dict['using_hours_list']['battery_discharge'] = [0] * 4  # 储能
                data_dict['using_hours_list']['battery_charge'] = [0] * 4  # 储能
                season_dict = {'1': spring_month, '2': summer_month, '3': autumn_month, '4': winter_month}
                for i_m in range(1, 5, 1):
                    _tm_i_df = _tm_df[_tm_df['month'].isin(season_dict[str(i_m)])]  # 循环12个月:
                    data_dict['using_hours_list']['wind_output'][i_m - 1] = (
                            _tm_i_df['wind'].values.sum() / wind_cap).round(2) if wind_cap > 0.01 else 0
                    data_dict['using_hours_list']['centralized_wind'][i_m - 1] = (
                            _tm_i_df['centralized_wind'].values.sum() / centralized_wind_cap).round(
                        2) if centralized_wind_cap > 0.01 else 0
                    data_dict['using_hours_list']['distributed_wind'][i_m - 1] = (
                            _tm_i_df['distributed_wind'].values.sum() / distributed_wind_cap).round(
                        2) if distributed_wind_cap > 0.01 else 0
                    data_dict['using_hours_list']['solar_output'][i_m - 1] = (
                            _tm_i_df['solar'].values.sum() / solar_cap).round(2) if solar_cap > 0.01 else 0
                    data_dict['using_hours_list']['centralized_solar'][i_m - 1] = (
                            _tm_i_df['centralized_solar'].values.sum() / centralized_solar_cap).round(
                        2) if centralized_solar_cap > 0.01 else 0
                    data_dict['using_hours_list']['distributed_solar'][i_m - 1] = (
                            _tm_i_df['distributed_solar'].values.sum() / distributed_solar_cap).round(
                        2) if distributed_solar_cap > 0.01 else 0
                    data_dict['using_hours_list']['pump_discharge'][i_m - 1] = (
                        _tm_i_df['pump_discharge'].values.sum()).round(2)
                    data_dict['using_hours_list']['pump_charge'][i_m - 1] = (
                        _tm_i_df['pump_charge'].values.sum()).round(2)
                    data_dict['electricity_list']['load'][i_m - 1] = (_tm_i_df['load'].values.sum() * cof_energy).round(
                        2)
                    data_dict['using_hours_list']['battery_discharge'][i_m - 1] = (
                        _tm_i_df['battery_discharge'].values.sum()).round(2)
                    data_dict['using_hours_list']['battery_charge'][i_m - 1] = (
                        _tm_i_df['battery_charge'].values.sum()).round(2)
        else:
            raise ValueError("暂不支持除月/季度以外的电量和利用小时统计")

    return data_dict


def get_electric_distribute(cal_result_dict: dict):
    """
    获取新能源小时/月度分布的电量/弃电量数据
    cal_result_dict:case的结果预处理结果dict
    Returns:
        dict
    """
    e_distri_dict = {
        'hour_electric_distri': dict(),
        'month_electric_distri': dict(),
    }
    if 'hour_electric_distri' in cal_result_dict:
        e_distri_dict['hour_electric_distri'] = cal_result_dict['hour_electric_distri']

    if 'month_electric_distri' in cal_result_dict:
        e_distri_dict['month_electric_distri'] = cal_result_dict['month_electric_distri']
    return e_distri_dict


def get_power_supply_distribute(cal_result_dict: dict):
    """
    获取供电能力: 正备用不足小时/供电缺口最大值/负备用不足小时/调峰缺口最大值的小时/月分布数据
    cal_result_dict:case的结果预处理结果dict
    Returns:
        dict
    """
    data_dict = {
        'hour_distri': dict(),
        'month_balance_distri': dict()
    }
    if 'hour_distri' in cal_result_dict:
        data_dict['hour_distri'] = cal_result_dict['hour_distri']  # 供电/调峰缺口--小时分布

    if 'month_balance_distri' in cal_result_dict:
        data_dict['month_balance_distri'] = cal_result_dict['month_balance_distri']  # 供电/调峰缺口--月度分布

    return data_dict


def get_data_hours_distribute(config: dict, result_dict: dict, value_type: list = list(), func_type="num_count"):
    """
    获取数据的小时特性分布;  例如:
    config: 算例配置参数
    result_dict: TEAP计算结果
    value_type : 结果类型: "load_curtailment"
    func_type:"num_count": 统计个数小时数据,"sum_count";统计和,"max_count"：统计最大值;
    :return:
    """
    snaps_col = "data"
    _tm_df = get_time_range(config['start_time'], config['sum_snap'], freq='h', snaps_col=snaps_col)
    for key in value_type:
        if key in result_dict.keys():
            _tm_df[key] = result_dict[key].sum(axis=0).round(1)
    _tm_df.drop(columns=[snaps_col], inplace=True)

    data_dict = data_df_distribute_base(_tm_df, distri_type="hour", func_type=func_type)
    return data_dict


def get_zone_device_relay_inf_info(net, result_dict: dict, area_name: str = GLOBAL_QW_NAME,
                                   area_details_dict: dict = dict(),
                                   inf_ele_type: str = 'trafo', vn_kv: float = 500):
    """
    获取分区设备的关联断面(interface)信息;  只取第一个关联主变的节点位置;
    :param net:  拓扑
    :param result_dict:  teap计算结果
    :param area_name:
    :param inf_ele_type:
    :return:
    dict = {
    "point_inf_data"：[
    {"inf_name": "", "related_trafo_idx":[], "location":[117.36748, 34.11342],"max_p_rate":1.05,"limit":160}
    ]
    }
    """
    rtn_data_dict = {
        "point_inf_data": {}
    }

    if inf_ele_type != "trafo":
        return rtn_data_dict

    if area_name in [GLOBAL_QW_NAME, None]:
        zone_ele = net[inf_ele_type]
    else:
        zone_ele = net[inf_ele_type].loc[area_details_dict[area_name]["trafo"]]

    # 获取主变关联的所有主变断面索引不为空的记录
    zone_ele = zone_ele[zone_ele["interface"].notna()]
    # group by
    for i_grp, grp_df in zone_ele.groupby("interface"):
        infi_info = {
            "inf_name": net["interface"].loc[i_grp, "name"],
            "limit": net["interface"].loc[i_grp, "max_p_mw"] * 0.1,
            "related_trafo_idx": grp_df.index.tolist(),
            "max_p_rate": result_dict["interface_power"].max().round(4) if "interface_power" in result_dict else 0.0
        }
        trafo_hv_bus = grp_df.iloc[0]["hv_bus"]
        infi_station = net["bus"].loc[trafo_hv_bus, "dispname"]
        infi_info["location"] = [net["bus"].loc[trafo_hv_bus, "lon"], net["bus"].loc[trafo_hv_bus, "lat"]]
        if infi_station not in rtn_data_dict["point_inf_data"]:
            rtn_data_dict["point_inf_data"][infi_station] = [infi_info]
        else:
            rtn_data_dict["point_inf_data"][infi_station].append(infi_info)
    return rtn_data_dict


def get_all_device_loadratio_info(cal_result_dict: dict, area_name: str = GLOBAL_QW_NAME, area_details: dict = dict(),
                                  case_inf_collection_dict: dict = dict(), inf_ele_type: str = 'trafo', net=None,
                                  area_details_dict: dict = dict()):
    """
    FUNC:获取全年平均重载设备(主变trafo/线路line/断面interface)列表;
    Args:
        cal_result_dict: 步骤2分析结果dict
        config:配置参数;--电压等级的过滤在统计分析的时候就进行操作;
        # select_vn_kv = config['device_dis_vn_kv'] if 'device_dis_vn_kv' in config else 500
        area_name: 分区;
        area_details: 分区关联设备索引;
        case_inf_collection_dict: 算例的通道/断面关联关系;--case_info_dict["inf_collection"]
        inf_ele_type: 断面的类型，"trafo"/"line"/""
        net: 电网拓扑信息;
    Returns:
        dict()
    """
    if 'device_loadratio' not in cal_result_dict:
        raise ValueError("结果分析中缺少设备重载过载信息的数据")
    else:
        cal_result_ratio = cal_result_dict['device_loadratio']
        # 筛选目标需要的电压等级以上的设备
        ratio_vn_kv_valid_dict = ddeal.check_ratio_vn_kv(cal_result_ratio)

        # 通道(2级断面)
        channel_dict = get_inf_collection_ratio_info(cal_result_ratio,
                                                     case_inf_collection_dict)

        if area_name == GLOBAL_QW_NAME:
            ratio_vn_kv_valid_dict['channel'] = channel_dict
            # interface筛选
            select_inf_dict = ratio_vn_kv_valid_dict['interface'].copy()
            ratio_vn_kv_valid_dict['interface'] = ddeal.interface_select_trafo(select_inf_dict, net=net,
                                                                               inf_ele_type=inf_ele_type,
                                                                               area_details_dict=area_details_dict)
            return ratio_vn_kv_valid_dict

        # 根据目标分区及分区关联关系筛选数据; 'line','trafo','interface'(不做筛选)
        if area_name not in area_details:
            raise ValueError("分区关联设备索引中不存在分区:{}".format(area_name))

        def find_indices_in(list1, list2_set):
            # list2_set是list2的集合版本，用于快速查找
            indices = [index for index, element in enumerate(list1) if element in list2_set]
            return indices

        rtn_rate_dict = dict()
        rtn_rate_dict['line'] = dict()
        rtn_rate_dict['trafo'] = dict()
        if 'trafo' in area_details[area_name]:
            trafo_idx_list = area_details[area_name]['trafo']
            trafo_loadratio_dict = ratio_vn_kv_valid_dict['trafo']
            # 根据trafo_loadratio_dict['index']中的元素筛选出在trafo_idx_list中的元素的位置,
            # 遍历trafo_loadratio_dict所有的key,抽出对应位置的数据，赋值给rtn_rate_dict['trafo']
            trafo_in_indices = find_indices_in(trafo_loadratio_dict['index'], set(trafo_idx_list))
            for key, val in trafo_loadratio_dict.items():
                rtn_rate_dict['trafo'][key] = np.array(val)[trafo_in_indices].tolist()
        # line
        if 'line' in area_details[area_name]:
            idx_list = area_details[area_name]['line']
            line_loadratio_dict = ratio_vn_kv_valid_dict['line']
            line_in_indices = find_indices_in(line_loadratio_dict['index'], set(idx_list))
            for key, val in line_loadratio_dict.items():
                rtn_rate_dict['line'][key] = np.array(val)[line_in_indices].tolist()

        # interface筛选
        select_inf_dict = ratio_vn_kv_valid_dict['interface'].copy()
        rtn_rate_dict['interface'] = ddeal.interface_select_trafo(select_inf_dict, net=net,
                                                                  inf_ele_type=inf_ele_type,
                                                                  area_details_dict=area_details_dict)
        # 通道(2级断面)
        rtn_rate_dict['channel'] = channel_dict
        return rtn_rate_dict


def get_inf_collection_ratio_info(ratio_result_dict: dict, case_inf_collection_dict: dict = dict()):
    """
    计算获取2级通道的平均负载率、最大负载率信息;
    ratio_result_dict: self.case_rlt_cal['device_loadratio']
    case_inf_collection_dict: self.case_info['inf_collection']
    :return:
    """
    channel_dict = dict()
    if case_inf_collection_dict:
        channel_dict = dict()
        # 选择合适的col并排序
        col_need = {"p_limit": 1, "avg_ratio": 2, "max_ratio": 3, "over_hours_ratio": 4, "heavy_hours_ratio": 5}
        col_sta_dict = {"p_limit": "sum", "avg_ratio": "mean", "max_ratio": "mean", "over_hours_ratio": "mean",
                        "heavy_hours_ratio": "mean"}
        channel_dict["columns"] = list(col_need.keys())

        sorted_columns = sorted(col_need.keys(), key=lambda x: col_need[x])
        line_loadratio_dict = ratio_result_dict['line']
        # 重新恢复成df key作为column, val作为value
        line_loadratio_df = pd.DataFrame(line_loadratio_dict)
        line_loadratio_df.set_index("index", inplace=True)
        # 筛选出line_loadratio_df在col_need.key的column并按value排序
        line_loadratio_df = line_loadratio_df[sorted_columns]

        trafo_loadratio_dict = ratio_result_dict['trafo']
        # 重新恢复成df key作为column, val作为value
        trafo_loadratio_df = pd.DataFrame(trafo_loadratio_dict)
        trafo_loadratio_df.set_index("index", inplace=True)
        trafo_loadratio_df = trafo_loadratio_df[sorted_columns]

        for i_c, item in case_inf_collection_dict.items():
            line_idx_dict, trafo_idx_dict = item["relation"]["line"], item["relation"]["trafo"]
            channel_dict[i_c] = dict()
            for key, i_item in line_idx_dict.items():
                channel_dict[i_c][key] = line_loadratio_df.loc[i_item].agg(col_sta_dict).values.round(3).tolist()
            for key, i_item in trafo_idx_dict.items():
                channel_dict[i_c][key] = trafo_loadratio_df.loc[i_item].agg(col_sta_dict).values.round(3).tolist()
    return channel_dict


def get_ele_power_rate_qr_(net, config: dict, result_dict: dict, ele_type: str, ele_dict: dict, rate_mk=[0.8, 1.0]):
    """
    获取设备(线路/主变/主变断面/通道)的 线路(如果是双回线路合并)/主变
    获取设备的潮流、限额时序
    计算越限/重载时长及比例、最大负载率及时刻、最大限额率
    生成重载/越限二维码qr图信息;
    :param net: net网络
    :param config: 配置参数信息
    :param result_dict: teap的计算结果
    :param ele_type: "trafo","line","interface","channel"
    :param ele_dict: 带查询设备信息dict;
    interface：传入{"index"：26}
    channel:传入：{"channel_name":"苏南受电断面","interface_name":"姑苏-常熟南"}
    line:传入:{"fromName":"梦溪","toName":"江都","pos": [155,156], "neg": []}
    :param rate_mk: 重过载门槛值
    :return:
    dict
    """
    rtn_dict = {
        "powerflow": [],  # 潮流时序;
        "limit": 0.0,  # 限额;
        "rate_info": dict(),  # 负载率信息
        "qr_info": dict()  # 二维码信息
    }

    if ele_type == "interface":
        ele_index = ele_dict["index"]
        if ele_index > net["interface"].index.max():
            raise ValueError("输入的接口索引超出设备索引范围")
        powerflow = (result_dict["interface_power"][ele_index, :] * 0.1).round(1).tolist()  # MW->万千瓦
        ele_limit = round(float(net["interface"].loc[ele_index, "max_p_mw"]) * 0.1, 1)  # 万千瓦
        ele_name = net["interface"].loc[ele_index, "name"]
    elif ele_type == "trafo":
        ele_name = ""
    elif ele_type == "line":
        # 传入线路的关联支路索引及方向;
        pos_line, neg_line = ele_dict["pos"], ele_dict["neg"]
        powerflow_arrs = result_dict["line_power"][pos_line, :].sum(axis=0) - result_dict["line_power"][neg_line,
                                                                              :].sum(axis=0)  # MW
        powerflow = (powerflow_arrs * 0.1).round(1).tolist()
        ele_limit = round(float(net["line"].loc[pos_line + neg_line, "stable_limit_mw"].values.sum()) * 0.1, 1)
        ele_name = f"{ele_dict['fromName']}_{ele_dict['toName']}"
    elif ele_type == "channel":
        # 直接传入：通道名称: channel_name; 断面名称:interface_name
        channel_info_dict = CTeapCase.get_inf_collections(net)
        channel_dict = rlt_base.get_channel_power_info(net, result_dict, channel_info_dict, ele_dict)
        powerflow = (np.array(channel_dict["powerflow"]) * 0.1).round(1).tolist()
        ele_limit = round(float(channel_dict["limit"]) * 0.1, 1)
        ele_name = ele_dict["interface_name"]
    else:
        raise TypeError(f"输入的设备类型不支持：{ele_type}")

    # 生成返回结果结构
    rtn_dict["powerflow"] = powerflow
    rtn_dict["limit"] = ele_limit
    # 负载率
    p_rate_series = np.abs(np.array(powerflow)) / ele_limit
    # 重载时长、过载时长、等效过载时长
    heavy_hours, over_hours, eq_over_hours = rlt_base.cal_element_loadrate_hours(p_rate_series, rate_mk=rate_mk)
    rtn_dict["rate_info"] = {
        "name": ele_name,
        "max_p_rate": round(float(p_rate_series.max()), 4),  # 位置:arr1.argmax()
        "heavy_hours": int(heavy_hours),
        "over_hours": int(over_hours),
        "eq_over_hours": round(float(eq_over_hours), 1)
    }
    # qr 二维码图; 0-轻载;1-重载;2-越限;
    rtn_dict["qr_info"] = ddeal.exchange_data_to_qrformat(p_rate_series, config, mk_val=rate_mk,
                                                          flag_val=[0, 1, 2], sta_type="max")
    return rtn_dict


def get_channel_relayinf_info_qr_(net, config: dict, result_dict: dict, ele_dict: dict,
                                  rate_mk=[0.8, 1.0]):
    """
    江苏特殊接口; 2级通道关联的断面结果信息获取;
    # 过江通道关联通道： 写：
    # 1、淮安 - 三汊湾(四线)
    # 前端传参：{"channel_name": "过江断面", "interface_name": "淮安-三汊湾"}
    # 2、三汊湾 - 秋藤(双线)
    # 前端传参：{"channel_name": "过江断面", "interface_name": "三汊湾-秋藤"
    # 3、秋藤 - 望江（双线）
    # 前端传参：{"channel_name": "过江断面", "interface_name": "秋藤-望江"}  # 220kV
    # 4、游圌直流
    # 前端传参：{"channel_name": "过江断面", "interface_name": "游圌直流"}

    # 西电东送关联通道：
    # 1、西津渡 - 廻峰山
    # 前端传参：{"channel_name": "西电东送断面", "interface_name": "西津渡-廻峰山"}
    # 2、西津渡 - 天目湖
    # 前端传参：{"channel_name": "西电东送断面", "interface_name": "西津渡-天目湖"}
    # 3、西津渡 - 廻峰山 / 天目湖
    # 前端传参：{"channel_name": "西电东送断面", "interface_name": "西津渡-廻峰山/天目湖"}
    #
    # 苏州南部关联通道：
    # 石牌 - 玉山
    # 前端传参：{"channel_name": "苏南受电断面", "interface_name": "石牌-玉山"}
    :param net:
    :param config:
    :param result_dict:
    :param ele_dict:
    :param rate_mk:
    :return:
    """
    rtn_dict = {
        "powerflow": [],  # 潮流时序;
        "limit": 0.0,  # 限额;
        "rate_info": dict(),  # 负载率信息
        "qr_info": dict()  # 二维码信息
    }

    channel_name, channel_inf_name = ele_dict["channel_name"], ele_dict["interface_name"]
    # 先根据channel_inf_name,解析首末端;
    if "-" in channel_inf_name:
        inf_node_name = channel_inf_name.split("-")
        fromName, toName = inf_node_name[0], inf_node_name[-1]
        if fromName == "秋藤" and toName == "三汊湾":
            fromName, toName = toName, fromName
    else:
        if channel_name == "过江断面" and "游圌" in channel_inf_name:
            if "dcline" in net.keys() and len(net.dcline) > 0:
                youchui_df = net.dcline[net.dcline["name"].str.contains("游圌")]
                yc_pf = result_dict["dcline_power_from"][youchui_df.index, :].sum(axis=0)
                rtn_dict["powerflow"] = (yc_pf * 0.1).round(1).tolist()  # 万千瓦
                rtn_dict["limit"] = round(float(youchui_df["max_p_mw"].sum()) * 0.1, 1)
                rtn_dict["rate_info"] = {
                    "name": channel_inf_name,
                    "max_p_rate": 1.0,
                    "heavy_hours": 0,
                    "over_hours": 0,
                    "eq_over_hours": 0
                }
                # qr 二维码图; 0-轻载;1-重载;2-越限;
                # rtn_dict["qr_info"] = dict()
            else:
                pass
            return rtn_dict
        else:
            raise ValueError(f"输入的关联断面：{channel_inf_name} 不支持")
    # 根据 fromName,to_name找对应关联的线路index;存入:{"pos":[],"neg":[]}
    bus_dispname_dict = net["bus"]["dispname"].to_dict()
    line_copy = net.line.copy()
    if "from_name" not in net["line"].columns:
        line_copy["from_name"] = net["line"]["from_bus"].map(bus_dispname_dict)
    if "to_name" not in net["line"].columns:
        line_copy["to_name"] = net["line"]["to_bus"].map(bus_dispname_dict)

    pos_idx = line_copy[(line_copy["from_name"] == fromName) & (line_copy["to_name"] == toName)].index.tolist()
    neg_idx = line_copy[(line_copy["from_name"] == toName) & (line_copy["to_name"] == fromName)].index.tolist()
    if toName == "廻峰山/天目湖":
        pos_idx = line_copy[
            (line_copy["from_name"] == fromName) & (line_copy["to_name"].isin(["廻峰山", "天目湖"]))].index.tolist()
        neg_idx = line_copy[
            (line_copy["from_name"].isin(["廻峰山", "天目湖"])) & (line_copy["to_name"] == fromName)].index.tolist()

    # 没匹配到的话,进一步尝试匹配;
    if len(pos_idx) == 0 and len(neg_idx) == 0:
        pos_idx = line_copy[(line_copy["from_name"].str.contains(fromName)) & (
            line_copy["to_name"].str.contains(toName))].index.tolist()
        neg_idx = line_copy[(line_copy["from_name"].str.contains(toName)) & (
            line_copy["to_name"].str.contains(fromName))].index.tolist()

    if len(pos_idx + neg_idx) > 0:
        # 计算潮流功率时序;
        powerflow_arrs = result_dict["line_power"][pos_idx, :].sum(axis=0) - result_dict["line_power"][neg_idx,
                                                                             :].sum(axis=0)  # MW
        rtn_dict["powerflow"] = (powerflow_arrs * 0.1).round(1).tolist()  # 万千瓦
        rtn_dict["limit"] = min(999999.0,
                                round(float(net["line"].loc[pos_idx + neg_idx, "stable_limit_mw"].values.sum()) * 0.1,
                                      1))  # 万千瓦;

        # 检查interface里有没有
        for index, row in net["interface"].iterrows():
            if toName == "廻峰山/天目湖":
                if fromName in row["name"] and ("廻峰山" in row["name"] or "天目湖" in row["name"]):
                    rtn_dict["limit"] = row["max_p_mw"] * 0.1
                    break
            elif fromName == "廻峰山/天目湖":
                if toName in row["name"] and ("廻峰山" in row["name"] or "天目湖" in row["name"]):
                    rtn_dict["limit"] = row["max_p_mw"] * 0.1
                    break
            else:
                pass
                # if fromName in row["name"] and toName in row["name"]:
                #     rtn_dict["limit"] = row["max_p_mw"]
                #     break

        # 生成返回结果结构
        p_rate_series = np.array(rtn_dict["powerflow"]) / rtn_dict["limit"]  # 负载率
        max_p_rate = round(float(p_rate_series.max()), 4)  # 位置:arr1.argmax()
        # 重载时长、过载时长、等效过载时长
        heavy_hours, over_hours, eq_over_hours = rlt_base.cal_element_loadrate_hours(p_rate_series, rate_mk=rate_mk)
    else:
        p_rate_series = np.zeros(config["sum_snap"])
        rtn_dict["powerflow"] = p_rate_series.tolist()
        rtn_dict["limit"] = 999999.0
        max_p_rate = 0.0
        heavy_hours, over_hours, eq_over_hours = 0, 0, 0

    rtn_dict["rate_info"] = {
        "name": channel_inf_name,
        "max_p_rate": max_p_rate,
        "heavy_hours": int(heavy_hours),
        "over_hours": int(over_hours),
        "eq_over_hours": round(float(eq_over_hours), 1)
    }
    # qr 二维码图; 0-轻载;1-重载;2-越限;
    rtn_dict["qr_info"] = ddeal.exchange_data_to_qrformat(p_rate_series, config, mk_val=rate_mk,
                                                          flag_val=[0, 1, 2], sta_type="max")
    return rtn_dict


def get_heavy_rate_device_info(net, config, result_dict: dict, grep_vn_kv: bool = False,
                               only_heavy_device: bool = True):
    """
    FUNC:获取全年平均重载设备(主变trafo/线路line/断面interface)列表;  所有电压等级;
    Args:
        cal_result_dict:
        # config:配置参数;--电压等级的过滤在统计分析的时候就进行操作;
        # select_vn_kv = config['device_dis_vn_kv'] if 'device_dis_vn_kv' in config else 500
        grep_vn_kv: 过滤电压等级
        only_heavy_device：只输出重载设备
    Returns:
        dict()
    """
    rtn_data_dict = {
        'line': dict(),
        'trafo': dict()
    }
    lines_df, trafos_df, interface_df = cal_all_devices_loadratio_df(net, result_dict, config, grep_vn_kv=grep_vn_kv,
                                                                     only_heavy_device=only_heavy_device)

    def UpdatVlevel(ori_v):
        vlevel = ori_v
        if abs(vlevel - 10) < 2:
            vlevel = 10
        elif abs(vlevel - 35) < 5:
            vlevel = 35
        elif abs(vlevel - 110) < 11:
            vlevel = 110
        elif abs(vlevel - 220) < 22:
            vlevel = 220
        elif abs(vlevel - 330) < 33:
            vlevel = 330
        elif abs(vlevel - 500) < 50:
            vlevel = 500
        return vlevel

    # line
    rtn_data_dict['line']['device_num'] = len(lines_df)
    rtn_data_dict['line']['detail'] = list()
    # 遍历lines_df的所有行
    for idx, row in lines_df.iterrows():
        # 修正电压等级至标准电压等级10、35、110、220
        rtn_data_dict['line']['detail'].append({
            'index': row['index'],
            'vn_kv': UpdatVlevel(float(net.line.loc[row['index'], 'vn_kv'])),
            'name': row['name'],
            'p_limit': row['p_limit'],
            'max_ratio': row['max_ratio'],
            'avg_ratio': row['avg_ratio'],
            'over_hours_ratio': int(row['over_hours_ratio'] * 100),
            'heavy_hours_ratio': int(row['heavy_hours_ratio'] * 100)
        })  # ['index', 'name', 'p_limit', 'avg_ratio', 'max_ratio', 'over_hours_ratio', 'heavy_hours_ratio'])

    # trafo
    # trafos_df 增加列'trafo_name'  等于'index' == net.trafo.index 的net.trafo['trafo_name']
    trafos_df['trafo_name'] = trafos_df['index'].map(lambda x: net.trafo.loc[x, 'trafo_name'])
    # 针对trafos_df 的 'trafo_name'进行去重复,如果有重复，则取'heavy_hours_ratio'大的那条记录,不是最后一条记录
    trafos_df = trafos_df.sort_values(by=['heavy_hours_ratio'], ascending=False)
    trafos_df = trafos_df.drop_duplicates(subset=['trafo_name'], keep='first')

    rtn_data_dict['trafo']['device_num'] = len(trafos_df)
    rtn_data_dict['trafo']['detail'] = list()
    for idx, row in trafos_df.iterrows():
        rtn_data_dict['trafo']['detail'].append({
            'index': row['index'],
            'vn_kv': UpdatVlevel(float(net.trafo.loc[row['index'], 'vn_hv_kv'])),
            'name': row['trafo_name'],
            'p_limit': row['p_limit'],
            'max_ratio': row['max_ratio'],
            'avg_ratio': row['avg_ratio'],
            'over_hours_ratio': int(row['over_hours_ratio'] * 100),
            'heavy_hours_ratio': int(row['heavy_hours_ratio'] * 100)
        })
    return rtn_data_dict


def get_device_loadratio_info(device_type: str, device_idx: int, result_dict: dict, devcie_limit: dict = dict(),
                              config: dict = dict()):
    """
    获取设备i的全年重载/越限情况明细，时刻,设备i的有功,限额，负载率;
    device_type: 设备类型: 'trafo','line','interface'
    device_idx: 设备索引号
    result_dict: dict, 计算结果;
    devcie_limit: dict, 设备限额---case_rlt_cal['device_limit']
    """
    if device_type not in ['trafo', 'line', 'interface']:
        raise ValueError(f"输入的设备类型:{device_type}不支持,仅支持:'主变、线路、断面'")

    device_p_df = pd.DataFrame()

    heavy_ratio = 0.85
    if device_type == 'trafo':
        # 从 'power':abs(result_dict['trafo_power'][device_idx:]的数据
        # 'limit': devcie_limit[device_idx]
        # 'rate': abs('power'/ 'limit')
        power = np.abs(result_dict['trafo_power'][device_idx, :])
        limit = devcie_limit['trafo'][device_idx]
        if 'trafo_heavy_ratio' in config:
            heavy_ratio = config['trafo_heavy_ratio']
    elif device_type == 'line':
        power = np.abs(result_dict['line_power'][device_idx, :])
        limit = devcie_limit['line'][device_idx]
        if 'line_heavy_ratio' in config:
            heavy_ratio = config['line_heavy_ratio']
    else:
        power = np.abs(result_dict['interface_power'][device_idx, :])
        limit = devcie_limit['interface'][device_idx]
        if 'interface_heavy_ratio' in config:
            heavy_ratio = config['interface_heavy_ratio']
    device_p_df = pd.concat([device_p_df, pd.DataFrame({
        'time_no': [i for i in range(len(power))],
        'power': power,
        'limit': limit,
        'rate': power / limit
    })], axis=1)

    # 筛选'rate' > heavy_ratio的df
    device_p_df = device_p_df[device_p_df['rate'] > heavy_ratio]

    # 将device_df按行转为dict,column作为key
    return device_p_df.to_dict('list')


def get_all_device_loadratio_distribute(cal_result_dict: dict, casee_address: str = '', rate_type='max_ratio'):
    """
    FUNC:获取全年设备的平均负载率的百分比分布信息(主变trafo/线路line/断面interface)列表;
    # 设备负载率分档比例: 0,0.3,0.5,0.8,1.0   #五档;  --config: device_rate_step
    Args:
        cal_result_dict: data['cal_result']
        vn_kv: 需要过滤的设备电压等级;
        casee_address: 算例分析的具体场景，方便读取配置conf中的config_extern.py配置
        rate_type: 是avg_ratio/max_ratio/over_hours_ratio/heavy_hours_ratio
    Returns:
        dict
    """
    rtn_data_dict = dict()
    rtn_data_dict["device_num"] = dict()  # 设备对应数目;
    if rate_type not in ['avg_ratio', 'max_ratio', 'over_hours_ratio', 'heavy_hours_ratio']:
        raise ValueError(f"输入的设备统计参数类型不支持：{rate_type}")

    if 'device_loadratio' not in cal_result_dict:
        raise ValueError("结果分析中缺少设备重载过载信息的数据")
    else:
        ana_config = get_ana_config_info(casee_address)
        dev_rate_step_mklist = sorted(
            ana_config['config_exter_dict']['device_rate_step'])  # 排序 --从小到大排序-sorted(my_list)
        dev_rate_step_mklist = [0, 0.3, 0.6, 0.9] if len(dev_rate_step_mklist) < 3 else dev_rate_step_mklist
        min_step_i = dev_rate_step_mklist[0]  # step的最小值;
        # 如果step最后一个值小于1.0的话,在加一个值100,用于统计大于1.0的负载率的设备
        if min_step_i > 0:
            dev_rate_step_mklist = [0] + dev_rate_step_mklist
        step_len = len(dev_rate_step_mklist)

        # 按设备trafo\line\interface逐个统计;
        dev_dict_ori = cal_result_dict['device_loadratio']  # 数据
        # 筛选目标需要的电压等级以上的设备
        dev_dict = ddeal.check_ratio_vn_kv(dev_dict_ori)

        for i_devtype in dev_dict.keys():
            if not dev_dict[i_devtype]:  # 检查设备是否为空
                rtn_data_dict[i_devtype] = {str(i_mk): 0.0 for i_mk in dev_rate_step_mklist}  # 设备是空的
                continue
            dev_rate_arr = np.array(dev_dict[i_devtype][rate_type])  # 设备的平均负载率信息
            len_dev = len(dev_dict[i_devtype][rate_type])
            rtn_data_dict[i_devtype] = dict()
            rtn_data_dict["device_num"][i_devtype] = dict()
            for i_mk in range(step_len):
                i_mk_val = dev_rate_step_mklist[i_mk]
                if i_mk == step_len - 1:
                    i_mk_val_min = dev_rate_step_mklist[-1]
                    i_mk_val_max = 100
                else:
                    i_mk_val_min = dev_rate_step_mklist[i_mk]
                    i_mk_val_max = dev_rate_step_mklist[i_mk + 1]

                # 统计dev_rate_date在[i_mk_val_min,i_mk_val_max) 中的设备数目
                percent_bool_in = (dev_rate_arr >= i_mk_val_min) & (dev_rate_arr < i_mk_val_max)
                rtn_data_dict[i_devtype][str(i_mk_val)] = round(float(np.sum(percent_bool_in) / len_dev), 4)
                rtn_data_dict['device_num'][i_devtype][str(i_mk_val)] = int(np.sum(percent_bool_in))

    return rtn_data_dict


def get_device_ratio_distribute(case_info_dict: dict, result_dict: dict):
    """
    计算统计分析线路主变设备的各个指标分布,主要包括：平均负载率、最大负载率、越限小时、重载小时、等效越限小时; 包括地图上的线路(多回线路平均)/主变(最高)
    rate_type=['avg_ratio', 'max_ratio', 'over_hours_ratio', 'heavy_hours_ratio']
    :param case_info_dict:  算例信息;
    :param cal_result_dict:  计算结果;
    :param result_dict:  teap的计算结果;
    :param casee_address:   计算场景;
    :return:
        dict(): such as : {"line_eq_heavy_hours":{"bin_edges":[],"hist":[]}},
    """
    ele_ratio_dict = {
        "line_heavy_hours": dict(),  # 线路重载时长
        "line_over_hours": dict(),  # 线路过载时长
        "line_eq_over_hours": dict(),  # 线路等效过载时长
        "trafo_heavy_hours": dict(),  # 主变重载时长
        "trafo_over_hours": dict(),  # 主变过载时长
        "trafo_eq_over_hours": dict(),  # 主变等效过载时长
    }
    # 电网拓扑
    vn_kv = 500 * 0.9
    bus_data_list = copy.deepcopy(case_info_dict['topo_info']['point_data'])
    bus_data_list = [x for x in bus_data_list if x['vn_kv'] >= vn_kv and x['type'] == 'station']
    line_data_list = copy.deepcopy(case_info_dict['topo_info']['line_data'])
    line_data_list = [x for x in line_data_list if x['vn_kv'] >= vn_kv]

    # 处理线路--多回线路合并;--和平均
    line_tunnel_rate, trafo_tunnel_rate = rlt_base.get_topo_elements_attri_series(result_dict, line_data_list,
                                                                                  bus_data_list)

    # 统计所有设备的重载/越限时长
    line_heavy_rate_hours, line_over_rate_hours, line_eq_over_hours = rlt_base.cal_topo_elements_loadrate_hours(
        line_tunnel_rate,
        rate_mk=[0.8,
                 1.0])
    trafo_heavy_rate_hours, trafo_over_rate_hours, trafo_eq_over_hours = rlt_base.cal_topo_elements_loadrate_hours(
        trafo_tunnel_rate,
        rate_mk=[0.8, 1.0])

    # 统计时长分布的设备数目;
    heav_bin_edges = [0, 1000, 2000, 4000, 5000, 9000]  # 重载
    over_bin_edges = [0, 40, 80, 120, 160, 9000]  # 越限
    line_heavy_hist, bin_edges = np.histogram(line_heavy_rate_hours[line_heavy_rate_hours != 0], bins=heav_bin_edges)
    ele_ratio_dict["line_heavy_hours"] = {
        "hist": line_heavy_hist.round(1).tolist(),
        "bin_edges": bin_edges.round(1).tolist()}
    line_over_hist, bin_edges = np.histogram(line_over_rate_hours[line_over_rate_hours != 0], bins=over_bin_edges)
    ele_ratio_dict["line_over_hours"] = {
        "hist": line_over_hist.round(1).tolist(),
        "bin_edges": bin_edges.round(1).tolist()}
    line_eq_over_hist, bin_edges = np.histogram(line_eq_over_hours[line_eq_over_hours != 0], bins=over_bin_edges)
    ele_ratio_dict["line_eq_over_hours"] = {
        "hist": line_eq_over_hist.round(1).tolist(),
        "bin_edges": bin_edges.round(1).tolist()}

    trafo_heavy_hist, bin_edges = np.histogram(trafo_heavy_rate_hours[trafo_heavy_rate_hours != 0], bins=heav_bin_edges)
    ele_ratio_dict["trafo_heavy_hours"] = {
        "hist": trafo_heavy_hist.round(1).tolist(),
        "bin_edges": bin_edges.round(1).tolist()}
    trafo_over_hist, bin_edges = np.histogram(trafo_over_rate_hours[trafo_over_rate_hours != 0], bins=over_bin_edges)
    ele_ratio_dict["trafo_over_hours"] = {
        "hist": trafo_over_hist.round(1).tolist(),
        "bin_edges": bin_edges.round(1).tolist()}
    trafo_eq_over_hist, bin_edges = np.histogram(trafo_eq_over_hours[trafo_eq_over_hours != 0], bins=over_bin_edges)
    ele_ratio_dict["trafo_eq_over_hours"] = {
        "hist": trafo_eq_over_hist.round(1).tolist(),
        "bin_edges": bin_edges.round(1).tolist()}

    return ele_ratio_dict


def get_device_loadratio_timestep(net: pp.pandapowerNet, result_dict: dict, time_no: int, config: dict = dict(),
                                  area_name: str = None, area_detail: dict = dict()):
    """
    获取指定时刻的电网潮流断面数据及设备负载率,设备有功值、限额、负载率数据，根据负载率从大到小排序;
    Args:
        net: 网络;  --  data['network']
        result_dict: teap计算结果数据-- data['result_output']
        timestep: 指定的时刻 - -- data['result_output']
        "名称、有功、限额、负载率时"-- - -- data['case_info']['config']
        vn_kv: 电压等级
        time_no :int, 如果非None, 获取time_no时刻的具体的所有设备的有功、负载率、限额数据;
        获取指定时刻的电网潮流断面数据及设备负载率,设备有功值、限额、负载率数据，根据负载率从大到小排序;
        config: caseinfo中的config,dict
        area_name: 需要提取分区的设备; None默认全网;
        area_detail:分区关联设备索引  如果非全网,分区时需要传入; # 分区关联设备索引--data['case_info']['zone']['device_relay']
        max_num: 最大单类型设备的获取数目;
    Returns:
    """
    line_power_t = result_dict['line_power'][:, time_no]
    trafo_power_t = result_dict['trafo_power'][:, time_no]
    branch_power = np.concatenate((line_power_t, trafo_power_t)).reshape(-1, 1)
    Pbr_max = get_Pbr_max(net, net.line.index, net.trafo.index)
    data_dict = cal_branch_rate_simple(net, config, branch_power, Pbr_max, area_name, area_detail)

    return data_dict


def get_inf_power_timesteps(net: pp.pandapowerNet, result_dict: dict, time_no: list = [], inf_max: bool = False):
    """
    # 获取指定时段内[]内的断面的限额、有功  #具体线路有功明细;
    net: 网络
    result_dict: teap结算结果
    time_no；list(),想要获取的时段;
    """
    inf_dict = dict()
    if 'interface_power' not in result_dict:
        raise ValueError("结果分析中缺少断面有功功率信息,请检查算例中是否断面数据")

    inf_df = net.interface

    n_tim_no = len(time_no)
    if n_tim_no > 0:
        inf_power_ts = result_dict['interface_power'][:, time_no]
        time_no_new = time_no
    elif n_tim_no == 0 and not inf_max:
        time_no_new = slice(None)
        inf_power_ts = result_dict['interface_power']
    else:
        time_no_new = list()
        # 根据各个interface的潮流最大时刻选择对应时刻填充time_no
        for index, row in inf_df.iterrows():
            inf_max_idx = np.argmax(result_dict['interface_power'][index, :])
            time_no_new.append(inf_max_idx)
        inf_power_ts = result_dict['interface_power'][:, time_no_new]
    # 获取断面的名称和限额信息生成dict返回-----------------单位都是MW

    inf_dev_idxes_dict = get_inf_unit_idxes(net)  # 二级断面
    for index, row in inf_df.iterrows():
        inf_dict[row['name']] = dict()
        inf_dict[row['name']]['name'] = row['name']
        inf_dict[row['name']]['limit'] = round(row['max_p_mw'], 1)
        inf_dict[row['name']]['power'] = inf_power_ts[index, :].round(1).tolist()
        inf_dict[row['name']]['p_rate'] = (np.abs(inf_power_ts[index, :]) / row['max_p_mw']).round(4).tolist()

        # 断面关联设备的潮流明细; [name,热稳限额,有功值,负载率]
        inf_dict[row['name']]['detail'] = list()
        # 断面关联设备;
        if row['name'] in inf_dev_idxes_dict:
            inf_dev_idx_dict = inf_dev_idxes_dict[row['name']]  # dict:{"line":[],"trafo":[]}
            # line
            for i_line in inf_dev_idx_dict['line']:
                inf_detail_dict = dict()
                inf_detail_dict['name'] = str(net.line.loc[i_line, 'name'])
                i_line_limit = float(net.line.loc[i_line, 'stable_limit_mw'])
                inf_detail_dict['limit'] = round(i_line_limit, 1)
                p_direction = float(net.line.loc[i_line, 'interface_direction'])  # 潮流方向;
                power_series = result_dict['line_power'][i_line, time_no_new] * p_direction
                inf_detail_dict['power'] = power_series.round(1).tolist()
                inf_detail_dict['p_rate'] = np.divide(np.abs(power_series), i_line_limit).round(4).tolist()
                inf_dict[row['name']]['detail'].append(inf_detail_dict)
            # trafo
            for i_tf in inf_dev_idx_dict['trafo']:
                inf_detail_dict = dict()
                inf_detail_dict['name'] = str(net.trafo.loc[i_tf, 'name'])
                i_tf_limit = float(net.trafo.loc[i_tf, 'stable_limit_mw'])
                inf_detail_dict['limit'] = round(i_tf_limit, 1)
                p_direction = float(net.trafo.loc[i_tf, 'interface_direction'])  # 潮流方向;
                tf_power_series = result_dict['trafo_power'][i_tf, time_no_new] * p_direction
                inf_detail_dict['power'] = tf_power_series.round(1).tolist()
                inf_detail_dict['p_rate'] = np.divide(np.abs(tf_power_series), i_tf_limit).round(4).tolist()
                inf_dict[row['name']]['detail'].append(inf_detail_dict)
    return inf_dict


def get_allzone_indicators(indicator_rlt_dict: dict, time_no: int = None, area_name: str = GLOBAL_QW_NAME,
                           area_details: dict = dict(), result_dict: dict = dict()):
    """
    获取全网各分区的全年6维指标最小裕度值及时刻;  #增加新能源消纳率指标;
    # 推演页面: 数据边界(其他接口)、 全网/各分区的供电裕度等6个指标,时刻的断面/主变/线路的负载率-限额;-'全省'
    Args:
        indicator_rlt_dict: dict(), 指标计算结构体;
        time_no : 时刻;,如果是None,返回所有时序的结果数据;
        area_name: 分区名;
        area_details: 分区关联设备索引;dict
        result_dict: TEAP的计算结果;
    Returns:
        dict()
        "thresholds":
        "indicators":
        "qs_fenqu_psm_color":
        同原来的: "baogongliuwei_nominal_chushizhi": {
        "全省": {
            "power_supply_margin": 3568.668800000001,  # 供电裕度
            "reserve_low": 320,   #正备用裕度
            "peak_shaving": 2011.2541000000006,   #调峰能力
            "non_inertia_penetration": 0.17120691944002545,   # 无惯量电源依存度
            "feedin_dependence": 0.12391338112598074,    #受入电力依存度
            "ramp_cap_upward": 94.1355   # 爬坡容量
        },
        "三门峡-新安": {
            "power_supply_margin": 526.9845764034468,
            "reserve_low": 320,
            "peak_shaving": 2011.2541000000006,
            "non_inertia_penetration": 0.17120691944002545,
            "feedin_dependence": 0.10016672889898934,
            "ramp_cap_upward": 94.1355
        },
    """
    rtn_data_dict = dict()
    if 'indicator_data' not in indicator_rlt_dict:
        raise ValueError("指标分析计算结果中缺少全网/分区供电裕度等指标结果数据:{indicator_data}")
    if 'indicator_thresholds' not in indicator_rlt_dict:
        raise ValueError("指标分析计算结果中缺少全网/分区供电裕度等指标结果数据:{indicator_thresholds}")
    # else:
    #     rtn_data_dict['thresholds'] = indicator_rlt_dict['indicator_thresholds']

    rtn_data_dict['indicators'] = dict()
    rtn_data_dict['thresholds'] = dict()
    rtn_data_dict['psm_color'] = dict()
    rtn_data_dict['new_consump_rate'] = dict()  # 新增新能源消纳率指标
    indicator_data_dict = indicator_rlt_dict['indicator_data']
    thresholds_data_dict = indicator_rlt_dict['indicator_thresholds']

    # 取全省供电裕度最小时刻;
    time_step = int(
        np.argmin(indicator_data_dict[GLOBAL_QW_NAME]['power_supply_margin'])) if time_no is None else time_no

    # 计算时刻每个分区的新能源消纳率;
    if result_dict:
        rtn_data_dict['new_consump_rate'] = result_zone_newerg_consump_rate(result_dict,
                                                                            area_details_dict=area_details,
                                                                            time_step=time_step)

    # 取时刻点的数目;
    for i_zone, value_i in indicator_data_dict.items():
        rtn_data_dict['indicators'][i_zone] = dict()
        if isinstance(value_i, dict):
            for i_key in value_i:
                rtn_data_dict['indicators'][i_zone][i_key] = value_i[i_key][time_step]

    for i_zone, value_i in thresholds_data_dict.items():
        rtn_data_dict['thresholds'][i_zone] = dict()
        if isinstance(value_i, dict):
            for i_key in value_i:
                rtn_data_dict['thresholds'][i_zone][i_key] = value_i[i_key][time_step]

    # 供电裕度着色
    for i_zone in indicator_data_dict.keys():
        area_thresholds = thresholds_data_dict[i_zone]['power_supply_margin'][time_step]  # list--4
        abs_threshold = np.array(area_thresholds)[1:3]
        psm_ = indicator_data_dict[i_zone]['power_supply_margin'][time_step]
        psm_cm_bool = np.where(psm_ > abs_threshold, 1, 0)
        rtn_data_dict['psm_color'][i_zone] = psm_cm_bool.sum(0).tolist()

    if area_name != GLOBAL_QW_NAME:
        # 只返回对应分区的数据;
        rtn_data_dict_area = dict()
        rtn_data_dict_area['indicators'] = rtn_data_dict['indicators'][area_name]
        rtn_data_dict_area['thresholds'] = rtn_data_dict['thresholds'][area_name]
        rtn_data_dict_area['psm_color'] = rtn_data_dict['psm_color'][area_name]
        rtn_data_dict_area['new_consump_rate'] = rtn_data_dict['new_consump_rate'][area_name]
        return rtn_data_dict_area
    else:
        return rtn_data_dict


def get_zone_psm_timeseries_data(indicator_rlt_dict: dict, time_no: list = []):
    """
    获取分区的指定时段的供电裕度时序曲线;
    indicator_rlt_dict:指标分析结果dict
    area_name:分区名称
    """
    if 'indicator_data' not in indicator_rlt_dict:
        raise ValueError("算例分析结果中无指标分析结果")

    rtn_psm_dict = dict()
    if len(time_no) == 0:
        # 返回全部时序曲线;
        for zone_name, zone_data in indicator_rlt_dict['indicator_data'].items():
            rtn_psm_dict[zone_name] = zone_data['power_supply_margin']  # 万千瓦;
    else:
        # 返回指定时刻时序曲线;
        for zone_name, zone_data in indicator_rlt_dict['indicator_data'].items():
            rtn_psm_dict[zone_name] = np.array(zone_data['power_supply_margin'])[time_no].tolist()  # 万千瓦;
    return rtn_psm_dict


def get_allzone_psm_value(indicator_rlt_dict: dict, result_dict: dict, area_details: dict, ana_result: dict = dict(),
                          ana_result_new_curtail_dict: dict = dict()):
    """
    functiotn:获取全省/所有分区的最小供电裕度/平均供电裕度, 有供电缺口时长/供电紧张时长,总供电缺口电量;
    indicator_rlt_dict:  指标计算结果dict
    result_dict: teap的计算结果数据
    area_details: 分区关联设备索引;
    ana_result: 针对teap结果分析的结果字典: case_result['consump_rate']['zone']
    ana_result_new_curtail: 针对teap结果分析的结果字典: case_result['cutailment_hours']['zone']
    return:dict()
    {'zone_name':{'min_psm':-10,'avg_psm':200,'load_curtial_hours':100,'power_tight_hours':100}
    }
    min_psm:最小供电裕度；avg_psm：平均供电裕度；load_curtial_hours：缺电时长，power_tight_hours：供电紧张时长;
    # color: 0--供电裕度充足;1--供电裕度紧张;2--供电裕度不足(缺电)
    """
    rtn_indicator = dict()
    if 'indicator_data' not in indicator_rlt_dict:
        raise ValueError("指标分析计算结果中缺少全网/分区供电裕度等指标结果数据:{indicator_data}")

    def get_psm_color(psm_, abs_threshold):
        psm_cm_bool = np.where(psm_ > abs_threshold, 1, 0)
        return psm_cm_bool.sum(0).tolist()

    # def get_psm_arr_color(psm_arr, abs_threshold_arr):
    #     thresholds = np.array(abs_threshold_arr)
    #     # abs_threshold_arr[0]
    #     # 使用 NumPy 的向量化比较来创建分类数组
    #     # 注意：我们使用的是 thresholds 的前两个和三个元素作为阈值
    #     categories = np.zeros_like(psm_arr, dtype=int)  # 初始化一个与 arr1 相同形状的整数数组
    #     categories[(psm_arr >= thresholds[1]) & (psm_arr < thresholds[2])] = 1  # 在 thresholds[1] 和 thresholds[2] 之间
    #     categories[psm_arr >= thresholds[2]] = 2  # 大于或等于 thresholds[2]
    #     return categories

    thresholds_data_dict = indicator_rlt_dict['indicator_thresholds']
    rear_types_inds = indicator_rlt_dict['area_types']  # 分区类型判断指标

    for zone_name, zone_data in indicator_rlt_dict['indicator_data'].items():
        rtn_indicator[zone_name] = dict()
        zone_psm_arr = np.array(zone_data['power_supply_margin'])  # 万千瓦;
        area_min_psm = float(np.min(zone_psm_arr).round(1))
        area_min_psm_arg = np.argmin(zone_psm_arr)  # 最小供电裕度位置
        rtn_indicator[zone_name]['min_psm'] = area_min_psm
        area_avg_psm = float(np.mean(zone_psm_arr).round(1))
        rtn_indicator[zone_name]['avg_psm'] = area_avg_psm

        area_psm_thresholds_arr = thresholds_data_dict[zone_name]['power_supply_margin']
        abs_area_psm_threshold = np.array(area_psm_thresholds_arr[0])[1:3]
        abs_min_area_psm_threshold = np.array(area_psm_thresholds_arr[area_min_psm_arg])[1:3]  # 最小供电裕度着色采用对应时刻的裕度着色门槛值
        # # TODO:240713--江苏临时改成11
        # abs_area_psm_threshold[1] = 11
        # abs_min_area_psm_threshold[1] = 11
        rtn_indicator[zone_name]['avg_psm_color'] = get_psm_color(area_avg_psm, abs_area_psm_threshold)  # 平均供电裕度着色
        rtn_indicator[zone_name]['min_psm_color'] = get_psm_color(area_min_psm, abs_min_area_psm_threshold)  # 最小供电裕度着色

        # 供电紧张判断标志 # 供电紧张定义供电裕度<第0个点的裕度着色门槛值的[2]的值的时间;
        power_tight_mk_value = float(abs_area_psm_threshold[1])

        rtn_indicator[zone_name]['power_tight_hours'] = float(np.sum(zone_psm_arr < power_tight_mk_value))
        if zone_name == GLOBAL_QW_NAME:
            # load_curtail = result_dict['load_curtailment'].sum(axis=0)
            rtn_indicator[zone_name]['load_cutial_hours'] = float(
                np.sum(zone_psm_arr < -0.1))  # float(np.sum(load_curtail > 0.1))  # 缺电小时数
            rtn_indicator[zone_name]['load_cutial_electricity'] = round(
                float(zone_psm_arr[zone_psm_arr < 0].sum() * (-1)), 1)  # 缺口电量, 万千瓦时;
            if 'all' in ana_result:
                rtn_indicator[zone_name]['new_consump_rate'] = ana_result['all']  # 新能源消纳率
            if 'all' in ana_result_new_curtail_dict:
                rtn_indicator[zone_name]['new_cutailment_hours'] = ana_result_new_curtail_dict['all']
        elif zone_name in area_details:
            area_load_idx = area_details[zone_name]['load']
            if area_load_idx:
                # area_load_curtail = result_dict['load_curtailment'][area_load_idx, :].sum(axis=0)
                # 统计area_load_curtail总值>0.1的个数
                rtn_indicator[zone_name]['load_cutial_hours'] = float(
                    np.sum(zone_psm_arr < -0.1))  # float(np.sum(area_load_curtail > 0.1))  # 缺电小时数
                rtn_indicator[zone_name]['load_cutial_electricity'] = round(
                    float(zone_psm_arr[zone_psm_arr < 0].sum() * (-1)), 1)  # 缺口电量, 万千瓦时;
            if zone_name in ana_result:
                rtn_indicator[zone_name]['new_consump_rate'] = ana_result[zone_name]  # 新能源消纳率
            if zone_name in ana_result_new_curtail_dict:
                rtn_indicator[zone_name]['new_cutailment_hours'] = ana_result_new_curtail_dict[zone_name]
        else:
            rtn_indicator[zone_name]['load_cutial_hours'] = 0

        # 分区类型判断数据;
        rtn_indicator[zone_name]['powerSupplyTypes'] = rear_types_inds[zone_name]
    return rtn_indicator


def get_simulation_power_boundry(boundary_original: dict, timestep: int, area_name=GLOBAL_QW_NAME,
                                 area_details_dict: dict = dict(), config: dict = dict(), net=None):
    """
    获取全网、分区推演分析时的数据输入的最大最小值边界;介于装机容量之间; 以及 time_step时刻的边界数据;
    # 初始值可以直接从上级页面获取;无需额外获取;
    Args:
        boundary_original: 保供推演分析计算的全网/分区的边界数据--ana_result_dict['indicator']['boundary_data']
        timestep: 推演仿真的时刻序号
        area_name: 分区名称;
        area_details_dict: 如果area_name非全省,传入，用于获取分区关联主变: data['case_info']['zone']['']
        net: 网络拓扑;
    Returns:
        boundary_range:dict(): 推演分析的数值边界;
    # 'wind_output','solar_output','load','feedin','gas_output','dc_feedin','coal_output','hydro_output','nuclear_output'
    # min , max , step
    "original"：{},
    "input_range": {
        "wind_output": [
            0,
            1500,
            150.0
        ],
    }
    """
    # 边界具体参数;
    ele_boundaries_rate_dict = {
        "load": [0.5, 1.5],  # 负荷下限取最小值的0.5倍,上限取全年最大负荷的1.5倍 #[下边界系数,上边界系数]
        "gas": [0, 1.0],  # 燃气的上限为装机，下限为0
        "wind": [0, 1.5],  # 风电、光伏的下限为0，上限为装机（光伏的话包括营销部分）的1.5倍
        "solar": [0, 1.5],  # 光伏的上限为装机，下限为0
    }
    init_max_rate = 1.5  # 默认最大值系数;
    init_min_rate = 0.3  # 默认最小值系数;

    ele_simu_boundary_dict = {}
    if net is not None:
        # 使用考虑装机的边界系数;
        # gas装机;
        if area_name == GLOBAL_QW_NAME:
            gas_cap = net["gen"][(net["gen"]["type"] == "gas")]["max_p_mw"].values.sum()
        else:
            zone_genidx = area_details_dict[area_name]["gen"]
            gas_cap = net["gen"][(net["gen"]["type"] == "gas") & (net["gen"].index.isin(zone_genidx))][
                "max_p_mw"].values.sum()
        gas_p_max = ele_boundaries_rate_dict["gas"][1] * gas_cap
        # gas_p_min = ele_boundaries_rate_dict["gas"][0] * gas_cap
        ele_simu_boundary_dict["gas_output"] = {"max_p": round(float(gas_p_max * 0.1), 1), "min_p": 0.0}  # 万千瓦,最大值最小值;
        # wind/solar
        for i_ele in ["wind", "solar"]:
            if area_name == GLOBAL_QW_NAME:
                iele_cap = net[i_ele]["max_p_mw"].values.sum()
                if i_ele == "solar":
                    # 加上feedin中的营销分布式光伏数据;
                    iele_cap += net["feedin"][net["feedin"]["type"] == "营销分布式"]["max_p_mw"].values.sum()
            else:
                zone_iele_idx = area_details_dict[area_name][i_ele]
                iele_cap = net[i_ele].loc[zone_iele_idx, "max_p_mw"].values.sum()
                if i_ele == "solar":
                    zone_feedinidx = area_details_dict[area_name]["feedin"]
                    iele_cap += \
                        net["feedin"][
                            (net["feedin"].index.isin(zone_feedinidx)) & (net["feedin"]["type"] == "营销分布式")][
                            "max_p_mw"].values.sum()  # 加上feedin中的营销分布式光伏数据;
            iele_p_max = ele_boundaries_rate_dict[i_ele][1] * iele_cap
            ele_simu_boundary_dict[f"{i_ele}_output"] = {"max_p": round(float(iele_p_max * 0.1), 1),
                                                         "min_p": 0.0}  # 万千瓦,最大值最小值;
    if "load" in boundary_original[area_name]:
        load_max = ele_boundaries_rate_dict["load"][1] * max(boundary_original[area_name]["load"])
        load_min = ele_boundaries_rate_dict["load"][0] * min(boundary_original[area_name]["load"])
        ele_simu_boundary_dict["load"] = {"max_p": round(float(load_max), 1), "min_p": round(float(load_min), 1)}

    rtn_data = dict()
    boundary_original_area = {k: v for k, v in boundary_original[area_name].items()
                              if k in ['wind_output', 'solar_output', 'load', 'feedin', 'coal_output', 'gas_output',
                                       'nuclear_output', 'hydro_output', 'centralized_wind_output',
                                       'distributed_wind_output', 'storage',
                                       'centralized_solar_output', 'distributed_solar_output']}

    boundary_range = dict()  # 仿真推演的边界数据;
    original_dict = dict()  # 仿真推演的初值;
    for key in boundary_original_area.keys():
        if key in ele_simu_boundary_dict.keys():
            upper = ele_simu_boundary_dict[key]["max_p"]
            lower = ele_simu_boundary_dict[key]["min_p"]
        else:
            upper = int(round(init_max_rate * max(boundary_original_area[key]), 1))
            lower = int(round(init_min_rate * min(boundary_original_area[key]), 1))
        step_length = (upper - lower) / 10
        boundary_range[key] = [lower, upper, round(step_length, 1)]
        original_dict[key] = round(boundary_original_area[key][timestep], 1)

    boundary_range['dc_feedin'] = dict()
    original_dict['dc_feedin'] = dict()
    if area_name == '全省':
        dc_feedin_output = boundary_original[area_name]['feedin_detail_output']
        for key, val in dc_feedin_output.items():
            if isinstance(val, dict):
                boundary_range['dc_feedin'][key] = dict()
                original_dict['dc_feedin'][key] = dict()
                for item_k, item_v in val.items():
                    intege = int(round(init_max_rate * max(item_v), 1))
                    step_length = (intege - 0.) / 10
                    boundary_range['dc_feedin'][key][item_k] = [0, intege, step_length]
                    original_dict['dc_feedin'][key][item_k] = round(item_v[timestep], 1)
            else:
                intege = int(round(init_max_rate * max(val), 1))
                step_length = (intege - 0.) / 10
                boundary_range['dc_feedin'][key] = [0, intege, step_length]
                original_dict['dc_feedin'][key] = round(val[timestep], 1)
    else:
        original_dict['dc_feedin'] = dict()

    # 区内直流信息;
    boundary_range['dcline'] = dict()
    original_dict['dcline'] = dict()
    if "dcline" in boundary_original[area_name]:
        dcline_output = boundary_original[area_name]['dcline']
        if dcline_output:
            dcline_max = dict()
            for key in dcline_output.keys():
                dcline_max[key] = max(dcline_output[key])
                intege = int(round(dcline_max[key], 1))
                step_length = (intege - 0.) / 10
                boundary_range['dcline'][key] = [0, intege, step_length]
                original_dict['dcline'][key] = round(dcline_output[key][timestep], 1)

    # 网侧初始边界--获取名字就得知道net.trafo
    boundary_range['trafo_name'] = list()
    # if area_name != '全省':
    #     area_trafo_idx = pd.DataFrame(**area_details_dict[area_name]['trafo'])
    #     boundary_range['trafo_name'] = area_trafo_idx['name'].values.tolist()
    # else:
    #     boundary_range['trafo_name'] = []
    rtn_data['input_range'] = boundary_range

    # 当前模获取拟时刻的数据;
    rtn_data['original'] = original_dict

    # 当日负荷时序曲线(原始负荷/净负荷)
    timestep_hour = timestep // 24
    day_tm_start = timestep - timestep_hour
    td_ori_load = boundary_original[area_name]["load"][day_tm_start: day_tm_start + 24]
    payload = np.array(td_ori_load.copy())
    for i_new in ["wind", "solar"]:
        op_key = f"{i_new}_output"
        if op_key in boundary_original[area_name]:
            payload -= np.array(boundary_original[area_name][op_key][day_tm_start: day_tm_start + 24])
    rtn_data['load_data'] = {
        "load": td_ori_load,
        "payload": payload.round(1).tolist()
    }

    # 如果有直流,获取直流的显示名称与模型记录索引映射;
    if config and "dc_feedin_dict" in config.keys():
        rtn_data['dc_feedin_dict'] = config["dc_feedin_dict"]
    return rtn_data


def get_zone_trafo_capability_timestep(net, config: dict, result_dict: dict, net_ptdf=None, timestep: int = 0,
                                       area_name: str = None, area_details: dict = dict()):
    """
    获取分区的主变初始潮流数据
    net_ptdf: topo,功率分布因子
    返回dict:
    """

    zone_cap = get_trafo_capability_timestep(net, config=config, result_dict=result_dict, net_ptdf=net_ptdf,
                                             timestep=timestep, area_name=area_name, area_details=area_details)
    return zone_cap


def get_typical_run_mod(indicator_dict: dict):
    """
    获取全网典型方式时刻、特殊方式时刻;
    Args:
        indicator_dict: 系统指标计算分析结果dict
    Returns:
        dict
    """
    run_mod_dict = dict()
    if 'typical_mod' not in indicator_dict:
        raise ValueError("指标分析结果中缺少系统典型工况信息的数据")
    else:
        run_mod_dict['typical_mod'] = indicator_dict['typical_mod']

    if 'special_mod' not in indicator_dict:
        raise ValueError("指标分析结果中缺少系统特殊工况信息的数据")
    else:
        run_mod_dict['special_mod'] = indicator_dict['special_mod']

    return run_mod_dict


def get_power_output_data(output_h5file, grp_id: str = 'power_out', gen_data: list = None, time_idx: list = None):
    """
    获取指定时刻列表，指定类型的数据
    """
    datasets_dict = {}
    with h5py.File(output_h5file, 'r') as f:
        grp = f[grp_id]
        if gen_data is None:
            # 遍历group中的items（即datasets和子groups）
            for name, obj in grp.items():
                if isinstance(obj, h5py.Dataset):  # 检查对象是否是dataset（而不是子group或其他对象）
                    if not time_idx:
                        datasets_dict[name] = obj[:time_idx]  # 如果是dataset，读取数据并存入字典
                    else:
                        datasets_dict[name] = obj[:]
        else:
            for name, obj in grp.items():
                if name not in gen_data:
                    continue
                if isinstance(obj, h5py.Dataset):
                    if not time_idx:
                        datasets_dict[name] = obj[:time_idx]
                else:
                    datasets_dict[name] = obj[:]
    return datasets_dict


def get_grid_margin_data(net, config: dict, result_dict: dict, indicator_rlt_dict: dict, area_name=GLOBAL_QW_NAME):
    """
    获取分区月-日/月-日-时的供电不足分布二维码;--原始数据---分区供电裕度--时序数据; indicator_data--门槛值:indicator_thresholds
    config: 配置参数dict
    indicator_rlt_dict: case的指标分析结果dict
    valueType: 需要的数据： 'psm': 供电裕度; -1--无数据;0--供电裕度充足;1--供电裕度紧张;2--供电裕度不足(缺电)
    disseries: 是否考虑供电紧张;
    """
    if 'indicator_data' not in indicator_rlt_dict:
        raise ValueError("算例分析结果中无指标分析结果")

    if area_name not in indicator_rlt_dict['indicator_data']:
        raise ValueError(f"算例分析结果中无分区:{area_name}指标分析结果")

    zone_psm_dataseries = np.array(indicator_rlt_dict['indicator_data'][area_name]['power_supply_margin'])
    # power_supply_slack_arr = cal_sys_loadcutailment_val(config, result_dict)
    # # 使用power_supply_slack_arr修正zone_psm_dataseries
    # # 创建一个布尔数组，表示arr2中哪些元素大于0
    # mask = power_supply_slack_arr > 0.1
    # # 使用布尔数组来索引arr1，并将对应位置的元素设置为(-1) * arr2
    # zone_psm_dataseries[mask] = (-1) * power_supply_slack_arr[mask]  # < 0 ,缺电,2;  >0, < 320 紧张,1; 否则0

    # 计算调峰缺口;
    peaking_gap_arr = cal_sys_peaking_val(net, config, result_dict)

    # 获取时间范围
    _tm_df = get_time_range(config['start_time'], config['sum_snap'])
    # 计算年/月/日/时分布
    _tm_df['month'] = _tm_df.index.month
    _tm_df['day'] = _tm_df.index.day
    _tm_df['hour'] = _tm_df.index.hour

    # 生成index索引为时间的df
    _tm_df['peaking_gap'] = peaking_gap_arr
    _tm_df['powerSupply'] = zone_psm_dataseries  # 分区供电裕度; --- 万千瓦;

    # 转为key:value格式;
    balanec_mar_mon_dict = dict()

    # 按月、日聚合(用resample不用group)生成新的_tm_monthday_df，相同月/peaking_gap\power_supply_slack的最大值
    _tm_months = _tm_df['month'].unique().tolist()  # 月份;
    _tm_daily_max_df = _tm_df.resample('D').min()  # 裕度最小值;

    _tm_daily_max_df['peaking_gap_flag'] = _tm_daily_max_df['peaking_gap'].apply(lambda x: 1 if x > 0.1 else 0)
    # 分析计算裕度着色标志
    power_supply_anxious_mk = 450  # 供电裕度紧张门槛值
    _tm_daily_max_df['powerSupplyFlag'] = np.where(_tm_daily_max_df['powerSupply'] < 0, 2,
                                                   np.where((_tm_daily_max_df['powerSupply'] >= 0) & (
                                                           _tm_daily_max_df['powerSupply'] < power_supply_anxious_mk),
                                                            0,
                                                            0))  # 此处需要计及负荷备用的裕度，太小这个值永远达不到就开始削减负荷了
    # 按月group;
    _tm_grp_mon_df = _tm_daily_max_df.groupby(['month'])
    for i_m in _tm_grp_mon_df.groups.keys():
        # 对grp_i进行按'day'统计
        grp_i = _tm_grp_mon_df.get_group(i_m)
        n_days = len(grp_i['day'])  # days
        peakinggapFlag = grp_i['peaking_gap_flag'].values.astype(int).tolist()
        peakinggapVal = grp_i['peaking_gap'].values.astype(float).tolist()
        powerSupplyFlag = grp_i['powerSupplyFlag'].values.astype(int).tolist()
        powerSupplyVal = grp_i['powerSupply'].values.astype(float).tolist()
        balanec_mar_mon_dict[i_m] = list()  # 月31天数据,list()
        for i_day in range(1, n_days + 1):
            balanec_mar_mon_dict[i_m].append(
                {
                    "peakSlackFlag": peakinggapFlag[i_day - 1],
                    "peakSlackVal": peakinggapVal[i_day - 1],
                    "powerSupplyFlag": powerSupplyFlag[i_day - 1],
                    "powerSupplyVal": powerSupplyVal[i_day - 1]
                }
            )
        if n_days < 31:
            # 补充-1，补充数目： 31-n_days
            for i_day in range(1, 32 - n_days):
                balanec_mar_mon_dict[i_m].append(
                    {
                        "peakSlackFlag": -1,
                        "peakSlackVal": -1,
                        "powerSupplyFlag": -1,
                        "powerSupplyVal": -1
                    }
                )

    # 生成每月的日/小时数据矩阵
    _tm_df['powerSupplyFlag'] = np.where(_tm_df['powerSupply'] < 0, 2,
                                         np.where((_tm_df['powerSupply'] >= 0) & (
                                                 _tm_df['powerSupply'] < power_supply_anxious_mk), 0,
                                                  0))  # 此处需要计及负荷备用的裕度，太小这个值永远达不到就开始削减负荷了

    _tm_df['peaking_gap_flag'] = _tm_df['peaking_gap'].apply(lambda x: 1 if x > 0.1 else 0)

    balanec_mar_monday_dict = dict()
    _tm_grp_mon_day_df = _tm_df.groupby(['month'])
    # 遍历_tm_grp_mon_df
    for i_m in _tm_grp_mon_day_df.groups.keys():
        # 对grp_i进行按'day'分组
        grp_month = _tm_grp_mon_day_df.get_group(i_m)
        _tm_grp_day_df = grp_month.groupby(['day'])
        daykeys = list(_tm_grp_day_df.groups.keys())
        n_days = len(daykeys)  # num of days
        # 开始填充数据;
        balanec_mar_monday_dict[i_m] = list()
        for i_d in _tm_grp_day_df.groups.keys():
            # 对grp_i进行按'day'统计
            grp_day_i = _tm_grp_day_df.get_group(i_d)
            mar_monday_list = list()
            peakinggapFlag = grp_day_i['peaking_gap_flag'].values.astype(int).tolist()
            peakinggapVal = grp_day_i['peaking_gap'].values.astype(float).tolist()
            powerSupplyFlag = grp_day_i['powerSupplyFlag'].values.astype(int).tolist()
            powerSupplyVal = grp_day_i['powerSupply'].values.astype(float).tolist()
            timeIndex = grp_day_i['data'].values.astype(int).tolist()
            for i_h in range(0, 24):
                if i_h < 3:  # TODO: 江苏汇报后去除
                    mar_monday_list.append(
                        {
                            "peakSlackFlag": 0,
                            "peakSlackVal": 0,
                            "powerSupplyFlag": 0,
                            "powerSupplyVal": 0,
                            "timeIndex": timeIndex[i_h],
                        }
                    )
                else:
                    mar_monday_list.append(
                        {
                            "peakSlackFlag": peakinggapFlag[i_h],
                            "peakSlackVal": peakinggapVal[i_h],
                            "powerSupplyFlag": powerSupplyFlag[i_h],
                            "powerSupplyVal": powerSupplyVal[i_h],
                            "timeIndex": timeIndex[i_h],
                        }
                    )
            balanec_mar_monday_dict[i_m].append(mar_monday_list)
        # 不足31天的全部补-1
        if n_days < 31:
            for i_day in range(1, 32 - n_days):
                mar_monday_list_ext = list()
                for i_h in range(0, 24):
                    mar_monday_list_ext.append(
                        {
                            "peakSlackFlag": -1,
                            "peakSlackVal": -1,
                            "powerSupplyFlag": -1,
                            "powerSupplyVal": -1,
                            "timeIndex": -1
                        }
                    )
                balanec_mar_monday_dict[i_m].append(mar_monday_list_ext)
    return balanec_mar_mon_dict, balanec_mar_monday_dict


def get_sys_case_boundary(network, config: dict, case_info: dict, result_dict: dict):
    """
    获取机组装机/设备数目等信息;
    :param case_id: 算例id
    :param result: 返回值
    :return:
    """
    result_data = dict()

    # 获取机组设备数目信息
    dev_number_data = get_devices_number(case_info_dict=case_info)
    result_data['deviceNum'] = dev_number_data

    # 获取机组装机容量信息
    result_data['genCapacity'] = get_allgen_capacity(case_info_dict=case_info)

    time_indx = case_info.get("time_range", [])
    # 获取区外来电功率时序
    feedin_series = get_result_power_curve_series(
        result_dict=result_dict, key_tab='feedin_output', case_net=network, to_sum=True
    )
    result_data['feedinPower'] = {
        "time": time_indx,
        "value":
            np.array(feedin_series).round(1).tolist()  # 兆瓦;
    }

    # 负荷侧--总最大负荷--每月最大负荷/用电量
    load_series = get_result_power_curve_series(
        result_dict=result_dict, key_tab='load'
    )
    load_curtailment = get_result_power_curve_series(
        result_dict=result_dict, key_tab='load_curtailment'
    )
    load_series = (np.array(load_series) + np.array(load_curtailment)) * 0.1  # 万千瓦

    maxLoad = np.nanmax(load_series)

    load_df = pd.DataFrame({"load": load_series}, index=pd.to_datetime(time_indx))
    # 将load_df 按月resample，计算每月最大负荷
    load_month_df = load_df.resample('M').max()
    load_electicity_month_df = load_df.resample('M').sum()

    result_data['loadInfo'] = {
        "maxLoad": float(maxLoad) * 1e-4,  # 亿千瓦
        "maxLoadMonths": load_month_df["load"].values.tolist(),
        "electricityMonths": load_electicity_month_df["load"].values.tolist()
    }
    return result_data


def get_curve_series_data(result_dict: dict, key_tab: str, case_net):
    """
    获取结果指定时序,并按规则进行排序;
    result_dict: teap的时序结果
    key_tab: 结果result_dict中的key
    case_net: case的net
    to_sum: 是否进行sum(axis=0)
    """
    if key_tab not in _output_dict.keys():
        raise KeyError(f"input key:{key_tab} error!")
    rlt_arrs = get_result_power_curve_series(result_dict=result_dict, key_tab=key_tab, case_net=case_net, to_sum=False)

    ele_key = _output_dict[key_tab]
    ele_allnames = case_net[ele_key]["name"].values

    result_dict = ddeal.series_coldelta_sorted(ele_allnames, rlt_arrs, sort_by="diff")
    return result_dict


def get_case_series_data(case_net, config: dict, ele_name: str, series_type: str):
    """
    获取算例自身相关的时序,并按规则进行排序;
    case_net: case的net
    ele_name: 设备表名
    series_type: 时序类型key: 如:"load","feedin","gen_on_off","maintenance"d等;
    """
    if ele_name not in case_net.keys():
        raise KeyError(f"input key:{ele_name} error!")

    # 检查设备绑定的时序;
    seriestype_arrs = CTeapCase.get_ele_key_series(case_net, config=config, ele_=ele_name, timeseries_key=series_type)
    seriestype_arrs = np.nan_to_num(seriestype_arrs, nan=0)  # 处理nan值

    ele_allnames = case_net[ele_name]["name"].values  # 设备;
    result_dict = ddeal.series_coldelta_sorted(ele_allnames, seriestype_arrs, sort_by="sum")

    return result_dict


def get_zone_elements_and_inds(net, config, area_details_dict: dict = dict(),
                               indicator_rlt_dict: dict = dict(), save_dir: str = ""):
    """
    获取所有分区的关联设备以及供电裕度相关指标时序结果及计算基础数据
    :param net:
    :param config:
    :param result_dict:
    :param area_name:
    :param area_details_dict:
    :param indicator_rlt_dict:
    :param save_dir:
    :return:
    """
    # 获取当前时间
    from datetime import datetime
    formatted_time = (datetime.now()).strftime("%Y-%m-%d_%H%M%S")

    ele_type_list = ["bus", "gen", "hydropower", "wind", "solar", "line", "trafo", "load", "feedin", "stogen"]
    if not os.path.exists(save_dir):
        # 循环创建多层目录
        try:
            os.makedirs(save_dir)  # 创建目录qs_path
        except:
            raise ValueError(f"无法创建目录: {save_dir}")

    # 生成data时序;
    case_start_time = config['start_time']
    num_snaps = config['sum_snap']
    _tm_df = get_time_range(case_start_time, num_snaps, freq='h')

    for area, area_ele_dict in area_details_dict.items():
        print(f"export zone:{area}")
        area_ele_file_name = os.path.join(save_dir, f"{area}_elements_{formatted_time}.xlsx")
        area_ind_file_name = os.path.join(save_dir, f"{area}_indicators_{formatted_time}.xlsx")

        # 分区设备
        writer = pd.ExcelWriter(area_ele_file_name)
        for ele_type in ele_type_list:
            if ele_type not in area_ele_dict or len(net[ele_type]) == 0:
                ele_df = pd.DataFrame(columns=["name"])
                ele_df.to_excel(writer, sheet_name=ele_type)
            else:
                ele_df = net[ele_type].loc[area_ele_dict[ele_type]]
                if ele_type == "feedin":
                    # 将营销分布式的光伏从feedin中提到'solar'中
                    ele_df = ele_df[~ele_df["type"].isin(["营销分布式", "分布式"])]
                    ele_df.to_excel(writer, sheet_name=ele_type)
                    # 原分区内光伏 + 营销分布式光伏
                    feedin_solar_df = ele_df[ele_df["type"].isin(["营销分布式", "分布式"])]
                    if not feedin_solar_df.empty:
                        feedin_solar_df = feedin_solar_df["name", "bus", "max_p_mw"]
                        ele_solar_df = net["solar"].loc[area_ele_dict["solar"]]
                        ele_solar_df = pd.concat([ele_solar_df, feedin_solar_df], ignore_index=False)
                        ele_solar_df.to_excel(writer, sheet_name="solar")
                else:
                    # 有些负荷被剔除了;
                    ele_df.to_excel(writer, sheet_name=ele_type)
        writer._save()

        # 分区供电裕度结果;
        writer2 = pd.ExcelWriter(area_ind_file_name)
        # 生成df
        area_psm_base_data = np.array(indicator_rlt_dict["indicator_base_data"][area]["power_supply_margin"])
        _tm_psm_df = pd.DataFrame({
            "主变供电能力": area_psm_base_data[:, 0].round(2),
            "常规容量加储能区内直流": area_psm_base_data[:, 1].round(2),
            "风光": area_psm_base_data[:, 2].round(2),
            "实际原始负荷": area_psm_base_data[:, 3].round(2),
            "供电裕度": area_psm_base_data[:, 4].round(2),
        }, index=_tm_df.index)
        _tm_area_df = _tm_df.copy()
        _tm_area_df = pd.concat([_tm_area_df, _tm_psm_df], axis=1)
        _tm_area_df.to_excel(writer2, sheet_name="psm")
        writer2._save()
    return


def get_zone_newrate_and_genoncapacity(net, result_dict: dict, time_list: list = list()):
    """
    获取新能源的发电同时率;常规机组的开机容量、以及负荷;
    :return:
    """
    data_dict = {
        "newenergy": {
            "capacity": 0.0,
            "p_rate": [],
        },
        "wind": {
            "capacity": 0.0,
            "p_rate": [],
        },
        "solar": {
            "capacity": 0.0,
            "p_rate": [],
        },
        "gen": {
            "on_capacity": [],
        },
        "load": []
    }
    if "load" in result_dict:
        data_dict["load"] = result_dict["load"][:, time_list].sum(axis=0) * 0.1  # 万千瓦;
        data_dict['load'] = data_dict['load'].tolist()

    num_len = len(time_list)
    new_cap = 0
    new_output = np.zeros(num_len)
    for i_ele in ["wind", "solar"]:
        rlt_key = f"{i_ele}_output"
        if rlt_key in result_dict:
            ele_capacity = net[i_ele]["max_p_mw"].sum()
            data_dict[i_ele]["capacity"] = ele_capacity * 0.1
            new_cap += ele_capacity
            ele_output = result_dict[rlt_key][:, time_list].sum(axis=0)  # MW
            data_dict[i_ele]["p_rate"] = (ele_output / ele_capacity).round(4).tolist()
            new_output += ele_output

    if new_cap > 0:
        data_dict["newenergy"]["capacity"] = new_cap * 0.1
        data_dict["newenergy"]["p_rate"] = (new_output / new_cap).round(4).tolist()

    # 开机容量统计
    if len(net["gen"]) > 0:
        gen_state = result_dict["gen_state"][:, time_list]  # on_off
        gen_on_cap = gen_state * net["gen"]["max_p_mw"][:, np.newaxis]
        data_dict["gen"]["on_capacity"] = (gen_on_cap.sum(axis=0) * 0.1).tolist()  # 万千瓦;

    return data_dict


if __name__ == '__main__':
    # case_id = '2123214455656766'
    # case_rlt_dir = os.path.join(basedir, "result_data", f"case_id_{case_id}")
    #
    # case_info_path = os.path.join(case_rlt_dir, f"basic_info_{case_id}.json")
    # with open(case_info_path, 'r') as file:
    #     # 使用json.load()方法读取文件内容并转换为Python字典
    #     case_info_dict = json.load(file)
    # #
    # data_get = CDataToServ()
    # gen_cap_dict = data_get.get_allgen_capacity(case_info_dict)

    ## test2
    output_h5file = 'D:\\code\\baogong_new\\result_data\\case_id_2123214455656766\\rlt_power_output_2123214455656766.hdf5'
    # 打开HDF5文件以读取模式
    import time
    import pickle

    start_time = time.time()

    # datasets_dict = get_power_output_data(output_h5file, 'power_out')

    # 载入case的net
    # net_file = 'D:\\code\\baogong_new\\result_data\\case_id_2123214455656766\\case_net_2123214455656766.p'
    # case_net1 = pp.from_pickle(net_file)  # absolute path

    # net_file = 'D:\\code\\baogong_new\\result_data\\case_id_2123214455656766\\case_net_2123214455656766.json'
    # case_net1 = pp.from_json(net_file)  # absolute path

    # 使用pickle加载网络
    net_file = 'D:\\code\\baogong_new\\result_data\\case_id_2123214455656766\\case_net_2123214455656766.pkl'
    with open(net_file, 'rb') as f:
        loaded_net = pickle.load(f)
    case_gen = loaded_net['gen']

    execution_time = (time.time() - start_time) * 1000  # 转换为毫秒
    print(f"h5py read h5file 代码块执行时间: {execution_time:.3f} 毫秒")

    # 识别供电分区测试
    import pandapower.topology as top

    # pp.plotting.simple_plot(loaded_net)
    load_buses = np.array(loaded_net.load['bus'].values)
    trafo_buses = np.array(
        loaded_net.trafo['hv_bus'].values.tolist() + loaded_net.trafo['lv_bus'].values.tolist())
    mg = top.create_nxgraph(loaded_net)  # converts example network into a MultiGraph
    area_dict = dict()
    area_num = 0
    for area in top.connected_components(mg, notravbuses=set(loaded_net.bus[loaded_net.bus.vn_kv >= 500].index)):
        # 区内有负荷&有主变的才算一个分区;过滤没有负荷或主变的集合;
        area_arr = np.array(list(area))
        load_in_area = np.in1d(area_arr, load_buses)
        trafo_in_area = np.in1d(area_arr, trafo_buses)
        if not list(area_arr[load_in_area]) or not list(area_arr[trafo_in_area]):
            # 分区没有负荷和主变
            continue
        elif len(area) <= 3:
            # 分区节点数目太少< 4个
            continue
        else:
            area_num += 1
            area_dict[area_num] = list(area)
    print(f"识别分区数目:{area_num}")
    for key, val in area_dict.items():
        print(val)
