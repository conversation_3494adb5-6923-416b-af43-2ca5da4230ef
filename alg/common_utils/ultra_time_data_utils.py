"""
任意起止时间段的数据统计分析;
"""
import numpy as np
from alg.cal_ssmetrics.case_info_statistics import (funcGetmax_p_mw, caldaliyload_peak_valley_max)
from alg.common_utils.data_utils import (get_grid_extreme_value_info)
from alg.cal_ssmetrics.base_func import (result_menu_name_dict, _output_dict)


def get_grid_extreme_value_info_with_time(net, config, case_series_dict: dict, cal_result_dict: dict, time_start: int,
                                          cal_num: int = 1):
    """
    获取任意时间段内的首页系统极值信息
    # 直接生成全时序,然后抽取需要时刻的数据进行统计分析;load\load_curtailment\wind_output\wind_curtailment\solar_curtailment\solar_output
    net:系统网络
    config: 配置数据;
    result: 结果数据;
    case_series：算例信息dict的时序;--statistics_dict['series']-- func:get_devs_series_arrs
    cal_result_dict: 结果分析dict
    """
    statistics_dict = dict()
    statistics_dict['max_val'] = dict()
    statistics_dict['ori_elec'] = dict()

    time_end = time_start + cal_num
    case_series_select = dict()
    for i_key, val in case_series_dict.items():
        case_series_select[i_key] = np.array(val[time_start:time_end])

    # 极值
    for i_ele in ["load", "wind", "solar"]:
        ele_maxp_dict, ele_eleric_dict = funcGetmax_p_mw(net, config, i_ele, case_series_select, time_start)
        statistics_dict['max_val'].update(ele_maxp_dict)
        statistics_dict['ori_elec'].update(ele_eleric_dict)

    # 计算日最大峰谷差
    load_delta_max = caldaliyload_peak_valley_max(config, case_series_select["load"].sum(axis=0),
                                                  start_time_no=time_start, snapshot=cal_num)
    statistics_dict['max_val']['load_peak_valley'] = load_delta_max

    # 缺口信息
    # 计算调峰缺口
    payload_arr = np.array(cal_result_dict['peaking'][time_start:time_end])
    peak_gap_max_idx = np.argmax(payload_arr)  # 调峰困难缺口最大值索引
    max_peaking_gap = round(float(payload_arr[peak_gap_max_idx]), 1)  # 万千瓦,1位小数

    # 供电缺口
    load_curtailment_arr = np.array(cal_result_dict['powerSupply']["series_data"][time_start:time_end])  # 供电缺口
    max_index = np.argmax(load_curtailment_arr)  # 负荷缺口最大值索引
    max_load_curtail = round(float(load_curtailment_arr[max_index]), 1)  # 万千瓦,1位小数

    tm_cal_result_dict = dict()
    tm_cal_result_dict['max_val'] = dict()
    tm_cal_result_dict['max_val']['power_slack'] = dict()
    tm_cal_result_dict['max_val']['power_slack']['peaking'] = [max_peaking_gap, int(peak_gap_max_idx) + time_start]
    tm_cal_result_dict['max_val']['power_slack']['supply_power'] = [max_load_curtail, int(max_index) + time_start]

    tm_cal_result_dict["powerSupply"] = dict()
    tm_cal_result_dict["powerSupply"]["series_data"] = load_curtailment_arr  # 供电缺口

    # 新能源消纳率计算数据
    tm_cal_result_dict["consump_rate"] = dict()
    tm_cal_result_dict["consump_rate"]["city"] = dict()
    tm_cal_result_dict["consump_rate"]["city"]["all"] = dict()
    tm_cal_result_dict["consump_rate"]["city"]["all"]["newenergy_proportion"] = 1.0  # 新能源消纳率--无新能源默认1.0
    # wind/solar/wind_curtailment/solar_curtailment
    newenergy_output = 0  # 新能源消纳
    newenergy_curtailment = 0  # 新能源弃电
    if config['n_element']['wind'] > 0:
        newenergy_output += np.array(cal_result_dict["line_curve_data"]["wind_output"][time_start:time_end]).sum()
        newenergy_curtailment += np.array(
            cal_result_dict["line_curve_data"]["wind_curtailment"][time_start:time_end]).sum()
    if config['n_element']['solar'] > 0:
        newenergy_output += np.array(cal_result_dict["line_curve_data"]["solar_output"][time_start:time_end]).sum()
        newenergy_curtailment += np.array(
            cal_result_dict["line_curve_data"]["solar_curtailment"][time_start:time_end]).sum()

    newenergy_total = newenergy_output + newenergy_curtailment
    if newenergy_total > 0.01:
        newenergy_proportion = round(float(newenergy_output / newenergy_total), 3)
        tm_cal_result_dict["consump_rate"]["city"]["all"]["newenergy_proportion"] = newenergy_proportion  # 新能源消纳率
    else:
        pass

    # 负荷统计
    tm_cal_result_dict["line_curve_data"] = dict()
    tm_cal_result_dict["line_curve_data"]["load_data"] = cal_result_dict["line_curve_data"]["load_data"][
                                                         time_start:time_end]  # 负荷时序

    grid_extreme_dict = get_grid_extreme_value_info(statistics_dict, tm_cal_result_dict)

    return grid_extreme_dict


def get_curve_device_list(net, result_dict: dict):
    """
    获取查询曲线信息时的设备列表;
    """
    rtn_data = {
        "line_type_map": [{"id": "power_output", "menu_name": "功率曲线"},
                          {"id": "on_off_state", "menu_name": "启停状态"}],
        "equipment_type_map": {"on_off_state": [{"id": "gen_state", "menu_name": result_menu_name_dict["gen_state"]}],
                               "power_output": [{"id": "gen_output", "menu_name": "常规机组出力"},
                                                {"id": "wind_output", "menu_name": "风电出力"},
                                                {"id": "wind_curtailment", "menu_name": "弃风功率"},
                                                {"id": "solar_output", "menu_name": "光伏出力"},
                                                {"id": "solar_curtailment", "menu_name": "弃光功率"},
                                                {"id": "feedin_output", "menu_name": "区外来电"},
                                                {"id": "hydropower_output", "menu_name": "水电出力"},
                                                {"id": "stogen_output", "menu_name": "储能/抽蓄出力"},
                                                {"id": "load_output", "menu_name": "负荷功率"}]
                               },
        "device_detail": dict(),
    }
    # 查询各个类型设备的列表
    ele_type_list = ["gen_state", "gen_output", "wind_output", "wind_curtailment", "solar_output", "solar_curtailment",
                     "feedin_output", "hydropower_output", "stogen_output", "load_output"]
    device_detail_dict = dict()
    for i_type in ele_type_list:
        device_detail_dict[i_type] = list()
        if i_type not in result_dict.keys():
            pass
        else:
            ele_key = _output_dict[i_type]
            if ele_key == "gen":
                name_data = (net[ele_key]['name'].astype(str) + ':' + net[ele_key]['type'].astype(
                    str)).to_dict()
                device_detail_dict[i_type] = [(key, value) for key, value in name_data.items()]
            else:
                name_data = (net[ele_key]['name'].astype(str)).to_dict()
                device_detail_dict[i_type] = [(key, value) for key, value in name_data.items()]
    rtn_data["device_detail"] = device_detail_dict

    return rtn_data


def get_device_curve_info(result_dict: dict, device_select_dict: dict):
    """
    获取设备曲线信息
    device_select_dict:{"gen_output":[{"0":"gen1"},{"1":"gen2"}]}
    result_dict: 结果数据;
    """
    rtn_data = dict()
    for i_type in device_select_dict.keys():
        rtn_data[i_type] = list()
        if i_type not in result_dict.keys():
            pass
        else:
            for i_ele in device_select_dict[i_type]:
                for i_key, ele_val in i_ele.items():
                    ele_data = {ele_val: result_dict[i_type][int(i_key), :].tolist()}
                    rtn_data[i_type].append(ele_data)
    return rtn_data
