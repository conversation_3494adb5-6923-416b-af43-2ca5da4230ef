import h5py
import pickle
import json
import shelve
import numpy as np
import pandas as pd
from alg.common_utils.data_utils import get_simulation_power_boundry
import alg.common_utils.calculator_view as cal_view


def read_json_file(file_path):
    with open(file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    return data


def read_h5_group(file_path, group_name):
    # 打开HDF5文件
    with h5py.File(file_path, 'r') as f:
        # 获取指定组
        group = f[group_name]

        # 初始化一个空字典来存储数据集
        datasets = {}

        # 遍历组中的每个键（它们应该是数据集或子组）
        for key in group.keys():
            # 检查是否为数据集（而不是子组）
            if isinstance(group[key], h5py.Dataset):
                # 读取数据集并添加到字典中
                datasets[key] = group[key][:]  # [:] 用于读取整个数据集
    return datasets


def select_n1_fault_elements(net, vn_kv: float = 500):
    """
    选取需要进行n1扫描的故障设备(线路/主变),双回线路只扫一回(选取x/limit小的，大的支路会先违背潮流约束)
    :return:
    所有需要扫描的设备索引;
    line: trafo
    """
    line500_list = net["line"][net["line"]["vn_kv"] >= vn_kv].index.tolist()
    trafo500_list = net["trafo"][net["trafo"]["vn_hv_kv"] >= vn_kv].index.tolist()

    n_line = len(net["line"])
    n1_ele_dict = {
        "line": line500_list,
        "trafo": (np.array(trafo500_list) + n_line).astype(int).tolist(),  # 转为ptdf中的设备索引;ptdf=line+trafo
    }
    return n1_ele_dict


def get_ele_num_rate(rtn_data, ele_name: str = "line"):
    """

    :return:
    """
    ele_data = rtn_data["chart_dict"][ele_name]

    # 获取list(ele_data["p_rate"])中的重载值在[0.8,1.0)之间的数目
    array = np.array(ele_data["p_rate"])
    heavy_num = np.sum((array >= 0.8) & (array < 0.9999))
    heavy_rate = round(float(heavy_num / len(ele_data["p_rate"])), 5)

    over_num = np.sum((array >= 0.9999))
    over_rate = round(float(over_num / len(ele_data["p_rate"])), 5)

    return heavy_num, heavy_rate, over_num, over_rate


if __name__ == '__main__':
    import warnings

    warnings.filterwarnings("ignore", category=RuntimeWarning)

    # 载入net文件;
    net_file = 'D:\\code\\baogong_sys\\baogong-api\\alg\\result_data\\case_id_2123214455656766\\case_net_2123214455656766.pkl'
    with open(net_file, 'rb') as f:
        loaded_net = pickle.load(f)

    # 载入计算结果算例的json文件;
    caseinfo_file = 'D:\\code\\baogong_sys\\baogong-api\\alg\\result_data\\case_id_2123214455656766\\basic_info_2123214455656766.json'
    result_info_file = 'D:\\code\\baogong_sys\\baogong-api\\alg\\result_data\\case_id_2123214455656766\\cal_result_2123214455656766.json'
    indicator_rlt_file = 'D:\\code\\baogong_sys\\baogong-api\\alg\\result_data\\case_id_2123214455656766\\indicators_cal_2123214455656766.json'
    power_flow_file = 'D:\\code\\baogong_sys\\baogong-api\\alg\\result_data\\case_id_2123214455656766\\power_flow_2123214455656766.hdf5'
    rlt_power_output_file = 'D:\\code\\baogong_sys\\baogong-api\\alg\\result_data\\case_id_2123214455656766\\rlt_power_output_2123214455656766.hdf5'

    # 读取数据
    caseinfo_file_pkl = 'D:\\code\\baogong_sys\\baogong-api\\alg\\result_data\\case_id_2123214455656766\\basic_info_2123214455656766.db'
    with shelve.open(caseinfo_file_pkl) as shelf:
        caseinfo_dict = shelf['data']

    indicator_rlt_file_pkl = 'D:\\code\\baogong_sys\\baogong-api\\alg\\result_data\\case_id_2123214455656766\\indicators_cal_2123214455656766.db'
    with shelve.open(indicator_rlt_file_pkl) as shelf:
        indicator_rlt_dict = shelf['data']

    config = caseinfo_dict['config']
    area_details = caseinfo_dict['zone']['device_relay']

    # 载入结果dict, h5文件;
    rlt_power_output_dict = read_h5_group(rlt_power_output_file, 'power_out')
    power_flow_dict = read_h5_group(power_flow_file, 'power_flow')
    result_init_data = {**rlt_power_output_dict, **power_flow_dict}

    # 推演的输入数据格式
    user_input_data = {
        "case_scenerio": "jiangsu",
        "calculate_flag": True,
        "case_id": "f305f13f-255a-4188-b199-d1e1bb0d8606",
        "timestep": "",
        "load": 0,
        "gas": 0,
        "wind": 0,
        "solar": 0,
        "feedin": 0,
        "dcline": dict(),  # 区内直流情况
        "device_name": [],  # n-1故障设备索引;
        "filter_name": "全省",
        "phase_shifter_arg": {},
        "add_reactance_arg": {},
        "dc_feedin": {}
    }

    # 推演时刻:
    extreme_year_time_idx = [28, 174, 660, 916, 940, 1307, 1416, 1481, 2076, 2401, 2867, 3060, 3330, 3374, 3481, 3733,
                             4058, 4475, 4477, 4720, 4852, 5126, 5317, 5387, 5418, 5893, 6011, 6266, 6780, 7276, 7547,
                             7756, 8627, 8692]
    extreme_smallyear_time_idx = []

    # 仿真推演开始
    result_cols = ["timestamp", "fault_ele_type", "fault_ele_idx", "power_supply_margin", "ramp_cap_upward",
                   "feedin_dependence", "non_inertia_penetration", "peak_shaving", "reserve_low", "line_heavy_num",
                   "line_heavy_rate", "line_over_num", "line_over_rate", "trafo_heavy_num", "trafo_heavy_rate",
                   "trafo_over_num", "trafo_over_rate", "inf_heavy_num", "inf_heavy_rate", "inf_over_num",
                   "inf_over_rate"]
    n1_result_df = pd.DataFrame(columns=result_cols)  # 结果文件;
    i_time = 0
    for i_no, i_val in enumerate(extreme_year_time_idx):
        i_time += 1
        if i_time % 10 == 0:
            n1_result_df.to_csv(f"./n1_analysis_result_{i_time}.csv")
            n1_result_df = pd.DataFrame(columns=result_cols)  # 结果文件;
        print(f"开始推演第{i_no}个时间点:{i_val}")
        # 获取推演初始数据;
        data = get_simulation_power_boundry(
            boundary_original=indicator_rlt_dict["boundary_data"], area_name=user_input_data["filter_name"],
            timestep=int(i_val)
        )

        # 赋值初始结果;
        user_input_data["timestep"] = str(i_val)
        user_input_data["wind"] = data["original"]["wind_output"]
        user_input_data["solar"] = data["original"]["solar_output"]
        user_input_data["load"] = data["original"]["load"]
        user_input_data["gas"] = data["original"]["gas_output"]
        user_input_data["dcline"] = data["original"]["dcline"]
        user_input_data["feedin"] = data["original"]["feedin"]
        dc_feedin_detail = data["original"]["dc_feedin"]
        if dc_feedin_detail:
            for key, val in dc_feedin_detail.items():
                if isinstance(val, dict):
                    user_input_data["dc_feedin"][key] = list(val.values())
                else:
                    user_input_data["dc_feedin"][key] = val

        # 测试推演改的数据;

        # 如果需要做n-1选,取n-1的故障分析设备(双回线路取一回,电压等级500kV,500kV主变/线路)
        # 将保供六维指标保存; 且计算线路过载/重载的概率，重载线路/总线路数目, 过载线路/总线路数目;
        # 保存为df,col: '时刻','故障设备类型','故障设备序号','保供六维指标','线路重载数目','线路重载概率','线路过载数目','线路过载概率','主变重载数目','主变重载概率','主变过载数目','主变过载概率'
        n1_ele_dict = select_n1_fault_elements(loaded_net)
        print(f"总n1设备数目:{len(n1_ele_dict['line']) + len(n1_ele_dict['trafo'])}")
        for key, val in n1_ele_dict.items():
            idx_delta = 0 if key == "line" else len(loaded_net["line"])
            i_no = 0
            if isinstance(val, list):
                for idx in val:
                    i_no += 1
                    print(f"开始第：{i_no} 个设备索引:{idx} 的n-1分析")
                    user_input_data["device_name"] = [idx]
                    rtn_data = cal_view.run_calculator_func(user_input_data, int(user_input_data["timestep"]),
                                                            result_init_data, loaded_net,
                                                            config, indicator_rlt_dict,
                                                            area_details)  # 执行推演

                    # 统计设备重过载情况;
                    line_heavy_num, line_heavy_rate, line_over_num, line_over_rate = get_ele_num_rate(rtn_data, "line")
                    trafo_heavy_num, trafo_heavy_rate, trafo_over_num, trafo_over_rate = get_ele_num_rate(rtn_data,
                                                                                                          "trafo")
                    inf_heavy_num, inf_heavy_rate, inf_over_num, inf_over_rate = get_ele_num_rate(rtn_data,
                                                                                                  "interface")

                    # 获取分区裕度最小值
                    zone_power_supply_margin = rtn_data["qs_fenqu_psm_value"]  # dict
                    zone_margin_min_key = min(zone_power_supply_margin, key=zone_power_supply_margin.get)
                    zone_margin_min_value = zone_power_supply_margin[zone_margin_min_key]

                    rtn_data_series = pd.Series({
                        "timestamp": int(user_input_data["timestep"]),
                        "fault_ele_type": key,
                        "fault_ele_idx": idx - idx_delta,
                        "power_supply_margin": rtn_data["baogongliuwei_nominal"]["power_supply_margin"],
                        "ramp_cap_upward": rtn_data["baogongliuwei_nominal"]["ramp_cap_upward"],
                        "feedin_dependence": rtn_data["baogongliuwei_nominal"]["feedin_dependence"],
                        "non_inertia_penetration": rtn_data["baogongliuwei_nominal"]["non_inertia_penetration"],
                        "peak_shaving": rtn_data["baogongliuwei_nominal"]["peak_shaving"],
                        "reserve_low": rtn_data["baogongliuwei_nominal"]["reserve_low"],
                        "zone_min_power_supply_margin": zone_margin_min_value,
                        "min_power_supply_zone": zone_margin_min_key,
                        "line_heavy_num": line_heavy_num,
                        "line_heavy_rate": line_heavy_rate,
                        "line_over_num": line_over_num,
                        "line_over_rate": line_over_rate,
                        "trafo_heavy_num": trafo_heavy_num,
                        "trafo_heavy_rate": trafo_heavy_rate,
                        "trafo_over_num": trafo_over_num,
                        "trafo_over_rate": trafo_over_rate,
                        "inf_heavy_num": inf_heavy_num,
                        "inf_heavy_rate": inf_heavy_rate,
                        "inf_over_num": inf_over_num,
                        "inf_over_rate": inf_over_rate
                    })
                    n1_result_df.loc[len(n1_result_df)] = rtn_data_series
    n1_result_df.to_csv("./n1_analysis_result.csv")
    pass
