"""
@bref 实时推演分析计算功能与服务模块的对接接口--配合(run_ss_calculator.py);
#### <font color="red">请求参数样例</font>
```json
"""
import numpy as np
from alg.ProductionCostSimulation.run_calculator_utils.run_ss_calculator import (ss_calculator)
from alg.common_utils.data_utils import get_simulation_power_boundry


def run_calculator_func(user_input_data: dict, timestep, case_result_dict: dict, case_net, config,
                        indicator_rlt_dict: dict, area_details: dict = dict()):
    """
    开始实时推演分析计算接口;  推演页面载入信息接口 和执行推演接口 独立; 本接口只执行推演分析功能;
    ## 解析传入的数据是否与原值有变化，如果没有变化，直接返回原结果，无需重新计算;否则开始计算;
    # 推演始终针对全网进行全局的潮流计算; 只不过在调整时,如果选择的是分区，则出力边界变化只在分区内的设备中进行;
    user_input_data = {
        'calculate_flag': True or False,    #是否需要计算
        'case_id': '132132321',     # case_id
        'timestep':100,    #推演时刻
        'device_name':[],    # 故障设备索引list, 该索引为 ptdf计算时的设备索引;line_idx + trafo_idx;
        'feedin':367,   # feedin: 整个feedin的目标值(非用户直接调节,=dc_feedin中的sum());= ori_val + dc_feedin中变化值的和;
        'dc_feedin':{},     #ctl-test0240513-增加字段--区外交直流的精细化目标量;{"name": val}--配合配置文件中的直流配置config_extern.py
        'filter_name': '全省', '全省' #  or '分区名'
        'gas':100,    #燃气机组总目标值
        'load':100,   # 负荷总目标值
        'wind':400,   # 风电总目标值
        'solar':100,   # 光伏总目标值
        'hydro':100, #水电(gen表中的径流式水电)
        'phase_shifter_arg':dict(), #移相器
        # 目前'coal'、'nuclear'不支持可调--被动跟踪平衡;
        'area_or_region_choice':[], # !!支持目标调整出力在分区内调整:
    }
    Args:
        user_input_data: 推演分析用户调整输入的数据(负荷/风光/机组/直流等新调整后的数据)
        timestep:int, 推演时刻
        case_result_dict: 输出的初始机组、负荷等基础数据;--data['result_output']
        case_net: 系统网络， data['network']
        config: 仿真参数, data['config']
        indicator_rlt_dict: 初始计算的全网及分区的指标结果及指标计算基础数据结果dict, data['indicator']
        zone_name: 推演的分区名
        area_details = dict()  # 分区关联设备索引--data['case_info']['zone']['device_relay']
    Returns:
        dict():
    """
    area_name = user_input_data['filter_name']  # 推演的分区名称;默认是"全省"
    if area_name != '全省' and area_name not in area_details:
        raise Exception(f'分区名称:{area_name},在分区关联设备索引中不存在,请检查!')

    # 重新组织输入的dict--都是总的设备目标值--输入的都是万千瓦;
    new_weather_para = {"case_scenerio": user_input_data['case_scenerio'] if 'case_scenerio' in user_input_data else "",
                        "calculate_flag": user_input_data['calculate_flag'],
                        "timestep": int(user_input_data['timestep']),
                        "wind_output": user_input_data['wind'],
                        "solar_output": user_input_data['solar'],
                        "load": user_input_data['load'],
                        "gas_output": user_input_data['gas'] if 'gas' in user_input_data else 0,
                        "hydro_output": user_input_data['hydro_output'] if 'hydro_output' in user_input_data else -1.0,
                        "feedin": user_input_data['feedin'],
                        # feedin的具体调节信息 "dc_feedin": {"灵宝": 517,"中州换": 16,"豫南特": 39,"豫南换": 39}
                        "dc_feedin": user_input_data['dc_feedin'] if 'dc_feedin' in user_input_data else {},
                        # 目标调整出力delta消纳的区域,默认全省平衡机均摊;
                        'area_or_region_choice': user_input_data[
                            'area_or_region_choice'] if 'area_or_region_choice' in user_input_data else [],
                        # N-1故障设备信息;
                        "device_name": [int(v) for v in
                                        user_input_data['device_name']] if 'device_name' in user_input_data else [],
                        # 移相器设备信息;
                        "phase_shifter_arg": user_input_data[
                            'phase_shifter_arg'] if 'phase_shifter_arg' in user_input_data else {},
                        # 增加容抗器设备信息;
                        "add_reactance_arg": user_input_data[
                            'add_reactance_arg'] if 'add_reactance_arg' in user_input_data else {},
                        # 区内直流调节信息;例如游圌直流;
                        "dcline": user_input_data['dcline'] if 'dcline' in user_input_data else {},
                        }
    # 先不考虑任意机组/设备的定出力选择;
    # if (
    #         "area_or_region_choice" in g.request_json
    #         and
    #         "gen_choices" in g.request_json
    #         and
    #         "target" in g.request_json
    # ):
    #     new_weather_para['gen_output'] = {
    #         'area_or_region_choice': parse_typed_arg("area_or_region_choice", str),
    #         'gen_choices': parse_typed_arg("gen_choices", list),
    #         'target': parse_typed_arg("target", float),
    #     }

    # 检查case_result_dict是否有'branch_power'
    if 'branch_power' not in case_result_dict:
        case_result_dict['branch_power'] = np.concatenate(
            [case_result_dict['line_power'], case_result_dict['trafo_power']])

    cal_output = ss_calculator(case_net, case_result_dict, config, user_input=new_weather_para,
                               area_name=area_name, area_details=area_details, indicators_rlt_dict=indicator_rlt_dict)
    return cal_output


# def cal_grid_power_flow_func(net, config, result_dict, timestep, user_input_init, area_name: str = None,
#                              device_name: list = list(),
#                              phase_shifter_arg: dict = dict(),
#                              add_reactance_arg: dict = dict(),
#                              ):
#     """
#     推演页面,根据用户设定的新的推演边界计算电网的新方式下的电网潮流;
#     Args:
#         net:网络
#         config:配置参数
#         result_dict:初始的teap分析结果信息
#         timestep:  推演分析时刻点
#         user_input_init: 用户设定的新的电网潮流边界，负荷/风光出力/feedin出力
#         device_name: 计算电网N-1的故障设备索引
#         phase_shifter_arg:
#         add_reactance_arg:
#         area_name:
#
#     Returns:
#
#     """
#     # 用户调整的潮流计算边界信息;
#     arg_dict: dict = {
#         "net": net,
#         "config": config,
#         "result_dict": result_dict,
#         "target_time": timestep, "user_input_init": user_input_init,
#         "device_name": device_name,
#         "phase_shifter_arg": phase_shifter_arg,
#         "add_reactance_arg": add_reactance_arg,
#         "area_name": area_name,
#     }
#     _, format_graph_data, chart_dict, _ = cal_device_rate(**arg_dict)
#
#     graph_pf_data = dict()
#     point_data, line_data = get_final_format_data(format_graph_data)
#     graph_pf_data['point_data'] = point_data
#     graph_pf_data['line_data'] = line_data
#     return graph_pf_data


if __name__ == '__main__':
    import warnings

    warnings.filterwarnings("ignore", category=RuntimeWarning)

    import h5py
    import pickle
    import json
    import time
    import shelve


    def read_json_file(file_path):
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        return data


    def read_h5_group(file_path, group_name):
        # 打开HDF5文件
        with h5py.File(file_path, 'r') as f:
            # 获取指定组
            group = f[group_name]

            # 初始化一个空字典来存储数据集
            datasets = {}

            # 遍历组中的每个键（它们应该是数据集或子组）
            for key in group.keys():
                # 检查是否为数据集（而不是子组）
                if isinstance(group[key], h5py.Dataset):
                    # 读取数据集并添加到字典中
                    datasets[key] = group[key][:]  # [:] 用于读取整个数据集
        return datasets


    # 测试输入;
    # 河南商丘
    # user_input_data = {
    #     "case_scenerio": "henan",
    #     "calculate_flag": True,
    #     "case_id": "f305f13f-255a-4188-b199-d1e1bb0d8606",
    #     "timestep": "0",
    #     "device_name": [],  # 2924-'安鹤'
    #     "feedin": 224,
    #     "filter_name": "商丘",
    #     "gas": 0,
    #     "load": 458,
    #     "wind": 55,
    #     "solar": 0,
    #     "phase_shifter_arg": {},
    #     "add_reactance_arg": {},
    #     "area": "商丘",
    #     "dc_feedin": {
    #     }
    # }
    # #河南
    user_input_data = {
        "case_scenerio": "henan",
        "calculate_flag": True,
        "case_id": "f305f13f-255a-4188-b199-d1e1bb0d8606",
        "timestep": "19",
        "device_name": [],  # 2924-'安鹤'
        "feedin": 641.7,
        "filter_name": "全省",
        "gas": 139.2,
        "load": 7699.6,
        "wind": 360.6,
        "solar": 0.2,
        "phase_shifter_arg": {},
        "add_reactance_arg": {},
        "area": "全省",
        "dc_feedin": {
            "灵宝直流": 11.9,
            "中天直流": 517.4,
            "青豫直流": 77.1,
            "南荆特高压": 42.9,
            "豫武特高压": -12.2,
            "长南特高压": 17.4
        }
    }
    timestep = 19  # 推演的时刻时序序号;

    # 济源
    # user_input_data = {
    #     "case_scenerio": "jiyuan",
    #     "calculate_flag": True,
    #     'device_name': [31],
    #     'feedin': 0,
    #     'dc_feedin': {},
    #     'filter_name': "全省",
    #     'gas': 0,
    #     'load': 201,
    #     'phase_shifter_arg': {},
    #     'solar': 5.9,
    #     'timestep': "62",
    #     'wind': 1.2,
    # }
    # 江苏
    # user_input_data = {
    #     "case_scenerio": "jiangsu",
    #     "calculate_flag": True,
    #     "case_id": "f305f13f-255a-4188-b199-d1e1bb0d8606",
    #     "timestep": "4004",
    #     "device_name": [],  # n-1故障设备索引;
    #     "dcline": {
    #         "游圌直流": -30,
    #     },  # 如果有直流线路,直流线路调节;
    #     "feedin": 2533.7,
    #     "filter_name": "全省",
    #     "gas": 775.5,
    #     "load": 10664,
    #     "wind": 704,
    #     "solar": 0,
    #     "phase_shifter_arg": {
    #         "line_index": 261,
    #         "point_name": "徐圩",
    #         "shift_degree": 10
    #     },
    #     "add_reactance_arg": {
    #         "line_index": 120,
    #         "added_reactance_ohm": 20
    #     },
    #     "area": "全省",
    #     # "dc_feedin": {
    #     #     "锦苏直流": 427.4,
    #     #     "雁淮直流": 483.6,
    #     #     "龙政直流": 103.7,
    #     #     "建苏直流": 214,
    #     #     "锡泰直流": 403.5
    #     # }
    #     # 支持直流单设备调节; 数组数目应该与直流的关联模型的线路数目一致;
    #     "dc_feedin": {
    #         "锦苏直流": 427.4,  # 默认["LCC", "VSC1", "VSC2", "VSC3"],顺序按：jiangsu_dcfeedin_name_map
    #         "雁淮直流": 483.6,
    #         "龙政直流": 103.7,
    #         "建苏直流": [204, 71.3, 71.3, 71.3],  # 428
    #         "锡泰直流": 403.5
    #     }
    # }
    # timestep = 4004  # 推演的时刻时序序号;

    # # 江苏淮安分区
    # user_input_data = {
    #     "case_scenerio": "jiangsu",
    #     "calculate_flag": True,
    #     "case_id": "f305f13f-255a-4188-b199-d1e1bb0d8606",
    #     "timestep": "1597",
    #     "device_name": [4758],
    #     "feedin": 117.8,
    #     "filter_name": "淮安",
    #     "gas": 11.6,
    #     "load": 388,
    #     "wind": 61.1,
    #     "solar": 114.8,
    #     "phase_shifter_arg": {},
    #     "add_reactance_arg": {},
    #     "area": "盐城中",
    #     # "dc_feedin": {
    #     #     "锦苏直流": 276.8,
    #     #     "雁淮直流": 417.1,
    #     #     "龙政直流": 63.3,
    #     #     "建苏直流": 32.8,
    #     #     "锡泰直流": 326.7
    #     # }
    # }
    # timestep = 1597  # 推演的时刻时序序号;

    net_file = 'D:\\code\\baogong_sys\\baogong-api\\alg\\result_data\\case_id_2123214455656766\\case_net_2123214455656766.pkl'
    with open(net_file, 'rb') as f:
        loaded_net = pickle.load(f)

    # # 自动识别供电分区拓扑测试
    # import pandapower.topology as top
    #
    # net = loaded_net
    # load_buses = np.array(net.load['bus'].values)
    # trafo_buses = np.array(
    #     net.trafo['hv_bus'].values.tolist() + net.trafo['lv_bus'].values.tolist())
    # mg = top.create_nxgraph(net)  # converts example network into a MultiGraph
    # area_dict = dict()
    # area_num = 0
    # for area in top.connected_components(mg, notravbuses=set(net.bus[net.bus.vn_kv >= 500].index)):
    #     # 区内有负荷&有主变的才算一个分区;过滤没有负荷或主变的集合;
    #     area_arr = np.array(list(area))
    #     load_in_area = np.in1d(area_arr, load_buses)
    #     trafo_in_area = np.in1d(area_arr, trafo_buses)
    #     if not list(area_arr[load_in_area]) or not list(area_arr[trafo_in_area]):
    #         # 分区没有负荷或主变
    #         continue
    #     elif len(area) <= 3:
    #         # 分区节点数目太少< 4个
    #         continue
    #     else:
    #         area_num += 1
    #         area_dict[area_num] = list(area)
    # print(f"识别分区数目:{area_num}")
    # for key, val in area_dict.items():
    #     val_df = net.bus.loc[val]
    #     val_df = val_df[val_df['vn_kv'] > 450].index
    #     print(f"key:{key},val_index:{val_df.tolist()}")  # bus index
    #     print(f"val:{val}")
    #     # 所有主变>500kV的主变;
    #     trafo_idx = net.trafo[net.trafo['hv_bus'].isin(val)].index
    #     all500_trafo_idx = net.trafo[(net.trafo['vn_hv_kv'] >= (500 * 0.95))].index
    #     trafo_idx_new = trafo_idx[np.in1d(trafo_idx, all500_trafo_idx)]
    #     print(f"trafo_idx:{trafo_idx_new}")

    # exit(0)

    # 载入计算结果算例的json文件;
    caseinfo_file = 'D:\\code\\baogong_sys\\baogong-api\\alg\\result_data\\case_id_2123214455656766\\basic_info_2123214455656766.json'
    result_info_file = 'D:\\code\\baogong_sys\\baogong-api\\alg\\result_data\\case_id_2123214455656766\\cal_result_2123214455656766.json'
    indicator_rlt_file = 'D:\\code\\baogong_sys\\baogong-api\\alg\\result_data\\case_id_2123214455656766\\indicators_cal_2123214455656766.json'
    power_flow_file = 'D:\\code\\baogong_sys\\baogong-api\\alg\\result_data\\case_id_2123214455656766\\power_flow_2123214455656766.hdf5'
    rlt_power_output_file = 'D:\\code\\baogong_sys\\baogong-api\\alg\\result_data\\case_id_2123214455656766\\rlt_power_output_2123214455656766.hdf5'

    # caseinfo_dict = read_json_file(caseinfo_file)
    # indicator_rlt_dict = read_json_file(indicator_rlt_file)
    # # 读取数据
    caseinfo_file_pkl = 'D:\\code\\baogong_sys\\baogong-api\\alg\\result_data\\case_id_2123214455656766\\basic_info_2123214455656766.db'
    with shelve.open(caseinfo_file_pkl) as shelf:
        caseinfo_dict = shelf['data']

    indicator_rlt_file_pkl = 'D:\\code\\baogong_sys\\baogong-api\\alg\\result_data\\case_id_2123214455656766\\indicators_cal_2123214455656766.db'
    with shelve.open(indicator_rlt_file_pkl) as shelf:
        indicator_rlt_dict = shelf['data']

    config = caseinfo_dict['config']
    area_details = caseinfo_dict['zone']['device_relay']

    # 载入结果dict, h5文件;
    rlt_power_output_dict = read_h5_group(rlt_power_output_file, 'power_out')
    power_flow_dict = read_h5_group(power_flow_file, 'power_flow')
    result_init_data = {**rlt_power_output_dict, **power_flow_dict}

    # 获取推演初始数据;
    data = get_simulation_power_boundry(
        boundary_original=indicator_rlt_dict["boundary_data"], area_name="全省", timestep=timestep
    )

    # 先打印出timestep时刻的所有边界数据,然后再调整测试输入进行测试;
    if 'wind_output' in result_init_data:
        init_wind_output_all = result_init_data['wind_output'][:, timestep].sum().round(2)
    else:
        init_wind_output_all = 0
    print(f"init_wind_output_all:{init_wind_output_all}")
    if 'solar_output' in result_init_data:
        init_solar_output_all = result_init_data['solar_output'][:, timestep].sum().round(2)
    else:
        init_solar_output_all = 0
    print(f"init_solar_output_all:{init_solar_output_all}")
    # 开始推演
    start_time = time.time()
    rtn_data = run_calculator_func(user_input_data, timestep, result_init_data, loaded_net, config, indicator_rlt_dict,
                                   area_details)
    execution_time = (time.time() - start_time) * 1000  # 转换为毫秒
    print(f"代码块执行时间: {execution_time:.3f} 毫秒")
    pass
