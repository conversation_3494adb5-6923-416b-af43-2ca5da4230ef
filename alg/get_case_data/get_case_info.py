import numpy as np
import os
import io
import zipfile
import shutil
import pandas as pd
import pickle
import json
from alg.get_case_data.load_case import loadcase
from pandapower import pandapowerNet


# 定义函数，用于将csv文件读取为numpy array
def read_csv_to_nparray(file_path, time_columns):
    data = pd.read_csv(file_path, index_col=0, encoding='GB18030').loc[:, time_columns].values
    return data


# 定义函数，用于将指定zip文件中的csv文件读取为numpy array，并以字典形式返回
def read_zip_to_dict(zip_file_path, config):
    time_columns = [str(_time) for _time in range(config['sum_snap'])]
    # 创建一个字典，用于保存读取到的numpy array
    data_dict = {}

    # 打开zip文件
    with zipfile.ZipFile(zip_file_path, 'r') as zip_file:
        # 遍历zip文件中的所有文件
        for file_name in zip_file.namelist():
            # 判断文件是否为csv文件
            if file_name.endswith('.csv'):
                # 读取csv文件中的数据，并保存到字典中
                file_data = read_csv_to_nparray(zip_file.open(file_name), time_columns)
                data_dict[os.path.splitext(os.path.basename(file_name))[0]] = file_data

    data_dict['branch_power'] = np.vstack((data_dict['line_power'], data_dict['trafo_power']))

    return data_dict


def read_data_from_dot_teap(dot_teap_file):
    # 执行函数，读取指定zip文件中的csv文件，并保存到字典中
    with zipfile.ZipFile(dot_teap_file, 'r') as outer_zip_file:
        for file_name in outer_zip_file.namelist():
            # # 判断文件是否为zip文件
            # if file_name.endswith('.zip'):
            #     # 打开内部的zip文件，并读取其中的csv文件
            #     zfiledata = io.BytesIO(outer_zip_file.read(file_name))
            #     data_dict = read_zip_to_dict(zfiledata)
            if file_name.startswith('job_pcs_detail_result_'):
                jsonfiledata = json.load(outer_zip_file.open(file_name))
                config = pickle.loads(eval(jsonfiledata['config']))
            # 判断文件是否为xlsx文件
            if file_name.endswith('.xlsx') and (not file_name.startswith('report_')):
                temp_dir_file = outer_zip_file.extract(file_name, path='temp')
                net = loadcase(temp_dir_file)
                # 删除临时目录
                try:
                    shutil.rmtree(os.path.join(os.getcwd(), temp_dir_file))
                except:
                    pass
                try:
                    os.remove(temp_dir_file)
                except:
                    pass

    data_dict = {}
    with zipfile.ZipFile(dot_teap_file, 'r') as outer_zip_file:
        for file_name in outer_zip_file.namelist():
            # 判断文件是否为结果zip文件
            if file_name.startswith('result_') and file_name.endswith('.zip'):
                # 打开内部的zip文件，并读取其中的csv文件
                zfiledata = io.BytesIO(outer_zip_file.read(file_name))
                data_dict = read_zip_to_dict(zfiledata, config)

    # dcline的from,to结果;*(-1)
    if "dcline_power_from" in data_dict:
        data_dict["dcline_power_from"] *= (-1)
        data_dict["dcline_power_to"] *= (-1)

    # 某些时刻风电光伏被强制设为0，这里clip掉，最小为0.01
    # if 'wind_output' in data_dict:
    #     data_dict['wind_output'] = np.clip(data_dict['wind_output'], 0.01, None)
    # if 'solar_output' in data_dict:
    #     data_dict['solar_output'] = np.clip(data_dict['solar_output'], 0.01, None)

    # 检查是否有line_power和trafo_power缺行(net中inservice=False会导致缺行);
    # 插入行的位置索引（注意NumPy索引从0开始，并且由于插入操作，索引会变化）
    # def insert_zeros_at_indices(A, insert_indices, nums_col):
    #     # 初始B为原始数组A
    #     B = A.copy()
    #
    #     # 在指定位置插入全零行
    #     zero_row = np.zeros((1, nums_col))
    #     for idx in sorted(insert_indices, reverse=True):  # 从后往前插入以避免索引变化的影响
    #         B = np.insert(B, idx, zero_row, axis=0)
    #     return B
    #
    # b_add_idx = False
    # for key in data_dict:
    #     if key in _output_dict:
    #         net_key = _output_dict[key]
    #         if key == 'line_power' or key == 'trafo_power':
    #             # 获取inservice = False的设备的索引;
    #             insert_indices = net[net_key][net[net_key]['in_service'] == False].index.tolist()
    #
    #             # 在指定位置插入全零行
    #             data_dict[key] = insert_zeros_at_indices(data_dict[key], insert_indices=insert_indices,
    #                                                      nums_col=config['sum_snap'])
    #             b_add_idx = True
    # if b_add_idx and 'branch_power' in data_dict:
    #     data_dict['branch_power'] = np.vstack((data_dict['line_power'], data_dict['trafo_power']))

    # 补充stogen中如果没有in_service
    if 'stogen' in net and not net.stogen.empty and 'in_service' not in net.stogen.columns:
        net.stogen['in_service'] = True

    # 检查net网络里的除了bus其它表有in_service=Fasle的时候reset index
    for key in net:
        if key not in ['gen', 'load', 'wind', 'solar', 'hydropower', 'dcline', 'line', 'trafo', 'feedin', 'stogen']:
            continue
        if 'in_service' in net[key]:
            # 如果'in_service' = Fasle 的记录不为0
            net_dev_false_df = net[key][~net[key]['in_service']]
            if len(net_dev_false_df) == 0:
                continue
            net[key] = net[key][net[key]['in_service']]
            net[key].reset_index(drop=True, inplace=True)

    # fill 'in_service' nan to True
    net.bus['in_service'] = net.bus['in_service'].fillna(True)

    # net.timeseries 去除全部为nan的列
    net.timeseries = net.timeseries.dropna(axis=1, how='all')

    # 创建一个字典来跟踪名称的计数
    line_name_count = {}
    trafo_name_count = {}

    def rename(name, name_count):
        if name in name_count:
            name_count[name] += 1
            return f"{name}_{name_count[name]}"
        else:
            name_count[name] = 0
            return name
    # 是否对line表进行重命名;
    if True and len(net["line"])>0:
        net.line["ori_name"] = net.line["name"]
        # 使用 apply 方法来更新 DataFrame
        net.line['name'] = net.line['ori_name'].apply(rename, args=(line_name_count,))
    if True and len(net["trafo"])>0:
        net.trafo["ori_name"] = net.trafo["name"]
        net.trafo['name'] = net.trafo['ori_name'].apply(rename , args=(trafo_name_count,))
    return net, config, data_dict


def update_device_limit_info(net: pandapowerNet):
    """
    更新其他设备表的zone、vn_kv及限额等信息;
    Args:
        net:

    Returns:

    """
    net = CheckDeviceZoneVnkV(net)  # 添加设备的zone/vn_kv信息
    net = AddDeviceLimitInfo(net)  # 添加设备的稳定限额信息;
    return net


def CheckDeviceZoneVnkV(net: pandapowerNet):
    """
   # 检查/gen/wind/solar/line/stogen/trafo/feedin中是否有zone,vn_kv信息
    Args:
        net:

    Returns:
    """
    bus_df = net.bus
    bus_zone_dict = bus_df.set_index(bus_df.index)["zone"].to_dict()  # 字典,键是bus_df,{index:'zone'}
    bus_vn_kv_dict = bus_df.set_index(bus_df.index)["vn_kv"].to_dict()

    # bus
    if 'lon' not in net.bus.columns:
        net.bus['lon'] = None
    else:
        net.bus['lon'] = net.bus['lon'].round(5)  # 保留5为小数

    if 'lat' not in net.bus.columns:
        net.bus['lat'] = None
    else:
        net.bus['lat'] = net.bus['lat'].round(5)  # 保留5为小数

    if 'dispname' not in net.bus.columns:
        net.bus['dispname'] = ""
    else:
        net.bus['dispname'].fillna("", inplace=True)
    if 'type' not in net.bus.columns:
        net.bus['type'] = 'station'

    if 'type' in net.bus.columns:
        net.bus.loc[net.bus["type"] == "DC", "type"] = 'dc'

    # gen
    device_type = ['gen', 'wind', 'solar', 'stogen', 'feedin', 'load']
    for i_dev in device_type:
        if net[i_dev].empty:
            net[i_dev]['zone'] = None
            net[i_dev]['vn_kv'] = None
        else:
            if 'zone' in net[i_dev].columns:
                pass
            else:
                net[i_dev]["zone"] = net[i_dev]["bus"].map(bus_zone_dict)  # 使用 map 函数将 'bus' 列的值映射到 'zone' 列
            if 'vn_kv' in net[i_dev].columns:
                pass
            else:
                net[i_dev]["vn_kv"] = net[i_dev]["bus"].map(bus_vn_kv_dict)

    # wind/solar--如果没有'type'--统一默认为'centralized': 集中式; 'distributed':分布式/分散式
    new_type = ['wind', 'solar']
    for i_type in new_type:
        if net[i_type].empty and 'type' not in net[i_type].columns:
            net[i_type]['type'] = None
        elif 'type' not in net[i_type].columns:
            net[i_type]['type'] = 'centralized'

    # dcline
    if "dcline" in net.keys():
        if 'vn_kv' in net['dcline'].columns:
            pass
        else:
            net['dcline']["vn_kv"] = net['dcline']["from_bus"].map(bus_vn_kv_dict)

    # line
    if net['line'].empty:
        net['line']['from_zone'] = None
        net['line']['to_zone'] = None
        net['line']['vn_kv'] = None
    else:
        if 'from_zone' in net['line'].columns or 'to_zone' in net['line'].columns:
            pass
        else:
            net['line']["from_zone"] = net['line']["from_bus"].map(bus_zone_dict)
            net['line']["to_zone"] = net['line']["to_bus"].map(bus_zone_dict)
        if 'vn_kv' in net['line'].columns:
            pass
        else:
            net['line']["vn_kv"] = net['line']["from_bus"].map(bus_vn_kv_dict)

        # ''

    # if 'dispname' not in net['line'].columns:
    #     net['line']["dispname"] = net['line']["name"]
    # else:
    #     net['line']["name"] = net['line']["dispname"]  # 将name用dispname替换

    # 补充line/trafo 的 ['interface_name', 'interface_collection_name']
    # interface_name: 断面的线路名称,用line_name补充; interface_collection_name:断面名称,用interface的name列补充

    def update_dev_interface_name(inf_df, dev_df):
        if 'interface_collection_name' not in dev_df.columns:
            # # 如果 net['line']['interface'] 不为空,则根据net['line']['interface']的值(可能为空,也可能是int，也可能是字符串)找对应的net.interface.loc[i,'name']
            # for i_dx, line_row in dev_df.iterrows():
            #     if not pd.isna(line_row['interface']):
            #         line_inf_no = line_row['interface']
            #         if isinstance(line_inf_no, int):
            #             dev_df.loc[i_dx, 'interface_collection_name'] = inf_df.loc[line_inf_no, 'name']
            #         if isinstance(line_inf_no, float) or isinstance(line_inf_no, np.float64) or isinstance(line_inf_no,
            #                                                                                                np.float32):
            #             dev_df.loc[i_dx, 'interface_collection_name'] = inf_df.loc[int(line_inf_no), 'name']
            #         elif isinstance(line_row['interface'], str):
            #             # 将str进行用, 分割成list
            #             line_inf_no_list = np.array(line_row['interface'].split(',')).astype(int)
            #             dev_df.loc[i_dx, 'interface_collection_name'] = ', '.join(
            #                 inf_df.loc[line_inf_no_list, 'name'].values.tolist())
            #     else:
            #         dev_df.loc[i_dx, 'interface_collection_name'] = None
            dev_df.loc[:, 'interface_collection_name'] = None

        if 'interface_name' not in dev_df.columns:
            line_inf_idx = dev_df[~pd.isna(dev_df['interface'])].index
            dev_df.loc[line_inf_idx, "interface_name"] = dev_df.loc[line_inf_idx, "name"]
        return dev_df

    net['line'] = update_dev_interface_name(net.interface, net.line)
    net['trafo'] = update_dev_interface_name(net.interface, net.trafo)

    # trafo
    if net['trafo'].empty:
        net['trafo']['zone'] = None
        net['trafo']['vn_hv_kv'] = None
        net['trafo']['vn_lv_kv'] = None
    else:
        if 'zone' in net['trafo'].columns:
            pass
        else:
            net['trafo']["zone"] = net['trafo']["hv_bus"].map(bus_zone_dict)
        if 'vn_hv_kv' in net['trafo'].columns:
            pass
        else:
            net['trafo']["vn_hv_kv"] = net['trafo']["hv_bus"].map(bus_vn_kv_dict)
        if 'vn_lv_kv' in net['trafo'].columns:
            pass
        else:
            net['trafo']["vn_lv_kv"] = net['trafo']["lv_bus"].map(bus_vn_kv_dict)
    # if 'dispname' not in net['trafo'].columns:
    #     net['trafo']["dispname"] = net['trafo']["name"]
    # else:
    #     net['trafo']["name"] = net['trafo']["dispname"]  # 将name用dispname替换
    if 'trafo_name' not in net['trafo'].columns:
        net['trafo']["trafo_name"] = net['trafo']["name"]

    # 检查gen/wind/slar是否有'city',方便后续对可再生能源对地市的统计分析
    if 'city' not in net['gen'].columns:
        net['gen']["city"] = None
    if 'city' not in net['wind'].columns:
        net['wind']["city"] = None
    if 'city' not in net['solar'].columns:
        net['solar']["city"] = None

    return net


def AddDeviceLimitInfo(net: pandapowerNet):
    """
    补充线路和主变设备的限额容量, 电流电压等级等折算;
    Returns:
    """
    if 'stable_limit_mw' not in net.line.columns:
        net.line['stable_limit_mw'] = 1.732 * net.line['max_i_ka'] * net.line['vn_kv']  # ui*1.732
    else:
        # 如果限额小于1e-1,全部置为999999.0
        net.line.loc[pd.isna(net.line['stable_limit_mw']) | (net.line['stable_limit_mw'] < 1.0), 'stable_limit_mw'] = \
            1.732 * net.line['max_i_ka'] * net.line['vn_kv']

    if 'stable_limit_mw' not in net.trafo.columns:
        net.trafo['stable_limit_mw'] = net.trafo['sn_mva']
    else:
        # 填充为net.trafo['sn_mva']
        net.trafo.loc[pd.isna(net.trafo['stable_limit_mw']) | (net.trafo['stable_limit_mw'] < 1.0), 'stable_limit_mw'] = \
            net.trafo['sn_mva']

    return net


if __name__ == '__main__':
    dot_teap_file = '20230618_110324.teap'
    net, config, result_dict = read_data_from_dot_teap(dot_teap_file)
    pass
