import time
import io
import logging
import openpyxl
import pandas as pd
import pandapower as pp

from alg.conf.log_conf import logger
from alg.get_case_data.xlsx2csv import Xlsx2csv

load_case_logger = logging.getLogger("load_case")


def loadcase(case_file, md5=None, job_id: int = 0):
    logger.debug(f"load_case: case_file=[{case_file}], md5=[{md5}], job_id=[{job_id}]")

    net = pp.create_empty_network()

    case_data = _load_case(case_file, job_id=job_id)

    sheet_name_list = ['parameter', 'bus', 'gen', 'load', 'feedin', 'wind', 'solar', 'storage', 'stogen',
                       'line', "dcline", 'trafo', 'timeseries', 'hydropower', 'reservoir', 'interface']
    if "off_gen" in case_data:
        sheet_name_list.append("off_gen")
    for sheet_name in sheet_name_list:
        if sheet_name not in case_data:
            continue
        try:
            net[sheet_name] = case_data[sheet_name]
        except Exception as e:
            logger.debug(e)
            logger.debug(f'Not consider {sheet_name}')

    return net


def _load_case(case_file, select_worksheets: str = "", optional_select_worksheets: str = "", job_id: int = 0):
    """
    解析算例文件的具体方法
    :param case_file: 算例文件
    :param select_worksheets: 必须要解析的sheet 如果解析失败则抛出异常
    :param optional_select_worksheets:  需尝试解析的sheet 如果解析失败则只做内部日志打印
    :param job_id:  任务id编号
    :return:
    """

    load_case_logger.info(f"start load_case [{case_file}]")

    if select_worksheets != "":
        select_worksheet_list = select_worksheets.split(',')
        load_case_logger.debug(f"load case selected worksheets({len(select_worksheet_list)}): {select_worksheet_list}")
    else:
        select_worksheet_list = []

    # 配置字典 记录了 各个sheet里需要转int的列
    int_column_map = {
        "gen": ["bus"],
        "load": ["bus", "timeseries"],
        "line": ["from_bus", "to_bus", "interface", "interface_direction"],
        "trafo": ["hv_bus", "lv_bus", "interface", "interface_direction"],
        "feedin": ["bus", "timeseries"],
        "wind": ["bus", "timeseries"],
        "solar": ["bus", "timeseries"],
        "stogen": ["bus", "storage"],
        "hydropower": ["bus", "reservoir"],
        "reservoir": ["timeseries"],
    }

    load_start_time = time.time()

    case_data = {}
    # 通过 openpyxl.load_workbook 方法提取出全部 sheet 名称列表
    wb = openpyxl.load_workbook(case_file, read_only=True)
    worksheets = [v.title for v in wb.worksheets]
    wb.close()
    load_case_logger.debug(f"case file [{case_file}] sheet list: {worksheets}")

    # --> do not use ProcessPoolExecutor
    for index, sheet_name in enumerate(worksheets, start=1):

        if len(select_worksheet_list) > 0 and sheet_name not in select_worksheet_list:
            continue

        # load_case_logger.debug(f"index=[{index}], sheet_name=[{sheet_name}] processing...")
        index, sheet_name, data = _load_xl_file(case_file, index, sheet_name, job_id)
        case_data[sheet_name] = data
        # load_case_logger.debug(f"index=[{index}], sheet_name=[{sheet_name}] done")

        # 空sheet尝试去用numpy重新读取
        if case_data[sheet_name].empty is True:
            load_case_logger.warning(f"index=[{index}], sheet_name=[{sheet_name}] is empty!")
            case_data.update(
                pd.read_excel(case_file, sheet_name=[sheet_name], index_col=0)
            )
            load_case_logger.warning(f"index=[{index}], sheet_name=[{sheet_name}] empty handler done!")
        else:
            # 索引列转int
            # 不允许 reset_index 以兼容索引不连续等场景
            # case_data[sheet_name].reset_index(drop=True, inplace=True)
            # 索引列 类型转int 转失败则正常报错即可
            case_data[sheet_name].index = case_data[sheet_name].index.astype(int)

            if sheet_name in int_column_map:
                load_case_logger.warning(f"sheet_name [{sheet_name}] in cnf map, ready to reset type")
                for col_name in int_column_map[sheet_name]:
                    load_case_logger.warning(f"reset sheet_name [{sheet_name}] col_name [{col_name}] type")
                    case_data[sheet_name][col_name] = case_data[sheet_name][col_name].map(
                        lambda x: int(x) if pd.notnull(x) else None
                    )
                    # case_data[sheet_name][col_name] = case_data[sheet_name][col_name].astype(int)

    load_case_logger.info(f"load case_file [{case_file}] cost [{time.time() - load_start_time}] s")

    if optional_select_worksheets != "":
        optional_select_worksheet_list = optional_select_worksheets.split(',')
        load_case_logger.info(f"ready to load optional select worksheet list: {optional_select_worksheet_list}")
        try:
            optional_case_data = pd.read_excel(case_file, sheet_name=optional_select_worksheet_list)
            case_data.update(optional_case_data)
            load_case_logger.info("load optional select worksheet list ok!")
        except Exception as e:
            load_case_logger.error(f"load optional select worksheet list error: {e}")
            load_case_logger.exception(e)

    # try:
    #     wind_db = case_data['wind_db']
    #     n_wind_db = len(wind_db)
    #     if n_wind_db > 0:
    #         wind = merge_wind_data(case_data['wind'], wind_db, ip, port, job_id=int(job_id))
    #         case_data['wind'] = wind
    # except Exception as e:
    #     load_case_logger.debug(f'n_wind_db: 0 ({e})')

    # try:
    #     solar_db = case_data['solar_db']
    #     n_solar_db = len(solar_db)
    #     if n_solar_db > 0:
    #         solar = merge_solar_data(case_data['solar'], solar_db, ip, port, job_id=int(job_id))
    #         case_data['solar'] = solar
    # except Exception as e:
    #     load_case_logger.debug(f'n_solar_db: 0 ({e})')

    return case_data


def _load_xl_file(xl_file, index, sheet_name, job_id: int = 0):
    # load_case_logger.info(f"[{index}-{sheet_name}] begin!")
    temp_start_time = time.time()
    csv_data = io.StringIO()
    Xlsx2csv(
        xl_file,
        outputencoding="utf-8",
        dateformat="%Y-%m-%d %H:%M:00",
        skip_empty_lines=True
    ).convert(csv_data, sheetid=index)
    csv_data.seek(0)
    data = pd.read_csv(csv_data, index_col=0, encoding='utf-8')
    data = data.dropna(how='all')

    load_case_logger.info(f"[{index}-{sheet_name}] cost time: {time.time() - temp_start_time}")

    return index, sheet_name, data
