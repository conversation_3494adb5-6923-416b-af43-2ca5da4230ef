"""
重新识别供电分区,更新bus['topo_zone']域,没有添加该域
"""
import copy
import pandas as pd
import numpy as np
from pandapower import pandapowerNet
import pandapower.topology as top


def search_power_supply_area(net: pandapowerNet):
    """
    根据拓扑搜索供电分区;--补充bus表的'topo_zone',后供电分区采用该列;
    """
    net['topo_zone'] = net['zone']

    # config = dict()
    # identify_power_supply_area(net, config)

    return net


def merge_dicts(*dict_args):
    """
    Given any number of dictionaries, shallow copy and merge into a new dict,
    precedence goes to key-value pairs in latter dictionaries.
    """
    result = {}
    for dictionary in dict_args:
        result.update(dictionary)
    return result


def identify_power_supply_area(net, config):
    """ 识别最小供电分区; 分区关联节点;
    Args:
        net (_type_): _description_
    Returns:
        dict: _description_
    """

    ss_config = config['ss_config']
    area_name_mapping = ss_config['area_name_mapping']
    area_region_mapping = ss_config['area_region_mapping']

    # area_name_mapping_rev = {trafo: new_name for trafo, new_name in area_name_mapping.items()}
    region_mapping = {area_name_mapping[trafo_name]: region for trafo_name, region in area_region_mapping.items() if
                      trafo_name != ''}  # 二级区域

    power_supply_area_dict = dict()

    mg = top.create_nxgraph(net)  # converts example network into a MultiGraph
    gen_col = ['name', 'fullname', 'bus', 'in_service', 'max_p_mw']
    trafo_col = ['name', 'sn_mva', 'hv_bus', 'vn_hv_kv', 'lv_bus', 'vn_lv_kv', 'stable_limit_mw']
    line_col = ['name', 'from_bus', 'to_bus', 'stable_limit_mw']
    load_col = ['name', 'bus', 'in_service', 'max_p_mw']
    feedin_col = ['name', 'bus']
    wind_col = ['name', 'bus', 'max_p_mw']
    solar_col = ['name', 'bus', 'max_p_mw']
    stogen_col = ['name', 'bus']
    area_trafo_name_buses = dict()

    load_buses = np.array(net.load['bus'].values)  # 所有负荷节点
    trafo_buses = np.array(
        net.trafo['hv_bus'].values.tolist() + net.trafo['lv_bus'].values.tolist())  # 所有主变节点
    for area in top.connected_components(mg, notravbuses=set(net.bus[net.bus.vn_kv >= 500].index)):
        # 区内有负荷&有主变的才算一个分区;过滤没有负荷或主变的集合;
        area_arr = np.array(list(area))
        load_in_area = np.in1d(area_arr, load_buses)
        trafo_in_area = np.in1d(area_arr, trafo_buses)
        if not list(area_arr[load_in_area]) or not list(area_arr[trafo_in_area]):
            # 分区没有负荷,或者主变
            continue
        elif len(area) <= 3:
            # 分区节点数目太少< 4个
            continue
        else:
            zone_bus = list(area)  # 分区内变电站序号列表
            if any(net.bus.name[zone_bus].str.startswith(ss_config['bus_name_startswith'])):  # 只算省级电网内的
                # 分区内节点
                area_bus = net.bus.loc[zone_bus, :].sort_values(by='vn_kv', ascending=False)

                # 500kv以上，连接高低压节点都在分区内的变电站
                ## handle all trafo
                area_trafo = net.trafo.loc[(net.trafo.vn_hv_kv >= 500) &
                                           (net.trafo.hv_bus.isin(area_bus.index)) &
                                           (net.trafo.lv_bus.isin(area_bus.index)), trafo_col]
                # 新设负载上限
                area_trafo.loc[:, 'max_p_mw'] = area_trafo.loc[:, 'stable_limit_mw']

                # 确定分区名称
                # 主变名称串起来
                # 旧命名：苏太仓_137.-苏太仓B21.
                # 新命名：37kV凤城B主变
                area_trafo_name = '-'.join(
                    np.unique([n.split("kV", 1)[1][0:2] if 'kV' in n else n[1:3] for n in area_trafo.name]))

                if ss_config['bus_name_startswith'] == '苏':
                    # 吴江东
                    if len([True for name in area_trafo.name.values if ('吴江1号' in name) or ('吴江B1' in name)]) > 0:
                        area_trafo_name += '东'

                # 主变--分区名：节点列表
                area_trafo_name_buses[area_trafo_name] = list(area_bus.index)

    # 新分区名，及其节点
    area_new_names = np.unique(list(area_name_mapping.values())).tolist()
    try:
        area_new_names.remove('')
    except Exception as e:
        print(e)
        pass

    area_new_name_buses = {n: [] for n in area_new_names}
    for area_trafo_name, area_buses in area_trafo_name_buses.items():
        area_new_name = area_name_mapping[area_trafo_name]
        if area_new_name in area_new_names:
            area_new_name_buses[area_new_name] += area_buses

    # 把空value的对去掉
    filtered = {k: v for k, v in area_new_name_buses.items() if v != []}
    area_new_name_buses.clear()
    area_new_name_buses.update(filtered)

    region_names = set(area_region_mapping.values())
    try:
        region_names.remove('')
    except:
        pass
    region_name_buses = {r: [] for r in region_names}
    for area_trafo_name, area_buses in area_trafo_name_buses.items():
        region_name = area_region_mapping[area_trafo_name]
        if region_name in region_name_buses.keys():
            region_name_buses[region_name] += area_buses

    area_or_region_buses = merge_dicts(area_new_name_buses, region_name_buses)

    # 分区内计算
    for area_or_region_name, area_bus_index in area_or_region_buses.items():

        # 分区内节点
        area_bus = net.bus.iloc[area_bus_index, :].sort_index(axis=0)

        # 分区内主变
        area_trafo = net.trafo.loc[(net.trafo.vn_hv_kv >= 500) &
                                   (net.trafo.hv_bus.isin(area_bus_index)) &
                                   (net.trafo.lv_bus.isin(area_bus_index)), trafo_col]
        # 分区内线路
        area_line = net.line.loc[(net.line.from_bus.isin(area_bus_index)) &
                                 (net.line.to_bus.isin(area_bus_index)), line_col]

        # 新设负载上限
        area_trafo.loc[:, 'max_p_mw'] = area_trafo.loc[:, 'stable_limit_mw']

        # 分区内发电机组
        area_gen = net.gen.loc[net.gen.bus.isin(area_bus_index), gen_col]

        # 分区内风电
        area_wind = net.wind.loc[net.wind.bus.isin(area_bus_index), wind_col]

        # 分区内光伏
        area_solar = net.solar.loc[net.solar.bus.isin(area_bus_index), solar_col]

        # 分区内区外来电接入点
        area_feedin = net.feedin.loc[net.feedin.bus.isin(area_bus_index), feedin_col]

        # 分区内储能发电
        area_stogen = net.stogen.loc[net.stogen.bus.isin(area_bus_index), stogen_col]

        # 分区内负荷，风光抽水蓄能不计入
        area_load_ext = net.load.loc[net.load.bus.isin(area_bus_index), :]
        area_load = area_load_ext.loc[(~area_load_ext.name.str.contains('XF')) &
                                      (~area_load_ext.name.str.contains('XG')) &
                                      (~area_load_ext.merge(net.bus, left_on='bus', right_index=True).zone.isin(
                                          ['Jc', 'Jf', 'Jg'])), load_col]

        ## 分区名和区域名写入net
        if area_or_region_name not in region_names:
            net.bus.loc[area_bus_index, 'area'] = area_or_region_name
        else:
            net.bus.loc[area_bus_index, 'region'] = area_or_region_name

        ## 分区所在区域
        if area_or_region_name not in region_names:
            area_in_region = pd.DataFrame.from_dict({area_or_region_name: [region_mapping[area_or_region_name]]})

        # 整合分区信息
        power_supply_area_dict[area_or_region_name] = dict()
        power_supply_area_dict[area_or_region_name]['area_bus'] = area_bus
        power_supply_area_dict[area_or_region_name]['area_gen'] = area_gen
        power_supply_area_dict[area_or_region_name]['area_trafo'] = area_trafo
        power_supply_area_dict[area_or_region_name]['area_line'] = area_line
        power_supply_area_dict[area_or_region_name]['area_load'] = area_load
        power_supply_area_dict[area_or_region_name]['area_feedin'] = area_feedin
        power_supply_area_dict[area_or_region_name]['area_wind'] = area_wind
        power_supply_area_dict[area_or_region_name]['area_solar'] = area_solar
        power_supply_area_dict[area_or_region_name]['area_stogen'] = area_stogen
        power_supply_area_dict[area_or_region_name]['area_in_region'] = area_in_region

    return power_supply_area_dict


# 命名分区名;
# 如果不找供电分区就不找;
# 如果找供电分区就执行：
# 找到全网电压等级>=500的节点,取出zone_name 作为最小供电分区名称;
# 找区域名称加入zone:如果有area不为空,则进一步找area的unique名称,然后找关联zone，再找关联的设备;
# 找onwer最多支持三级;
def update_identify_supply_area(net_ori, config: dict):
    """
    Args:
        net:
        config:
    Returns:

    """
    # pp.plotting.simple_plot(loaded_net)
    if config["case_address"] == "jiangsu" and "dcline" in net_ori.keys() and len(net_ori["dcline"]) > 0:
        # 江苏扬镇的游圌直流线路断开(去除,否则泰扬北和访晋分区就会连在一起无法分开,直流是220kV的);
        net = pandapowerNet(copy.deepcopy(net_ori))
        net.dcline = net.dcline[~net.dcline['name'].str.contains('游圌')]
    else:
        net = net_ori

    load_buses = np.array(net.load['bus'].values)
    trafo_buses = np.array(
        net.trafo['hv_bus'].values.tolist() + net.trafo['lv_bus'].values.tolist())
    mg = top.create_nxgraph(net)  # converts example network into a MultiGraph
    area_dict = dict()
    area_num = 0
    for area in top.connected_components(mg, notravbuses=set(net.bus[net.bus.vn_kv >= 500].index)):
        # 区内有负荷&有主变的才算一个分区;过滤没有负荷或主变的集合;
        area_arr = np.array(list(area))
        load_in_area = np.in1d(area_arr, load_buses)
        trafo_in_area = np.in1d(area_arr, trafo_buses)
        if not list(area_arr[load_in_area]) or not list(area_arr[trafo_in_area]):
            # 分区没有负荷或主变
            continue
        elif len(area) <= 3:
            # 分区节点数目太少< 4个
            continue
        else:
            area_num += 1
            area_dict[area_num] = list(area)
    # print(f"识别分区数目:{area_num}")
    # for key, val in area_dict.items():
    #     print(val)
    return area_dict
