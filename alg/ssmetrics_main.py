'''
主入口文件;
# @Time    : 2024/4/20 16:08
# @File    : ssmetrics_main.py
# 运行统计分析类;
'''
import os
import re
import pandas as pd
import yaml
import warnings
import numpy as np
import pandapower as pp
from loguru import logger
from alg.ssmetrics_config import basedir
from typing import Optional, Callable
from alg.conf.config_extern import (public_para_dict, config_exter_dict,
                                    dc_feedin_dict, scene_config_dict)
from alg.get_case_data.get_case_info import read_data_from_dot_teap, update_device_limit_info
from alg.cal_ssmetrics.case_info_statistics import CCaseInfo
from alg.cal_ssmetrics.case_result_statis import CCaseResultAna
from alg.cal_ssmetrics.indicator_cal import CIndicatorAna
from alg.get_case_data.identify_supply_area import update_identify_supply_area

warnings.filterwarnings("ignore", category=RuntimeWarning)


def load_yaml_to_dict(file_path):
    try:
        with open(file_path, 'r', encoding="utf8") as file:
            data = yaml.safe_load(file)
    except:
        try:
            with open(file_path, 'r', encoding="gbk") as file:
                data = yaml.safe_load(file)
        except:
            return None
    return data


class CTeapAna():
    """
    Teap运行结果的统计与分析基类;
    """

    def __init__(self, case_id: str, case_file: str, case_address: str = '', case_start_time: str = None) -> None:
        self.case_id = case_id
        self.case_file = case_file
        self.case_config = dict()
        self.ss_config = dict()
        # self.extern_conf_f = config_f
        self.case_net = pp.create_empty_network()
        self.rlt_data_dict = dict()  # teap的计算结果dict;
        self.case_address = case_address  # 保供部署应用的场景，例如: 'jiangsu','henan','hunan','jiyuan','zhejiang'
        self.ana_input_start_time = case_start_time

        self.topo_zone_dict = dict()

        # 分析子类的对象;
        self.case_sta = None  # 统计静态数据子类的对象;

    def load_teap_net_data(self):
        """
        载入teap的case数据
        """
        self.net, self.case_config, self.rlt_data_dict = read_data_from_dot_teap(self.case_file)

        self.updateConfInfo()  # 更新配置参数dict

        # 自动更新识别供电分区并处理;net--并更新'bus'的'topo_zone'和'zone'
        self.net, self.topo_zone_dict = self.update_power_zone(self.net)  # topo_zone_dict分区关联母线索引;

        # 补充其他设备表的分区、电压等级和限额等列信息
        self.net = update_device_limit_info(self.net)

    def update_config_feedin_dcit(self):
        """
        如果有feedin有列'dispname',重新更新self.ss_config['dc_feedin_dict']
        """
        feedin_df = self.net['feedin'][~self.net['feedin']["type"].isin(["营销分布式", "分布式"])]
        if "feedin_name_map" in self.ss_config:
            """
            'feedin_name_map': {
            '建苏直流': {"LCC": ["常熟"], "VSC1": ["常北"], "VSC2": ["木渎"], "VSC3": ["玉山"]},
            '锦苏直流': ["锦苏"],
            '龙政直流': ["龙政"],
            '雁淮直流': ["雁淮"],
            '锡泰直流': ["锡泰"]
            }
            """
            for key, val in self.ss_config["feedin_name_map"].items():
                if isinstance(val, list):
                    self.ss_config["dc_feedin_dict"][key] = feedin_df[
                        feedin_df.name.str.contains('|'.join(val))].index.tolist()
                elif isinstance(val, dict):
                    self.ss_config["dc_feedin_dict"][key] = dict()
                    for item_key, item_val in val.items():
                        self.ss_config["dc_feedin_dict"][key][item_key] = feedin_df[
                            feedin_df.name.str.contains('|'.join(list(item_val)))].index.tolist()
                else:
                    # raise TypeError(f"配置文件中的feedin_name_map的值类型错误")
                    pass
                # 需要排序;按list或者dict的key进行排序

        for i_col in ["name", "dispname", "bpa_busname"]:  # , "name_maybe"
            if i_col in feedin_df.columns:
                feedin_df[i_col].fillna("", inplace=True)
                for key_, val in self.ss_config['dc_feedin_dict'].items():
                    if isinstance(val, dict):
                        continue
                    # 找出  isin 'dispname' contains key_的索引-- 将key_中的'直流'、'交流'、'特高压'、'特高压交流'这些字去除
                    key_simple_ = re.sub(r'直流|交流|特高压|特高压交流', '', key_)
                    key_index = feedin_df[feedin_df[i_col].str.contains(key_simple_)].index.tolist()
                    if len(key_index) > 0:
                        self.ss_config['dc_feedin_dict'][key_] += (
                            [x for x in key_index if x not in self.ss_config['dc_feedin_dict'][key_]])
                    else:
                        pass
        # 检查val里的索引是否都在feedin索引中
        for key_, val in self.ss_config['dc_feedin_dict'].items():
            if isinstance(val, dict):
                for sub_key, sub_val in val.items():
                    val_valid = [idx for idx in sub_val if idx in self.net['feedin'].index]
                    self.ss_config['dc_feedin_dict'][key_][sub_key] = val_valid
            else:
                val_valid = [idx for idx in val if idx in self.net['feedin'].index]
                self.ss_config['dc_feedin_dict'][key_] = val_valid

    def updateConfInfo(self):
        # init_conf_f = os.path.join(basedir, 'conf', self.extern_conf_f)
        # self.ss_config = load_yaml_to_dict(init_conf_f)
        if self.case_address != '' and self.case_address in scene_config_dict.keys():
            self.ss_config = scene_config_dict[self.case_address]['config_exter_dict']
            self.ss_config['dc_feedin_dict'] = scene_config_dict[self.case_address]['dc_feedin_dict']
            for i_key in ["feedin_name_map"]:
                if i_key in scene_config_dict[self.case_address]:
                    self.ss_config[i_key] = scene_config_dict[self.case_address][i_key]
        else:  # 载入默认的参数
            self.ss_config = config_exter_dict
            self.ss_config['dc_feedin_dict'] = dc_feedin_dict
        self.update_config_feedin_dcit()  # 更新self.ss_config['dc_feedin_dict']

        self.ss_config = {**public_para_dict, **self.ss_config}

        # 获取分区映射的文件dict
        zone_map_ymldirfile = os.path.join(basedir, 'conf', f"{self.case_address}_config.yml")  # 绝对路径"
        zonename_map_dict = load_yaml_to_dict(zone_map_ymldirfile)
        if zonename_map_dict is not None:
            self.ss_config['area_name_mapping'] = zonename_map_dict['area_name_mapping']  # 分区名称映射;
            self.ss_config['area_region_mapping'] = zonename_map_dict['area_region_mapping']  # 分区--区域名称映射;
        else:
            logger.error(f"{zone_map_ymldirfile}文件 读取失败")
            if 'area_name_mapping' not in self.ss_config.keys():
                self.ss_config['area_name_mapping'] = dict()
            if 'area_region_mapping' not in self.ss_config.keys():
                self.ss_config['area_region_mapping'] = dict()

        self.ss_config['case_id'] = self.case_id
        self.ss_config['case_address'] = self.case_address
        if self.ana_input_start_time is not None and self.ana_input_start_time != '':
            self.ss_config['start_time'] = self.ana_input_start_time

        # 补充算例相关的config
        self.ss_config['sum_snap'] = self.case_config['sum_snap']  # 时序数目
        self.ss_config['n_element'] = self.case_config['n_element']  # 设备数目;
        self.ss_config['element_type'] = self.case_config['element_type']
        self.ss_config['data_type'] = self.case_config['data_type']
        self.ss_config['case_rlt_dir'] = os.path.join(basedir, "result_data",
                                                      f"case_id_{self.case_id}")  # 算例的预分析结果文件的目标路径;

        # 'selected_branch_idx','wind_reserve_cof','solar_reserve_cof'
        self.ss_config['up_reserve_cof'] = self.case_config['up_reserve_cof']  # 上备用裕度门槛
        self.ss_config['down_reserve_cof'] = self.case_config['down_reserve_cof']  # 下备用裕度门槛
        # self.ss_config['emergency_reserve_cof'] = self.case_config['emergency_reserve_cof']  # 停机备用裕度门槛
        self.ss_config['wind_reserve_cof'] = self.case_config['wind_reserve_cof']
        self.ss_config['solar_reserve_cof'] = self.case_config['solar_reserve_cof']
        self.ss_config['selected_line_idx'] = list(self.case_config['selected_line_idx'])
        self.ss_config['selected_trafo_idx'] = list(self.case_config['selected_trafo_idx'])
        self.ss_config['selected_branch_idx'] = self.case_config['selected_branch_idx'].tolist()

    def update_power_zone(self, net):
        """
        更新zone的'zone'列;   最终还是用'zone'做分区名,并做分区/设备关联关系dict的key
        Args:
            net:
        Returns:
        """
        supplt_area_dict_new = dict()
        if self.ss_config['zone_col'] == 'zone':
            # 直接使用'zone'列
            zone_list = net.bus['zone'].unique()
            zone_list = [item for item in zone_list if item != '']  # 去除''值
            zone_list = [item for item in zone_list if not pd.isna(item)]  # 去除nan值
            supplt_area_dict = dict()

            # 如果分区没有'zone_name'列,先添加;
            if 'zone_name' not in net.bus.columns:
                net.bus['zone_name'] = ""

            for i_no in range(len(zone_list)):
                # 先找出所有分区'zone' == zone_list[i_no]的记录的'zone_name'列;
                zone_i_name_list = net.bus.loc[net.bus['zone'] == zone_list[i_no], 'zone_name'].unique()
                zone_i_name_list = [item for item in zone_i_name_list if item != '']  # 去除''值
                zone_i_name_list = [item for item in zone_i_name_list if not pd.isna(item)]  # 去除nan值
                if len(zone_i_name_list) == 0:
                    # zone_name = zone_list[i_no]
                    continue
                else:
                    zone_name = zone_i_name_list[0]  # 取zone_i_name的一个值作为zone_name
                zn_index = net.bus.loc[net.bus['zone'] == zone_list[i_no]].index
                net.bus.loc[zn_index, 'zone'] = zone_name

                supplt_area_dict[i_no] = zn_index.tolist()
                if zone_name not in supplt_area_dict_new.keys():
                    supplt_area_dict_new[zone_name] = zn_index.tolist()
                else:
                    supplt_area_dict_new[zone_name] += zn_index.tolist()
            net.bus['zone_name'] = net.bus['zone']

        else:
            # if self.ss_config['zone_col'] == 'topo_zone':
            # 搜索供电分区增加'topo_zone'
            supplt_area_dict = update_identify_supply_area(net, self.ss_config)  # 新识别的供电分区;

            # 从外部读取分区名:
            if 'zone_name_from_yml' in self.ss_config and self.ss_config['zone_name_from_yml']:
                # net.bus['zone'] = ''  # 先全部置为''
                net.bus['zone_name'] = ''  # 先全部置为''
                area_name_mapping = self.ss_config['area_name_mapping']
                # 新分区名，及其节点
                area_new_name_keys = list(area_name_mapping.keys())
                for i_z, val in supplt_area_dict.items():
                    # 分区内节点
                    area_bus = net.bus.loc[val, :].sort_values(by='vn_kv', ascending=False)

                    # 500kv以上，连接高低压节点都在分区内的变电站
                    ## handle all trafo
                    area_trafo = net.trafo.loc[(net.trafo.vn_hv_kv >= 450) &
                                               (net.trafo.hv_bus.isin(area_bus.index)) &
                                               (net.trafo.lv_bus.isin(area_bus.index)), :]
                    # 确定分区名称
                    # 主变名称串起来
                    # 旧命名：苏太仓_137.-苏太仓B21.新命名：37kV凤城B主变
                    # 如果area_trafo.name都是以'豫'开头
                    if self.ss_config["prov_key"] != "":
                        prov_key = self.ss_config["prov_key"]
                        name_start_no = 1 if area_trafo.name.str.startswith(prov_key).all() else 0
                    else:
                        name_start_no = 0
                    area_trafo_name = '-'.join(
                        np.unique(
                            [n.split("kV", 1)[1][0:2] if 'kV' in n else n[name_start_no:3] for n in area_trafo.name]))
                    # 如果有'站'都去除
                    area_trafo_name = re.sub(r'站', '', area_trafo_name)
                    # 江苏的吴江东做特殊处理
                    if len([True for name in area_trafo.name.values if ('吴江1号' in name) or ('吴江B1' in name)]) > 0:
                        area_trafo_name += '东'

                    # 给'zone_name'赋值;
                    if area_trafo_name in area_new_name_keys:
                        # net.bus.loc[val, 'zone'] = area_name_mapping[area_trafo_name]
                        net.bus.loc[val, 'zone_name'] = area_name_mapping[area_trafo_name]
                        supplt_area_dict_new[area_name_mapping[area_trafo_name]] = val
                    else:
                        # net.bus.loc[val, 'zone'] = area_trafo_name
                        net.bus.loc[val, 'zone_name'] = area_trafo_name
                        supplt_area_dict_new[area_trafo_name] = val

                # supplt_area_dict_new 输出至csv文件 supplt_area_dict_new.csv:
                # 将字典数据写入CSV文件
                # import csv
                # with open('./supplt_area_dict_new.csv', 'w', newline='') as csvfile:
                #     writer = csv.writer(csvfile)
                #     for key, value in supplt_area_dict_new.items():
                #         writer.writerow([key, value])
            else:
                # 根据'zone_name'列 ,新识别的供电分区更新'zone'和'zone_name'
                if 'zone_name' not in net.bus.columns:
                    raise ValueError("bus表中缺少分区名称列:{zone_name},无法对分区进行命名")
                else:
                    net.bus['zone_name'] = net.bus['zone_name'].fillna('')  # 将nan置为''

                for i_z, val in supplt_area_dict.items():
                    # val 关联记录的'zone_name',unique,取第一个; 如果没有'告警',临时命名: '分区i'
                    zone_i_name_list = net.bus.loc[val, 'zone_name']
                    zone_i_name_list = [item for item in zone_i_name_list if item != '']  # 去除''值
                    zone_i_name = zone_i_name_list[0] if zone_i_name_list else 'zone_' + str(i_z)
                    # net.bus.loc[val, 'zone'] = zone_i_name
                    net.bus.loc[val, 'zone_name'] = zone_i_name
        # else:
        #     # 直接使用'zone_name'列
        #     zone_list = net.bus['zone_name'].unique()
        #     zone_list = [item for item in zone_list if item != '']  # 去除''值
        #     supplt_area_dict = dict()
        #     for i_no in range(len(zone_list)):
        #         supplt_area_dict[i_no] = net.bus[net.bus['zone'] == zone_list[i_no]].index.tolist()

        # 获取所有的'dispname'列unique值"
        # if 'dispname' in net.bus.columns:
        #     net.bus['dispname'] = net.bus['dispname'].fillna('')  # 将nan置为''
        #     disp_name_list = net.bus['dispname'].unique()
        #     disp_name_list = [item for item in disp_name_list if item != '']  # 去除''值
        #     for dispname_i in disp_name_list:
        #         # 查找'dispname' == dispname_i的'zone'列; 如果有非空值,则将所有行的'zone' 和'zone_name' 置为相同的值
        #         zone_i_name_list = net.bus.loc[net.bus['dispname'] == dispname_i, 'zone'].tolist()
        #         zone_i_name_list = [item for item in zone_i_name_list if item != '']  # 去除''值
        #         if len(zone_i_name_list) == 1:
        #             zone_i_name = zone_i_name_list[0]
        #             net.bus.loc[net.bus['dispname'] == dispname_i, 'zone'] = zone_i_name
        #             net.bus.loc[net.bus['dispname'] == dispname_i, 'zone_name'] = zone_i_name
        return net, supplt_area_dict_new

    def start_case_data_statistics(self, tofile: bool = False):
        """开始统计;
        Args:
            case_id (str):
            case_file (str): case的teap结果包.teap的完整路径;
        """
        # 最终结果整个结构体;
        ana_result_dict = {
            'case_info': dict(),
            'cal_result': dict(),
            'indicator': dict()
        }

        ##载入teap包,读取算例和结果信息;
        self.load_teap_net_data()

        # 填充算例配置信息;

        ## 开始功能计算;
        # （1）统计静态数据;
        case_sta = CCaseInfo(self.net, self.rlt_data_dict, config_f=self.ss_config,
                             area_bus_map_dict=self.topo_zone_dict)
        case_sta.get_case_info(tofile)
        ana_result_dict['case_info'] = case_sta.statistics_dict  # case有关静态结果数据;
        ana_result_dict['network'] = self.net  # teap算例网络;
        ana_result_dict['ptdf'] = case_sta.ptdf  # 网络topo
        ana_result_dict['result_output'] = self.rlt_data_dict  # teap计算的结果数据,去除限额名称等数据

        # （2）结果时序数据;
        area_details_dict = case_sta.statistics_dict['zone']['device_relay']
        case_rlt_ana = CCaseResultAna(self.net, self.rlt_data_dict, config=self.ss_config,
                                      area_details=area_details_dict)
        case_rlt_ana.start_result_output(tofile)
        ana_result_dict['cal_result'] = case_rlt_ana.case_rlt_cal  # 结果相关的部分数据;

        # case_test_func1(self.net, self.ss_config, self.rlt_data_dict, case_sta.statistics_dict,
        #                 case_rlt_ana.case_rlt_cal, area_details_dict)  # 测试函数;

        # （3）保供分析指标数据;
        index_ana = CIndicatorAna(self.net, self.rlt_data_dict, config=self.ss_config)
        index_ana.start_identify_alerts(self.net, self.ss_config, self.rlt_data_dict, area_details_dict, case_sta.ptdf,
                                        tofile=tofile)
        ana_result_dict['indicator'] = index_ana.indicator_rlt_dict  # 存储结果

        # case_test_func2(self.net, self.ss_config, self.rlt_data_dict, case_rlt_ana.case_rlt_cal, area_details_dict,
        #                 index_ana.indicator_rlt_dict)  # 测试函数;

        return ana_result_dict


def TeapAna_main(case_id: str, case_file: str,
                 case_address: str = '',
                 case_start_time: str = None,
                 func: Optional[Callable] = None,
                 tofile: bool = False, log_file: Optional[str] = None,
                 is_short: Optional[int] = None):
    """
    预处理分析主函数;
    Args:
        case_id: case唯一的uuid
        case_file: case_的.teap文件
        case_address: 保供部署应用省份场景名,用于选择不同的配置文件conf/config_extern.py中dict;
        case_start_time: 算例的开始时刻
        log_file: 分析日志存放的文件路径
        func:
        tofile:

    Returns:

    """
    status = 2
    rlt_data = {}  # 结果dict
    try:
        g_rlt_ana = CTeapAna(case_id, case_file, case_address, case_start_time)  # 定义类;
        rlt_data = g_rlt_ana.start_case_data_statistics(tofile)
    except Exception as e:
        if log_file:
            logger.add(log_file)
        status = 3
        logger.exception(e)
    finally:
        if func:
            func(case_id=case_id, data=rlt_data, status=status, log_file=log_file, is_short=is_short)
    return


def case_test_func1(net, config, result_dict, case_info_dict, rlt_data_dict, area_details_dict):
    """
    测试函数;
    :param rlt_data_dict:
    :return:
    """
    import time
    # from alg.common_utils.data_utils import get_power_supply_distribute, get_data_hours_distribute
    # start_time = time.time()
    # dat_ = get_power_supply_distribute(rlt_data_dict)
    # execution_time = (time.time() - start_time) * 1000  # 转换为毫秒
    # print(f"代码块执行时间: {execution_time:.3f} 毫秒")

    # from alg.common_utils.data_utils import get_data_hours_distribute
    # start_time = time.time()
    # data_num = get_data_hours_distribute(config, result_dict, ['load_curtailment'], "num_count")
    # execution_time = (time.time() - start_time) * 1000  # 转换为毫秒
    # print(f"代码块执行时间: {execution_time:.3f} 毫秒")

    # from alg.common_utils.data_utils import get_station_location
    # start_time = time.time()
    # data_storage = get_station_location(net, "storage")
    # execution_time = (time.time() - start_time) * 1000  # 转换为毫秒
    # print(f"代码块执行时间: {execution_time:.3f} 毫秒")
    #
    # from alg.common_utils.balance_data_utils import get_channel_ability_qr_data
    # start_time = time.time()
    # data_channel, data_c_2 = get_channel_ability_qr_data(net=net, config=config, result_dict=result_dict,
    #                                                      inf_collection=case_info_dict["inf_collection"],
    #                                                      channel_name="过江断面")
    # execution_time = (time.time() - start_time) * 1000  # 转换为毫秒
    # print(f"代码块执行时间: {execution_time:.3f} 毫秒")
    #
    # from alg.common_utils.balance_data_utils import get_channel_ratio_time_distribute
    # start_time = time.time()
    # data_channel_2 = get_channel_ratio_time_distribute(net=net, config=config, result_dict=result_dict,
    #                                                    inf_collection=case_info_dict["inf_collection"],
    #                                                    channel_name="过江断面")
    # execution_time = (time.time() - start_time) * 1000  # 转换为毫秒
    # print(f"代码块执行时间: {execution_time:.3f} 毫秒")
    #
    # from alg.common_utils.balance_data_utils import get_dcline_power_series
    # start_time = time.time()
    # dcline_power = get_dcline_power_series(net=net, result_dict=result_dict, dc_line_idx=[0], is_all=False,
    #                                        time_list=[i for i in range(24)])
    # execution_time = (time.time() - start_time) * 1000  # 转换为毫秒
    # print(f"代码块执行时间: {execution_time:.3f} 毫秒")

    from alg.common_utils.data_utils import get_zone_device_relay_inf_info
    zone_inf_data = get_zone_device_relay_inf_info(net=net, result_dict=result_dict, area_name="车坊",area_details_dict=area_details_dict)

    from alg.common_utils.data_utils import get_network_topo
    start_time = time.time()
    data_topo = get_network_topo(case_info_dict=case_info_dict, time_no=None, result_dict=result_dict, area_name="全省")
    execution_time = (time.time() - start_time) * 1000  # 转换为毫秒
    print(f"代码块执行时间: {execution_time:.3f} 毫秒")

    from alg.common_utils.data_utils import get_network_topo_attri
    start_time = time.time()
    data_topo_attri = get_network_topo_attri(case_info_dict=case_info_dict, result_dict=result_dict, area_name="全省")
    execution_time = (time.time() - start_time) * 1000  # 转换为毫秒
    print(f"代码块执行时间: {execution_time:.3f} 毫秒")

    from alg.common_utils.balance_data_utils import get_casesys_control_info
    start_time = time.time()
    data_sys = get_casesys_control_info(net=net, config=config, result_dict=result_dict,
                                        inf_collection=case_info_dict["inf_collection"])
    execution_time = (time.time() - start_time) * 1000  # 转换为毫秒
    print(f"代码块执行时间: {execution_time:.3f} 毫秒")

    from alg.common_utils.data_utils import get_channel_relayinf_info_qr_
    ele_dict = {"channel_name": "过江断面", "interface_name": "淮安-三汊湾"}
    start_time = time.time()
    data_relay_inf = get_channel_relayinf_info_qr_(net=net, config=config, result_dict=result_dict, ele_dict=ele_dict)
    execution_time = (time.time() - start_time) * 1000  # 转换为毫秒
    print(f"代码块执行时间: {execution_time:.3f} 毫秒")

    # from alg.common_utils.data_utils import get_network_topo_v2
    # start_time = time.time()
    # dat1_ = get_network_topo_v2(case_sta.statistics_dict, case_rlt_ana.case_rlt_cal['topo_dev_rate'],time_no=0,result_dict=self.rlt_data_dict)
    # execution_time = (time.time() - start_time) * 1000  # 转换为毫秒
    # print(f"代码块执行时间: {execution_time:.3f} 毫秒")

    # from alg.common_utils.ultra_time_data_utils import get_grid_extreme_value_info_with_time
    # start_time = time.time()
    # dat6 = get_grid_extreme_value_info_with_time(self.net, self.ss_config, case_sta.statistics_dict["series"], case_rlt_ana.case_rlt_cal, time_start=24,
    #                                   cal_num = 1000)
    # execution_time = (time.time() - start_time) * 1000  # 转换为毫秒
    # print(f"代码块执行时间: {execution_time:.3f} 毫秒")

    # from alg.common_utils.data_utils import get_zone_balance_data_curve
    # start_time = time.time()
    # dat_ = get_zone_balance_data_curve(self.net, self.ss_config, self.rlt_data_dict, area_name='安鹤',
    #                                        area_details_dict=area_details_dict)
    # execution_time = (time.time() - start_time) * 1000  # 转换为毫秒
    # print(f"代码块执行时间: {execution_time:.3f} 毫秒")

    # from alg.common_utils.data_utils import get_grid_margin_data
    # dat_ = get_grid_margin_data(self.net, self.ss_config, self.rlt_data_dict, index_ana.indicator_rlt_dict)
    # from alg.common_utils.data_utils import get_inf_power_timesteps
    # dat_ = get_inf_power_timesteps(self.net, self.rlt_data_dict, time_no=[10, 13, 15])
    # execution_time = (time.time() - start_time) * 1000  # 转换为毫秒
    # from alg.common_utils.data_utils import get_grid_extreme_value_info
    # dat_ = get_grid_extreme_value_info(case_sta.statistics_dict,case_rlt_ana.case_rlt_cal)
    # from alg.common_utils.data_utils import get_heavy_rate_device_info
    # dat_ = get_heavy_rate_device_info(self.net,self.ss_config,self.rlt_data_dict)
    # from alg.common_utils.data_utils import get_power_balance_data_curve
    # dat_ = get_power_balance_data_curve(cal_result_dict=case_rlt_ana.case_rlt_cal, is_all=False , time_list = [100,103,104],
    #                          area_name = '全省', area_details_dict = area_details_dict)
    # from alg.common_utils.data_utils import get_allzone_psm_value, get_power_supply_distribute
    # dat_ = get_allzone_psm_value(index_ana.indicator_rlt_dict, self.rlt_data_dict, area_details_dict,
    #                              case_rlt_ana.case_rlt_cal['consump_rate']['zone'],
    #                              case_rlt_ana.case_rlt_cal['cutailment_hours']['zone'])
    # dat2_ = get_power_supply_distribute(case_rlt_ana.case_rlt_cal)
    # from alg.common_utils.data_utils import get_newenergy_curtailment_data
    # dat_ = get_newenergy_curtailment_data(self.ss_config,case_rlt_ana.case_rlt_cal)
    # from alg.common_utils.data_utils import get_allzone_indicators
    # dat_ = get_allzone_indicators(index_ana.indicator_rlt_dict, time_no=10, area_name='全省',
    #                               area_details=area_details_dict, result_dict=self.rlt_data_dict)
    # from alg.common_utils.data_utils import get_zone_loadinsufficiency_data
    # dat_ = get_zone_loadinsufficiency_data(self.rlt_data_dict, area_name='全省',
    #                                        area_details_dict=area_details_dict,bload_series=True, bzone_detail=True, bdetail=True)
    # from alg.common_utils.data_utils import get_zone_trafo_capability_timestep
    # dat_ = get_zone_trafo_capability_timestep(self.net, self.ss_config, self.rlt_data_dict, net_ptdf=case_sta.ptdf,
    #                                           timestep=1, area_name='商丘', area_details=area_details_dict)
    # from alg.common_utils.data_utils import get_device_loadratio_info
    # dat_ = get_device_loadratio_info('trafo', 58, self.rlt_data_dict,
    #                                  devcie_limit=case_rlt_ana.case_rlt_cal['device_limit'],
    #                                  config=self.ss_config)
    # from alg.common_utils.data_utils import get_all_device_loadratio_info
    # dat_ = get_all_device_loadratio_info(self.rlt_data_dict, area_name='全省')
    # from alg.common_utils.data_utils import get_device_loadratio_timestep
    # dat_ = get_device_loadratio_timestep(self.net, self.rlt_data_dict, time_no=91, config=self.ss_config,
    #                                      area_name='全省', area_detail=area_details_dict)
    # from alg.common_utils.data_utils import get_allgen_electric_hours
    # dat_ = get_allgen_electric_hours(case_rlt_ana.case_rlt_cal, config=self.ss_config)

    # import time
    # from alg.common_utils.balance_data_utils import get_zone_margin_qr_data
    # start_time = time.time()
    # dat_ = get_zone_margin_qr_data(config=self.ss_config, indicator_rlt_dict=index_ana.indicator_rlt_dict,
    #                                area_name="盐城中", valueType="psm")
    # execution_time = (time.time() - start_time) * 1000  # 转换为毫秒
    # print(f"代码块执行时间: {execution_time:.3f} 毫秒")
    return


def case_test_func2(net, config, result_dict, rlt_data_dict, area_details_dict, indicator_dict):
    """
    测试函数2
    :param rlt_data_dict:
    :return:
    """
    import time

    from alg.common_utils.data_utils import get_zone_psm_timeseries_data,get_grid_margin_data
    time_list = [i+2520 for i in range(24)]
    psm_time_data = get_zone_psm_timeseries_data(indicator_rlt_dict=indicator_dict, time_no=time_list)
    psm_qr_data = get_grid_margin_data(
        net=net, result_dict=result_dict, config=config, indicator_rlt_dict=indicator_dict
    )
    # from alg.common_utils.data_utils import get_zone_balance_data_curve
    # start_time = time.time()
    # crv_data = get_zone_balance_data_curve(net=net, config=config, result_dict=result_dict, area_name="全省",
    #                                        area_details_dict=area_details_dict, is_all=False,
    #                                        time_list=[i for i in range(24)],
    #                                        zone_psm_dict=indicator_dict["indicator_data"])
    # execution_time = (time.time() - start_time) * 1000  # 转换为毫秒
    # print(f"代码块执行时间: {execution_time:.3f} 毫秒")
    #
    # from alg.common_utils.balance_data_utils import get_zone_psm_time_distribute
    # zone_name_list = ["访晋", "泰扬北"]
    # start_time = time.time()
    # data_psm = get_zone_psm_time_distribute(config=config, zone_name_list=zone_name_list,
    #                                         indicator_data=indicator_dict["indicator_data"])
    # execution_time = (time.time() - start_time) * 1000  # 转换为毫秒
    # print(f"代码块执行时间: {execution_time:.3f} 毫秒")
    #
    from alg.common_utils.data_utils import get_simulation_power_boundry
    start_time = time.time()
    dat2_ = get_simulation_power_boundry(indicator_dict['boundary_data'], timestep=10, area_name='全省',net=net)
    execution_time = (time.time() - start_time) * 1000  # 转换为毫秒
    print(f"代码块执行时间: {execution_time:.3f} 毫秒")

    from alg.common_utils.data_utils import get_zone_elements_and_inds
    save_dir = "./zone_ele_inds"
    start_time = time.time()
    get_zone_elements_and_inds(net, config, area_details_dict=area_details_dict,
                                                indicator_rlt_dict=indicator_dict, save_dir=save_dir)
    execution_time = (time.time() - start_time) * 1000  # 转换为毫秒
    print(f"代码块执行时间: {execution_time:.3f} 毫秒")

    return


if __name__ == '__main__':
    ## 对算例进行预统计分析
    case_id = '2123214455656766'

    # cal_start_time = None

    case_file = 'D:\\code\\baogong_sys\\baogong-api\\alg\\teap_data\\result_henan_v4.2026算例-增加分布式新能源-负荷曲线修改.20240531_163718.teap'
    case_address = 'henan'

    # case_file = "D:\\code\\baogong_sys\\baogong-api\\alg\\teap_data\\result_jiyuan_2025济源_202406251430.teap"
    # case_address = 'jiyuan'

    # case_file = "D:\\code\\baogong_sys\\baogong-api\\alg\\teap_data\\result_hn2030_火电必开_三回直流.20240612_083434-弃风光%2B坐标%2B断面.20240715_103914.teap"
    # case_address = 'hunan'

    # case_file = "D:\\code\\baogong_sys\\baogong-api\\alg\\teap_data\\result_js2024全年基础算例0717-含储能-基础算例.teap"
    # case_file = "D:\\code\\baogong_sys\\baogong-api\\alg\\teap_data\\result_js全年-游圌优化-日调节上限4.teap"
    # case_file = "E:\\保供\\江苏\\result_2025时序_20250102.teap"
    # case_address = 'jiangsu'

    # case_file = "D:\\code\\baogong_sys\\baogong-api\\alg\\teap_data\\result_240715甘肃2025年_无陇东_无翼珍协鑫中核核储瑞竹孤立节点加坐标20240708.teap"
    # case_address = 'gansu'

    ## 调用接口函数测试;
    cal_start_time = '2026-01-01 00:00:00'
    TeapAna_main(case_id, case_file, case_address, case_start_time=cal_start_time, tofile=True)
