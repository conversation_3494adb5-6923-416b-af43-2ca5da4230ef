# -*- coding: utf-8 -*-

from datetime import datetime
from functools import lru_cache
from hashlib import md5
from shutil import copyfile
from random import randint

import os
import sys
import socket
import queue
import json
import yaml

# 启动时间
app_up_datetime = datetime.now()
# 应用标题
app_title = "TEAP能源分析平台"
# echarts 水印文本
watermark_text = "TODE Co., Ltd."
# 开关参数 是否展示启动入场动画
show_splash: bool = True
# 开关参数 用于控制是否为试用版 初始化赋值为 True （即为试用版）
add_watermark: bool = True
# 开关参数 用于控制是否为开放 BPA 文件转换 初始化赋值为 False （即不开放该功能）
allow_bpa: bool = False
# 配置参数 启动程序后自动打开的网页地址后缀
index_url_suffix = "/"
# 磁盘剩余空间最小值(单位:GB) 小于该值则触发告警
min_disk_free_space = 4
# 指磁盘已用空间占用比最大值 超过该值则触发告警
max_disk_usage_percent = 80
# 算例文件阈值大小 超过此大小 则采用新方法读取 load_case
case_file_threshold_size = 2 * 1024 * 1024
# 协同规划最大任务执行时长 (单位: 秒) 超过此时间限制 则终止任务线程 并返回失败信息
planning_max_seconds = 3600


########################################################################################################################
# ########################################### APP DIRECTORY CONFIG ################################################### #
########################################################################################################################

def init_dir(directory: str):
    """
    初始化文件夹 没有则创建该文件夹
    :param directory:
    :return:
    """
    if not os.path.isdir(directory):
        os.mkdir(directory)
    return directory


basedir = os.path.abspath(os.path.dirname(__file__))


# 默认的 teap日志、上传/下载文件存放路径
teap_temp_dir = basedir
# 在windows下 检查校正teap临时文件路径
if sys.platform.startswith("win"):
    try:
        # 优先判断代码/可执行文件路径是否有可写权限 如果有则使用该路径作为teap临时文件路径
        # 以避免多磁盘分区windows用户情况下 大量占用用户C盘空间
        with open(os.path.join(basedir, 'teap_up_datetime'), 'w') as f:
            f.write(str(app_up_datetime))
        # print(f"尝试在 '{basedir}' 路径 初始化创建文件成功！")
    except Exception as e:
        print(f"尝试在 '{basedir}' 路径 初始化创建文件失败: {e}")
        # 尝试在windows下将 teap日志、上传/下载文件存放路径 更改为当前用户appdata路径
        app_data_path = os.environ.get("APPDATA")
        if os.path.isdir(app_data_path):
            try:
                company_path = init_dir(os.path.join(app_data_path, "TODE"))
                teap_dir = init_dir(os.path.join(company_path, "teapPCS"))
                teap_temp_dir = teap_dir
            except Exception as e:
                print(f"尝试在 '{app_data_path}' 路径 初始化创建文件夹失败: {e}")

# teap日志文件存放路径
# log_dir = init_dir(os.path.join(teap_temp_dir, "logs"))
log_dir = os.path.join(basedir, "logs")
# # teap上传文件存放路径
# upload_file_path = init_dir(os.path.join(teap_temp_dir, "upload"))
# # teap下载文件存放路径
# download_file_path = init_dir(os.path.join(teap_temp_dir, "download"))
# # teap模版文件存放路径
# template_file_path = init_dir(os.path.join(teap_temp_dir, "template"))
# # 前端打包后的文件夹
# front_end_dir = init_dir(os.path.join(basedir, 'front_end'))
# # 协同规划-进程停止标志位文件
# stop_thread_flag_file = os.path.join(teap_temp_dir, "__stop__")
# # 协同规划-进程出错标志位文件
# error_thread_flag_file = os.path.join(teap_temp_dir, "__error__")
# # teapcase h5文件存放路径
# teapcase_h5_file_path = init_dir(os.path.join(teap_temp_dir, "teapcase_h5"))
# # 用于保供项目的teap_data文件存放路径
# teap_data_file_path = init_dir(os.path.join(teap_temp_dir, "teap_data"))
# # 用于保供项目的result_files文件存放路径
# result_files_file_path = init_dir(os.path.join(teap_temp_dir, "result_files"))

########################################################################################################################
# ########################################### JSON CONF FILE CLASS ################################################### #
########################################################################################################################

class JsonConfig:

    json_config_file = os.path.join(teap_temp_dir, "config.json")

    @classmethod
    @lru_cache(maxsize=1)
    def read(cls):
        try:
            if os.path.isfile(cls.json_config_file):
                with open(cls.json_config_file, "r") as fd:
                    load_dict = json.load(fd)
                    return load_dict
            else:
                return {}
        except Exception as err:
            print(f"read json_config_file error: {err}")
            return {}

    @classmethod
    def update(cls, dic):
        old_dic = cls.read()
        old_dic.update(dic)
        with open(cls.json_config_file, "w") as fd:
            json.dump(old_dic, fd)

        cls.read.cache_clear()

    @classmethod
    def get(cls, key, default_value=None, target_type=None):
        if target_type is None:
            return cls.read().get(key, default_value)

        try:
            return target_type(cls.get(key, default_value))
        except (ValueError, TypeError):
            return default_value

    @classmethod
    def get_config_dict(cls, keys):
        config_dic = cls.read()
        return {key: config_dic.get(key) for key in keys}


########################################################################################################################
# ############################################ APP VERSION CONFIG #################################################### #
########################################################################################################################

# 默认项目版本号
default_app_version = "0.3.0401"
# 项目版本号 默认读取当前路径下 的 version  文件 如果读取失败 则使用默认值
try:
    with open(os.path.join(basedir, 'version'), 'r') as f:
        app_version = f.read().strip()
except Exception:
    app_version = default_app_version

########################################################################################################################
# ############################################# LOG QUEUE CONFIG ##################################################### #
########################################################################################################################
log_formatter_function = """function log_formatter(log_content) {
    return new Date().Format('MM-dd HH:mm:ss')+': '+log_content
}"""


def log_formatter(log_content, log_level: str = ""):

    if '\n' in log_content:
        log_content_list = log_content.split('\n')
        return '\n'.join([log_formatter(v) for v in log_content_list])

    return f"{datetime.now().strftime('%m-%d %H:%M:%S')} {(log_level+' ' if (log_level != '') else '')}{log_content}"


log_queue = queue.Queue()
log_queue.push = lambda data: log_queue.put_nowait(json.dumps((
    data if ("teap_log" not in data) else
    {**data, "teap_log": log_formatter(data['teap_log'])}
)))

# 消息队列 queue 压入/弹出 数据打印日志时 允许打印数据长度最大值
#   如果数据长度超过配置的最大值 则只打印长度值 不打印内容
#   如果配置值为0 则打印完整数据内容到日志
max_log_content_len: int = 100

# 持久化缓存文件相关配置
cache_file_prefix = '__teap_cache_'
cache_file_name = f'{cache_file_prefix}{md5(app_version.encode()).hexdigest()}__'
cache_file_path = os.path.join(teap_temp_dir, cache_file_name)

########################################################################################################################
# ############################################ CHECK LICENSE FILE #################################################### #
########################################################################################################################
teap_license = os.path.join(basedir, 'teap.license')


def check_license() -> tuple:

    try:
        check_res = yaml.load(open(teap_license), Loader=yaml.FullLoader)
        return True, check_res
    except Exception as err:
        return False, str(err)


private_rsa = """
MIIC1TBPBgkqhkiG9w0BBQ0wQjAhBgkrBgEEAdpHBAswFAQIKAmfQAKpsm0CAkAA
AgEIAgEBMB0GCWCGSAFlAwQBAgQQEanR3qAHCv9r1ZlJ2U5ClgSCAoC0eDSJc9EQ
1kes2SOw465TKY1IXmOjPR18zkdc7cB1jrioC5kBeVDzTqCKnysAjXQ0QFMNym7v
zQoGdb48whYAJgsVt5HCg2bMhWPNt/UUvf7XYLk2dI/LtH1ZxhIkgQYakjqUgBMz
zRw5yR+6raDzh+XNMCiO1yi1lKNQ/rswbXbrcHYNNvb/5/ZU8V6XjpyUQv3R3R0A
fnuTKUFaDrhsZPOTWUMJ3vcg4+UQTNsvi+jGmNL2B+O9HNc6dc/7jng++LreUQnO
u7hr2EvNaXNulOYuFAozpdRTjwU/M40QIPcfWTTt3pagBK/zvg1wGdQ4Mj357xEw
2X3OpxhYR6tsDhXY87R6IiOGSGvu+XawDc2kTCXikEAxfZVFcNPdU703g0XLyDH9
T6yUhHoVTP6/ozjvXExvC6FQq/DSerghe8d47+RF56LIZXJxDu5ng34qsohhinRf
mNdT3umev06NPMydknR9P9iPAinjD+dQlXqSUt5gIQqBHbLoufuR/mc5r8LN1Om5
aHjVSa7Iy51xja39alBczgrqxh6ASVGn57+MGOMhoX4XVWsjFZiRWlxnTZVsWN1p
iY4jCSkGRPdqItj1YYEFgmpxuKZHaJSXXNRUPr2r5PC27Jfb07n/Di2R1qPmRy4Q
3Aw3/3EF15bh3mz2TLDElVN5RloGNfYWYYy9wVd+gCEahREgyh53vZLJMA2SiQRW
7v4XV1uoXsWjdohSpQ0w9np1cY7ZOUUdc7ZFbApjupEcPACN4HzcQpSdGdxujbKU
UMccCwRV8/BEzs97+1m49q5Ca5ydqsEbV+VCONe1TWbShweDgoWJul1F7ByYz3l0
8mlD5luJJeew"""

origin_str = "tode"
rsa_private_key = b"tode"
passphrase = "tode"
__tode_lic__ = "tode"

try:
    flag, check_license_result = check_license()
    if flag is False:
        raise RuntimeError('license error')

    add_watermark = check_license_result.get('add_watermark', True)
    allow_bpa = check_license_result.get('allow_bpa', False)
    app_title_suffix = check_license_result.get('app_title_suffix', "")
    solver_debug = check_license_result.get('solver_debug', False)
    solver_threads = check_license_result.get('solver_threads', 2)
    pcs_loop_interval = check_license_result.get('pcs_loop_interval', 1.0)

    tode_lic = check_license_result.get('tode_lic')

    if tode_lic != __tode_lic__:
        raise RuntimeError('license error')

    en_str = check_license_result.get('en_str')

    from Crypto.PublicKey import RSA
    from Cryptodome.Cipher import PKCS1_v1_5
    private_key = RSA.import_key(rsa_private_key, passphrase=passphrase)
    cipher_rsa = PKCS1_v1_5.new(private_key)
    de_data = cipher_rsa.decrypt(en_str, None)

    if bytes.decode(de_data) != origin_str:
        add_watermark: bool = True
        allow_bpa: bool = False
    else:
        index_url_suffix = check_license_result.get('index_url_suffix', '/')

except (AssertionError, ImportError, ValueError, RuntimeError):

    add_watermark: bool = True
    allow_bpa: bool = False
    app_title_suffix: str = ""
    solver_debug: bool = False
    pcs_loop_interval: float = 1.0
    solver_threads: int = 2

    # develop mode
    if origin_str == passphrase == __tode_lic__:
        allow_bpa: bool = True
        app_title_suffix: str = " Beta"
        solver_debug: bool = False

app_title += app_title_suffix

trial_version: bool = add_watermark


########################################################################################################################
# ############################################## APP PORT CONFIG ##################################################### #
########################################################################################################################

# web 应用默认端口号
default_app_port = 20426
# multiprocessing.managers 访问 authkey
auth_key = b'tode'


def port_is_occupied(port):
    s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    s.settimeout(0.1)
    try:
        s.connect(("127.0.0.1", int(port)))
        s.shutdown(2)
        return True
    except Exception:
        return False


def manager_server_is_running(port):

    from multiprocessing.managers import BaseManager

    try:
        manager = BaseManager(address=('127.0.0.1', port), authkey=auth_key)
        manager.connect()
        return True
    except ConnectionRefusedError:
        pass

    return False


manager_port = JsonConfig.get('manager_port', 50002, int)
if port_is_occupied(manager_port):
    if not manager_server_is_running(manager_port):
        for i in range(30000):
            if not port_is_occupied(manager_port):
                JsonConfig.update({'manager_port': manager_port})
                break
            else:
                manager_port = randint(20001, 50001)

# 最大任务并行数 通过框定线程池大小实现控制
max_parallel = 20
# 页面上 并行进程数 允许的最大值
user_max_parallel_num = 12

########################################################################################################################
# ############################################## FLASK APP CONFIG #################################################### #
########################################################################################################################
os.environ.setdefault('SECRET_KEY', md5(os.urandom(24)).hexdigest())  # 随机产生24位的字符串


class BaseConfig:

    DEBUG = False

    SECRET_KEY = os.getenv('SECRET_KEY', 'tode secret key')

    BLUEPRINT_NAME = ""

    # flask-docs config
    # see all in "https://github.com/kwkwc/flask-docs"

    # Using CDN
    # API_DOC_CDN = True

    # Disable document pages
    # API_DOC_ENABLE = False

    # SHA256 encrypted authorization password, e.g. here is admin
    # echo -n admin | shasum -a 256
    API_DOC_PASSWORD_SHA2 = "35a6f87a24196516a7e00c8c1b9667608ded78862411a9f765d6c8299f59ded4"

    # Methods allowed to be displayed
    # API_DOC_METHODS_LIST = ["GET", "POST", "PUT", "DELETE", "PATCH"]

    # Custom url_prefix
    API_DOC_URL_PREFIX = "/docs/"

    # RESTful Api class name to exclude
    # API_DOC_RESTFUL_EXCLUDE = ["Todo"]

    # Name of the Api blueprint to be displayed
    API_DOC_MEMBER = ["teap_api"]

    # Name of the Submembers Api function to be excluded
    # API_DOC_MEMBER_SUB_EXCLUDE = ["delete_data"]

    # Auto generating request args markdown
    # API_DOC_AUTO_GENERATING_ARGS_MD = True

    # Disable markdown processing for all documents
    # API_DOC_ALL_MD = False

    SCHEDULER_EXECUTORS = {
        'default': {'type': 'threadpool', 'max_workers': max_parallel},
        'cleaner': {'type': 'threadpool', 'max_workers': 10},
        'process': {'type': 'processpool', 'max_workers': 2},
    }

    @staticmethod
    def init_app(app):
        pass


class TeapConfig(BaseConfig):

    BLUEPRINT_NAME = "teap"

    JOBS = [
        {  # 任务0 定时任务 启动 manager server
            'id': 'manager_server',
            'func': 'app.schedule_task.manager_server:serve_forever',
            'args': [manager_port, auth_key],
            'replace_existing': True,
            'misfire_grace_time': 120,
            'max_instances': 1,
            'trigger': 'date',
            "next_run_time": datetime.now(),
            "executor": "process"
        },
        {  # 任务1 定时任务 启动 db task listener
            'id': 'task_listener',
            'func': 'app.schedule_task.task_listener:task_listener',
            'replace_existing': True,
            'misfire_grace_time': 120,
            'max_instances': 1,
            'trigger': 'date',
            "next_run_time": datetime.now(),
            "executor": "default"
        },
        # {  # 任务1 定时任务 打开本地默认浏览器 访问app首页
        #     'id': 'open_browser',
        #     'func': 'app.schedule_task.open_browser:open_browser',
        #     'replace_existing': True,
        #     'misfire_grace_time': 120,
        #     'max_instances': 1,
        #     'trigger': 'date',
        #     "next_run_time": datetime.now(),
        #     "executor": "process"
        # },
    ]


config = {
    'teap': TeapConfig,
    'default': TeapConfig
}

########################################################################################################################
# ###################################### TEAP CALCULATION AND PAGE CONFIG ############################################ #
########################################################################################################################

# 功率平衡曲线堆叠图数据项目 中英文对应名称
power_balance_line_chart_name_dict = {
    'nuclear_output': '核电', 'feedin_output': '区外来电', 'hydropower_output': '水电', 'gen_output': '火电',
    'storage_output': '储能', 'wind_output': '风电', 'solar_output': '光伏', 'wind_curtailment': '弃风',
    'solar_curtailment': '弃光', 'load_curtailment_data': '负荷削减量', 'load_data': '负荷'
}
# 功率平衡曲线堆叠图数据项目 展示排序
power_balance_line_chart_sorted_name_list = [
    'nuclear_output', 'feedin_output', 'hydropower_output', 'gen_output', 'storage_output', 'wind_output',
    'solar_output', 'wind_curtailment', 'solar_curtailment', 'load_curtailment_data', 'load_data'
]

# 默认选中的功能 (a: 时序运行模拟 ; b: 协同规划 ;)

default_selected_fun_id = "a"

# 变量列表 (变量编号 变量展示名 存储[变量可选条件的编号]的列表)

var_list = [
    ('1', '弃风率', "wind_curtail_proportion", ["1"]),
    ('2', '弃光率', "solar_curtail_proportion", ["1"]),
    ('3', '新能源弃电率', "renewable_curtail_proportion", ["1"]),
    ('4', '风电电量占比', "wind_energy_proportion", ["2"]),
    ('5', '光伏电量占比', "solar_energy_proportion", ["2"]),
    ('6', '新能源电量占比', "renewable_energy_proportion", ["2"]),
    # ('7', '弃水率', "hydropower_curtail_proportion", ["1"]),
]

# 条件列表

condition_list = [
    ('1', '≤', '<='),
    ('2', '≥', '>='),
    ('3', '=', '=='),
]

# 目标函数按钮前缀html + 后缀html
obj_func_prefix = """&nbsp;""" \
                  """<button contenteditable="false" type="button" class="btn btn-primary mt-1 mb-1" """ \
                  """style="display: inline-block;">"""
obj_func_suffix = """&nbsp;&nbsp;""" \
                  """<i class="fas fa-window-close" onclick="(function f(e) """ \
                  """{e.parentNode.parentNode.removeChild(e.parentNode);})(this)"></i></button>&nbsp;"""

# 目标函数列表
obj_func_list = [
    ('1', '风电装机容量', 'cap_wind_install'),
    ('2', '光伏装机容量', 'cap_solar_install'),
    ('3', '发电机运行费用', 'gen_operate_cost'),
    ('4', '发电机启停费用', 'gen_on_off_cost'),
    ('5', '风电弃电惩罚费用', 'wind_curtail_cost'),
    ('6', '光伏弃电惩罚费用', 'solar_curtail_cost'),
    ('7', '风电装机费用', 'wind_install_cost'),
    ('8', '光伏装机费用', 'solar_install_cost'),
    ('9', '储能装机费用', 'storage_install_cost'),
    ('10', '区外调节费用', 'feedin_cost'),
    ('11', '水电弃电惩罚费用', 'hydropower_curtail_cost'),
]

# 模式选择
mode_select_list = [
    (
        "1", "风电规划--系统总运行费用与风电装机费用最小",
        {
            'variable_map': [1, 0, 0, 0],
            'constraint_condition': {'wind_curtail_proportion': '<='},
            'constraint_value': {'wind_curtail_proportion': 5},
            'obj_func': 'gen_operate_cost+gen_on_off_cost+wind_install_cost'
        }
    ),
    (
        "2", "光伏规划--系统总运行费用与光伏装机费用最小",
        {
            'variable_map': [0, 1, 0, 0],
            'constraint_condition': {'solar_curtail_proportion': '<='},
            'constraint_value': {'solar_curtail_proportion': 5},
            'obj_func': 'gen_operate_cost+gen_on_off_cost+solar_install_cost'
        }
    ),
    (
        "3", "储能规划--系统总运行费用与储能装机费用最小",
        {
            'variable_map': [0, 0, 1, 1],
            'constraint_condition': {'renewable_curtail_proportion': '<='},
            'constraint_value': {'renewable_curtail_proportion': 3},
            'obj_func': 'gen_operate_cost+gen_on_off_cost+storage_install_cost'
        }
    ),
    (
        "4", "风电光伏协同规划--系统总运行费用与风电光伏装机费用最小",
        {
            'variable_map': [1, 1, 0, 0],
            'constraint_condition': {'renewable_curtail_proportion': '<='},
            'constraint_value': {'renewable_curtail_proportion': 5},
            'obj_func': 'gen_operate_cost+gen_on_off_cost+wind_install_cost+solar_install_cost'
        }
    ),
    (
        "5", "风电储能协同规划--系统总运行费用与风电储能装机费用最小",
        {

            'variable_map': [1, 0, 1, 1],
            'constraint_condition': {'wind_curtail_proportion': '<='},
            'constraint_value': {'wind_curtail_proportion': 5},
            'obj_func': 'gen_operate_cost+gen_on_off_cost+wind_install_cost+storage_install_cost'
        }
    ),
    (
        "6", "光伏储能协同规划--系统总运行费用与光伏储能装机费用最小",
        {
            'variable_map': [0, 1, 1, 1],
            'constraint_condition': {'solar_curtail_proportion': '<='},
            'constraint_value': {'solar_curtail_proportion': 5},
            'obj_func': 'gen_operate_cost+gen_on_off_cost+solar_install_cost+storage_install_cost'
        }
    ),
    (
        "7", "风电光伏储能协同规划--系统总运行费用与风电光伏储能装机费用最小",
        {
            'variable_map': [1, 1, 1, 1],
            'constraint_condition': {'renewable_curtail_proportion': '<='},
            'constraint_value': {'renewable_curtail_proportion': 5},
            'obj_func': 'gen_operate_cost+gen_on_off_cost+wind_install_cost+solar_install_cost+storage_install_cost'
        }
    ),
    (
        "8", "风电规划--系统风电最大装机容量",
        {
            'variable_map': [1, 0, 0, 0],
            'constraint_condition': {'wind_curtail_proportion': '<='},
            'constraint_value': {'wind_curtail_proportion': 5},
            'obj_func': '-cap_wind_install'
        }
    ),
    (
        "9", "光伏规划--系统光伏最大装机容量",
        {
            'variable_map': [0, 1, 0, 0],
            'constraint_condition': {'solar_curtail_proportion': '<='},
            'constraint_value': {'solar_curtail_proportion': 5},
            'obj_func': '-cap_solar_install'
        }
    ),
    (
        "10", "风电光伏协同规划--系统风电光伏最大总装机容量",
        {
            'variable_map': [1, 1, 0, 0],
            'constraint_condition': {'renewable_curtail_proportion': '<='},
            'constraint_value': {'renewable_curtail_proportion': 5},
            'obj_func': '-(cap_wind_install+cap_solar_install)'
        }
    ),
]

# 时序模拟启动参数描述
arg_desc_dict: dict = {

    "up_reserve_cof_desc": "系统所需的负荷上备用容量占时序负荷的百分比",  # 负荷上备用率
    "down_reserve_cof_desc": "系统所需的负荷下备用容量占时序负荷的百分比",  # 负荷下备用率
    "emergency_reserve_cof_desc": "系统所需的事故备用容量占时序负荷的百分比",  # 事故备用率

    # "reserve_coefficient_desc": "为负荷提供的备用占时序负荷的百分比",  # 负荷备用系数
    "wind_coefficient_desc": "为风电提供的备用占时序出力的百分比",  # 风电备用系数
    "solar_coefficient_desc": "为光伏提供的备用占时序出力的百分比",  # 光伏备用系数

    "reserve_constraint_desc": "是否考虑系统备用约束",  # 开关参数
    "generator_on_off_desc": "是否考虑机组的启停约束",  # 开关参数
    "generator_ramping_desc": "是否考虑机组的爬坡约束",  # 开关参数

    "line_constraint_desc": "是否忽略系统网络约束",  # 开关参数
    "interface_constraint_desc": "是否考虑断面约束",  # 开关参数

    "roll_day_desc": "单次滚动计算小时数",  # 滚动小时数
    "lookahead_day_desc": "单次滚动考虑的未来小时数",  # 前瞻小时数
    "mip_gap_desc": "计算的目标值和理论最优值之间的相对误差",  # MIP Gap


    "parallel_num_desc": "全年时序分段并行数",  # 分段数
    "max_rollback_day_desc": "无解时向前回滚的最大轮次数",  # 最大回滚次数
    "time_limit_desc": "单次计算的最长时间",  # 单次滚动时限

    "wind_curtail_cof_desc": "风电弃电惩罚系数",  # 弃风惩罚

    "load_relax_cof_desc": "负荷松弛的惩罚系数",  # 负荷松弛惩罚
    "pbr_relax_cof_desc": "线路潮流越限的惩罚系数",  # 潮流松弛惩罚
    "overlap_number_desc": "时序分解重叠周期数",  # 时序分解重叠

    "solar_curtail_cof_desc": "光伏弃电惩罚系数",  # 弃光惩罚

    "planning_max_seconds_desc": "协同规划计算时间上限",  # 规划计算时间上限(秒)

    # "parallel_desc": "是否采用时序分段并行计算",  # 时序分解
    # "rollback_desc": "计算无解时是否向前回滚",  # 自动回滚
    #
    # "auto_adjust_desc": "根据系统的调节成本计算最优弃电惩罚",  # 自动优化弃电惩罚
}

# 首页各个参数默认值配置字典
arg_default_val_dict: dict = {

    "default_up_reserve_cof": "5",  # 负荷上备用率
    "default_down_reserve_cof": "2",  # 负荷下备用率
    "default_emergency_reserve_cof": "0",  # 事故备用率

    "default_wind_coefficient": "0",  # 风电备用系数
    "default_solar_coefficient": "0",  # 光伏备用系数

    "default_reserve_constraint": True,  # 备用约束
    "default_generator_on_off": True,  # 机组启停约束
    "default_generator_ramping": True,  # 机组爬坡约束

    "default_line_constraint": False,  # 线路和主变约束
    "default_interface_constraint": False,  # 断面约束

    "default_roll_day": "24",  # 滚动小时数
    "default_lookahead_day": "0",  # 前瞻小时数
    "default_mip_gap": "5",  # MIP Gap

    "default_parallel_num_bool": True,  # 分段数
    "default_max_rollback_day": "1",  # 最大回滚次数
    "default_max_rollback_day_bool": True,  # 最大回滚次数
    "default_time_limit": "180",  # 单次滚动时限
    "default_time_limit_bool": True,  # 单次滚动时限

    "default_load_relax_cof": "3000",  # 负荷松弛惩罚
    "default_load_relax_cof_bool": True,  # 负荷松弛惩罚
    "default_pbr_relax_cof": "2000",  # 潮流松弛惩罚
    "default_pbr_relax_cof_bool": True,  # 潮流松弛惩罚
    "default_overlap_number": "5",  # 时序分解重叠
    "default_overlap_number_bool": True,  # 时序分解重叠

    "default_solar_curtail_cof": "1000",  # 弃光惩罚
    "default_solar_curtail_cof_bool": True,  # 弃光惩罚
    "default_wind_curtail_cof": "1000",  # 弃风惩罚
    "default_wind_curtail_cof_bool": True,  # 弃风惩罚
    "default_planning_max_seconds": 3600,  # 规划计算时间上限(秒)
    "default_planning_max_seconds_bool": True,  # 规划计算时间上限(秒)

    "default_api_address_bool": True,  # API地址默认

    # "default_reserve_coefficient": "为负荷提供的备用占时序负荷的百分比",  # 负荷备用系数

    # 目标函数 minimize_production_cost or minimize_co2_emission
    "default_pcs_objective_function": "minimize_production_cost",
    "pcs_objective_function": "minimize_production_cost",

    # 考虑网损
    "default_consider_transmission_losses": False,
    "consider_transmission_losses": False,
}
