##
## @brief 获取拓扑信息及指定时刻的潮流数据;
## 通用接口,支持
##
import numpy as np
# import pandas as pd
import copy

import pandas as pd


def get_format_graph_data(net, vn_kv: int = 220, area_name=None, area_details: dict = dict()):
    """
    获取地理拓扑数据信息;无时刻断面的潮流数据;
    Args:
        full_bus: 所有的电网节点
        full_line: 所有的电网线路
        full_trafo: 所有的电网主变
        # area_name: 分区名;
        vn_kv:m默认电压等级
        area_name: 分区名称默认None,全网
        area_details: 分区关联设备索引信息;
    Returns:
        dict()
    """
    graph_bus_unique, graph_line_unique, graph_dcline_unique = select_available_device(net,
                                                                                       area_name=area_name,
                                                                                       disp_type=['DC', 'dc', 'switch',
                                                                                                  'station'],
                                                                                       area_details=area_details,
                                                                                       grp_vn_kv=vn_kv)
    full_dcline = net.dcline if "dcline" in net.keys() and len(net.dcline) > 0 else None
    graph_dict = get_graph_data(graph_bus_unique, graph_line_unique, net.line, net.trafo,
                                graph_dcline_unique=graph_dcline_unique, full_dcline=full_dcline)

    point_data, line_data, dcline_data = get_final_format_data(graph_dict)
    # n1_dev_data = graph_dict['N-1flag']
    # point_data_n1, line_data_n1 = get_final_format_data_keydev(n1_dev_data, point_data, line_data)

    topo_graph_dict = {
        'point_data': point_data,
        'line_data': line_data,
        'dcline_data': dcline_data,
        'point_data_n1': [],
        'line_data_n1': [],
        # 'point_data_n1': point_data_n1,
        # 'line_data_n1': line_data_n1,
    }

    return topo_graph_dict


def get_final_format_data(format_graph_data) -> tuple:
    """
    生成方便与前端服务进行交互格式的电网拓扑数据;
    Args:
        format_graph_data:

    Returns:

    """
    coords_name_dict = {
        str(item["coords"]): item["name"] for item in format_graph_data["bus"].values()
    }
    point_data = [
        {
            "bus_index": item["related_buses_idx"],
            "name": item["name"],
            "type": item["type"],
            "vn_kv": item["vn_kv"],
            "zone": item["zone"],
            # 城市坐标 前两个经纬度   #第三个负载率最大值
            "value": [
                *(item["coords"]),
                float(np.max(item["powerflow"]).round(4))
            ],
            "info": item["info"],
            "related_trafo_idx": item["related_trafo_idx"],
            "limit": np.array(item["limit"]).round(1).tolist(),
            "powerflow_detail": np.array(item["powerflow_detail"]).round(4).tolist()
        }
        for item in format_graph_data["bus"].values()
    ]

    zone_name_dict = {
        str(item["coords"]): item["zone"] for item in format_graph_data["bus"].values()
    }
    line_data_dict = dict()
    for i_ele in ["line", "dcline"]:  # line,dcline
        if i_ele not in format_graph_data:
            line_data_dict[i_ele] = list()
        else:
            line_data_dict[i_ele] = [
                {
                    # "index":,
                    "fromName": coords_name_dict[str(item["coords"][0])],  # 起点城市name
                    "toName": coords_name_dict[str(item["coords"][1])],  # 终点城市name
                    "from_zone": zone_name_dict[str(item["coords"][0])],  # 起点分区
                    "to_zone": zone_name_dict[str(item["coords"][1])],  # 终点分区
                    "value": item["powerflow"],  # 已经是负载率了,无需再除以限额;
                    "vn_kv": item["vn_kv"],
                    "coords": [
                        # 起点城市坐标
                        item["coords"][0],
                        # 终点城市坐标
                        item["coords"][1],
                    ],
                    "limit": np.array(item["limit"]).round(1).tolist(),
                    "limitDetail": item["limitDetail"],
                    "pRateDetail": item["pRateDetail"],
                    "info": item["info"],
                    "related_line_idx": item["related_line_idx"],
                }
                for item in format_graph_data[i_ele].values()
            ]
    return point_data, line_data_dict["line"], line_data_dict["dcline"]


def get_final_format_data_2(format_graph_data, key: str = "N-1flag") -> tuple:
    """
    point_data_2, line_data_2 = get_final_format_data_2(format_graph_data)
    :param format_graph_data:
    :param key: "N-1flag", "large_rate_diff"
    :return:
    """

    point_data, line_data, dcline_data = get_final_format_data(format_graph_data)
    point_data_2, line_data_2 = list(), list()
    if key in format_graph_data:
        if format_graph_data[key]["line"]:
            name_list = [f'{v["fromName"]}{v["toName"]}' for v in format_graph_data[key]["line"]]
            line_data_2 = [v for v in line_data if (f'{v["fromName"]}{v["toName"]}' in name_list)]

        if format_graph_data[key]["trafo"]:
            name_list = [f'{v["name"]}' for v in format_graph_data[key]["trafo"]]
            point_data_2 = [v for v in point_data if (f'{v["name"]}' in name_list)]

    return point_data_2, line_data_2, dcline_data


def get_final_format_data_keydev(key_dev_dict, graph_point: list, graph_line: list) -> tuple:
    """
    point_data_2, line_data_2 = get_final_format_data_2(format_graph_data)
    :param format_graph_data:
    :param key: "N-1flag", "large_rate_diff"
    key_dev_dict:  format_graph_data['key']
    graph_point : 地图的变电站list
    graph_line： 地图的线路list
    :return:
    """
    point_data_2, line_data_2 = list(), list()
    if key_dev_dict["line"]:
        name_list = [f'{v["fromName"]}{v["toName"]}' for v in key_dev_dict["line"]]
        line_data_2 = [v for v in graph_line if (f'{v["fromName"]}{v["toName"]}' in name_list)]

    if key_dev_dict["trafo"]:
        name_list = [f'{v["name"]}' for v in key_dev_dict["trafo"]]
        point_data_2 = [v for v in graph_point if (f'{v["name"]}' in name_list)]
    return point_data_2, line_data_2


def get_graph_data(graph_bus_unique, graph_line_unique, full_line, full_trafo, line_powerflow=None,
                   trafo_powerflow=None, graph_dcline_unique: pd.DataFrame = None, full_dcline=None,
                   dcline_powerflow=None, location_none: bool = False):
    """获取拓扑图数据

    Args:
        graph_bus_unique (_type_): _description_
        graph_line_unique (_type_): _description_
        full_trafo (_type_): _description_
        full_line (_type_): _description_
        line_powerflow、trafo_powerflow: 只有单时刻断面的潮流数据,shape :(m,1)
        location_none: 坐标为空的设备是否保留;
        graph_dcline_unique: 直流设备的信息; 例如游圌直流;
        dcline_powerflow: 直流设备的时刻潮流数据; 例如游圌直流;
    Returns:
        _type_: _description_
    """
    graph_dict = {'bus': {}, 'line': {}, 'dcline': {}}
    # dcline
    if graph_dcline_unique is not None and full_dcline is not None:
        graph_dict['dcline'] = get_graph_line_data(full_dcline, graph_line_unique=graph_dcline_unique,
                                                   line_powerflow=dcline_powerflow,
                                                   location_none=location_none, maxp_col_name="max_p_mw")
    else:
        graph_dict['dcline'] = dict()

    # loop all lines
    graph_dict['line'] = get_graph_line_data(full_line, graph_line_unique=graph_line_unique,
                                             line_powerflow=line_powerflow,
                                             location_none=location_none)

    # loop all trafos
    loop_idx = 0
    n_line = full_line.shape[0]
    full_trafo.loc[np.isnan(full_trafo.stable_limit_mw), 'stable_limit_mw'] = 10000
    for idx, row in graph_bus_unique.iterrows():
        if not location_none and (pd.isna(row['lon']) or pd.isna(row['lat'])):
            continue

        trafo_info = dict()
        for related_trafo_idx in row['related_trafo_idx']:
            trafo_info[full_trafo.loc[related_trafo_idx, 'name']] = related_trafo_idx + n_line

        if trafo_powerflow is not None:
            pf_list = (np.divide(trafo_powerflow[row['related_trafo_idx'], :].flatten(),
                                 full_trafo.loc[row['related_trafo_idx'], 'stable_limit_mw'].values)).tolist()
        else:
            pf_list = [0.0] * len(row['related_trafo_idx'])
        graph_dict['bus'][loop_idx] = {
            'related_buses_idx': row['related_buses_idx'],
            'related_trafo_idx': row['related_trafo_idx'],
            'name': row['dispname'],
            'zone': row['zone_name'],
            'coords': [row['lon'], row['lat']],
            'type': row['type'],
            'vn_kv': row['vn_kv'],
            'info': trafo_info,
            'powerflow': max(pf_list) if len(pf_list) > 0 else 0,
            'powerflow_detail': pf_list,
            'limit': full_trafo.loc[row['related_trafo_idx'], 'stable_limit_mw'].values.tolist()
        }
        loop_idx = loop_idx + 1

    return graph_dict


def get_graph_line_data(full_line, graph_line_unique, line_powerflow=None, location_none: bool = False,
                        maxp_col_name: str = "stable_limit_mw"):
    """
    获取地图展示的线路拓扑数据
    :return:
    """
    loop_idx = 0
    graph_line_data = dict()
    for idx, row in graph_line_unique.iterrows():
        related_line_idxs = [*row['related_line_idx']['pos'], *row['related_line_idx']['neg']]
        # 潮流
        if not location_none and (
                pd.isna(row['lon_from_bus']) or pd.isna(row['lat_from_bus']) or pd.isna(row['lon_to_bus']) or pd.isna(
            row['lat_to_bus'])):
            continue
        _coords = [[row['lon_from_bus'], row['lat_from_bus']], [row['lon_to_bus'], row['lat_to_bus']]]  # 默认方向是从from->to
        if line_powerflow is not None:
            _line_flow = np.sum(line_powerflow[row['related_line_idx']['pos'], :], axis=0) \
                         - np.sum(line_powerflow[row['related_line_idx']['neg'], :], axis=0)
            _line_flow_detail = line_powerflow[related_line_idxs, :].flatten()

            if _line_flow < -0.1:
                # 如果潮流反向,需要将起点和终点交换
                _coords = [[row['lon_to_bus'], row['lat_to_bus']], [row['lon_from_bus'], row['lat_from_bus']]]
        else:
            _line_flow = 0.0
            _line_flow_detail = np.zeros(len(related_line_idxs))
        line_info = dict()
        for related_line_idx in related_line_idxs:
            line_info[full_line.loc[related_line_idx, 'name']] = related_line_idx

        rate_i = abs(round(float(_line_flow / row[maxp_col_name]), 4))
        pRateDetail = (
            np.divide(_line_flow_detail, full_line.loc[related_line_idxs, maxp_col_name].values)).round(
            4)
        pDirection = [-1 if x < -0.1 else 1 for x in pRateDetail]

        graph_line_data[loop_idx] = {
            'related_line_idx': row['related_line_idx'],  # 关联线路的索引号;
            'coords': _coords,
            'powerflow': rate_i,
            'limit': row[maxp_col_name],
            'pRateDetail': np.abs(pRateDetail).tolist(),
            'pDirection': pDirection,  # 潮流方向;
            'limitDetail': full_line.loc[related_line_idxs, maxp_col_name].values.round(1).tolist(),
            'info': line_info,
            'vn_kv': row['vn_kv']
        }
        loop_idx = loop_idx + 1
    return graph_line_data


def select_available_device(net, disp_type=None, area_name=None,
                            area_details: dict = dict(), grp_vn_kv=220):
    """
    Args:
        full_bus: df
        full_line: df
        full_trafo: df
        disp_type: list
        area_name:  如果是获取分区拓扑,则为分区的：
        area_details:   分区包含的设备;
        grp_vn_kv: 过滤的电压等级;
    Returns:

    """
    # graph bus,筛选出分区内带有经纬度和显示名称的节点graph_bus_unique
    graph_bus = net.bus.loc[(~net.bus.lon.isna()) & (~net.bus.lat.isna()) & (net.bus.dispname != "")]

    # dc_line_bus
    if "dcline" in net.keys() and len(net.dcline) > 0:
        if area_name is not None and area_name in area_details.keys():
            zone_dcline = net.dcline[net.dcline.index.isin(area_details[area_name]['dcline'])]
            dc_bus_list = list(set(zone_dcline.from_bus.values.tolist() + zone_dcline.to_bus.values.tolist()))
        else:
            dc_bus_list = list(set(net.dcline.from_bus.values.tolist() + net.dcline.to_bus.values.tolist()))
    else:
        dc_bus_list = []

    # bus
    graph_bus_unique = get_graph_bus_unique(graph_bus, net.trafo, disp_type=disp_type, area_name=area_name,
                                            area_details=area_details, grp_vn_kv=grp_vn_kv, ext_bus=dc_bus_list)

    # Handling lines--分区内线路,需要首末端均在分区内;
    graph_line_unique = get_graph_line_unique(net.bus, graph_bus_unique, net.line, maxp_col_name="stable_limit_mw")

    # dcline
    if "dcline" in net.keys():
        graph_dcline_unique = get_graph_line_unique(net.bus, graph_bus_unique, net.dcline, maxp_col_name="max_p_mw")
    else:
        graph_dcline_unique = None
    return graph_bus_unique, graph_line_unique, graph_dcline_unique


def get_graph_bus_unique(graph_bus: pd.DataFrame, full_trafo: pd.DataFrame, disp_type=None,
                         area_name: str = None, area_details: dict = dict(),
                         grp_vn_kv: float = 0.0, ext_bus: list = list()):
    """
    :param graph_bus:
    :param full_trafo:
    :param disp_type:
    :param area_name:
    :param area_details:
    :param grp_vn_kv:
    :param ext_bus:  需要额外保留的节点序号;
    :return:
    """
    if area_name is not None and area_name != "全省":
        if area_details is None or area_details == {}:
            raise ValueError("查询分区拓扑,需传入分区关联设备映射!")
        elif area_name not in area_details:
            raise ValueError("分区关联设备映射中无分区:{area_name}!")
        else:
            pass
        graph_bus = graph_bus.loc[
            (graph_bus.index.isin(area_details[area_name]['bus']) & (graph_bus.vn_kv >= grp_vn_kv * 0.95)) | (
                graph_bus.index.isin(ext_bus))]
        trafos = full_trafo.loc[full_trafo.index.isin(area_details[area_name]['trafo'])]
    else:
        # 电压等级过滤
        # graph_bus = graph_bus.loc[graph_bus.vn_kv >= grp_vn_kv * 0.95]
        graph_bus = graph_bus.loc[(graph_bus.vn_kv >= grp_vn_kv * 0.95) | graph_bus.index.isin(ext_bus)]
        trafos = copy.deepcopy(full_trafo)

    if disp_type is not None and 'type' in graph_bus.columns.to_list():
        graph_bus = graph_bus.loc[graph_bus.type.isin(disp_type)]

    graph_bus_unique = graph_bus.drop_duplicates('dispname')  # 去除名称重复的节点名;

    # 筛选出graph_bus_unique的相关节点和*分区内*主变
    bus_related_buses = {}
    bus_related_trafo = {}
    for idx, row in graph_bus_unique.iterrows():
        # 筛选出目标区域内带显示名称的节点
        bus_related_buses[idx] = graph_bus.loc[
            graph_bus.dispname == row['dispname']].index.values.tolist()
        # 筛选出目标区域500kV以上且高压侧节点带显示名称的主变
        bus_related_trafo[idx] = trafos.loc[(trafos.hv_bus.isin(
            bus_related_buses[idx])) & (trafos.vn_hv_kv >= grp_vn_kv * 0.95)].index.values.tolist()
        graph_bus_unique.loc[idx, 'stable_limit_mw'] = np.sum(trafos.loc[bus_related_trafo[idx], 'stable_limit_mw'])
    graph_bus_unique['related_buses_idx'] = graph_bus_unique.index.map(bus_related_buses)
    graph_bus_unique['related_trafo_idx'] = graph_bus_unique.index.map(bus_related_trafo)
    return graph_bus_unique


def get_graph_line_unique(full_bus: pd.DataFrame, graph_bus: pd.DataFrame, full_line: pd.DataFrame,
                          maxp_col_name: str = "stable_limit_mw"):
    """
    获取graph的线路展示信息;
    :return:
    """
    # Handling lines--分区内线路,需要首末端均在分区内;
    # if area_name is not None:
    #     # 分区进一步处理500kV主变都在分区内，但节点不在分区拓扑内导致无法关联的两站间的500kV线路
    #     # zone_line_idx = get_lines_with_factorys(full_bus, full_line, graph_bus_unique)
    #     # cond1 = full_line.from_bus.isin(graph_bus.index) & full_line.to_bus.isin(graph_bus.index)
    #     # cond2 = full_line.index.isin(zone_line_idx)
    #     # graph_line = full_line.loc[cond1 | cond2]
    #     graph_line = full_line.loc[
    #         full_line.index.isin(area_details[area_name]['line']) & (full_line.vn_kv >= grp_vn_kv * 0.95)]
    # else:
    #     graph_line = full_line.loc[full_line.vn_kv >= grp_vn_kv * 0.95]
    bus_idx_list = set().union(*graph_bus['related_buses_idx'])
    graph_line = full_line.loc[full_line.from_bus.isin(bus_idx_list) & (full_line.to_bus.isin(bus_idx_list))]

    # use full_bus instead graph_bus to merge dispname .etc info
    graph_line = graph_line.merge(full_bus.loc[:, ['dispname', 'lon', 'lat']], left_on='from_bus',
                                  right_index=True)
    graph_line = graph_line.merge(full_bus.loc[:, ['dispname', 'lon', 'lat']], left_on='to_bus', right_index=True,
                                  suffixes=('_from_bus', '_to_bus'))

    graph_line_unique = graph_line.drop_duplicates(subset=['dispname_from_bus', 'dispname_to_bus'])
    # # 去除'dispname_from_bus' or 'dispname_to_bus' 为nan 或''的记录
    # graph_line_unique = graph_line_unique.loc[
    #     (graph_line_unique.dispname_from_bus.notna()) & (graph_line_unique.dispname_to_bus.notna())]

    # 筛选出graph_bus_unique的相关节点
    # graph_bus_dispname = graph_bus.dispname.values.tolist()
    # graph_line_unique = graph_line_unique.loc[
    #     (graph_line_unique.dispname_from_bus.isin(graph_bus_dispname)) & (
    #         graph_line_unique.dispname_to_bus.isin(graph_bus_dispname))]

    # If have from_bus and to_bus reversed lines
    reverse_match_list = []
    for idx, row in graph_line_unique.iterrows():
        if idx not in reverse_match_list:
            reverse_match_list.extend(graph_line_unique.loc[
                                          (graph_line_unique.dispname_from_bus == row['dispname_to_bus'])
                                          & (graph_line_unique.dispname_to_bus == row[
                                              'dispname_from_bus'])].index.values)
    if len(reverse_match_list) > 0:
        graph_line_unique = graph_line_unique.drop(index=reverse_match_list)

    # get related power data(line power) for each plot line
    line_related_lines = {}
    for idx, row in graph_line_unique.iterrows():
        line_related_lines[idx] = {'pos': graph_line.loc[(graph_line.dispname_from_bus == row['dispname_from_bus'])
                                                         & (graph_line.dispname_to_bus == row[
            'dispname_to_bus'])].index.values.tolist(),
                                   'neg': graph_line.loc[(graph_line.dispname_from_bus == row['dispname_to_bus'])
                                                         & (graph_line.dispname_to_bus == row[
                                       'dispname_from_bus'])].index.values.tolist()}
        graph_line_unique.loc[idx, maxp_col_name] = np.sum(
            full_line.loc[line_related_lines[idx]['pos'], maxp_col_name]) \
                                                    + np.sum(
            full_line.loc[line_related_lines[idx]['neg'], maxp_col_name])
    graph_line_unique['related_line_idx'] = graph_line_unique.index.map(line_related_lines)
    return graph_line_unique


def get_zone_inner_factorys(full_bus, grp_vn_kv=220, disp_type=None, area_name: str = None,
                            area_details: dict = dict()):
    """
    筛选全网/分区内的有坐标的dispname不为空的厂站;
    full_bus:  net.bus pd.DataFrame()
    grp_vn_kv: 过滤电压等级;
    """
    # 筛选出分区内带有经纬度和显示名称的节点graph_bus_unique
    graph_bus = full_bus.loc[~full_bus.lon.isna()]

    if area_name is not None:
        if area_details is None or area_details == {}:
            raise ValueError("查询分区拓扑,需传入分区关联设备映射!")
        elif area_name not in area_details:
            raise ValueError("分区关联设备映射中无分区:{area_name}!")
        else:
            pass
        graph_bus = graph_bus.loc[
            graph_bus.index.isin(area_details[area_name]['bus']) & (graph_bus.vn_kv >= grp_vn_kv * 0.95)]
    else:
        # 电压等级过滤
        graph_bus = graph_bus.loc[graph_bus.vn_kv >= grp_vn_kv * 0.95]

    if disp_type is not None and 'type' in graph_bus.columns.to_list():
        graph_bus = graph_bus.loc[graph_bus.type.isin(disp_type)]

    graph_bus_unique = graph_bus.drop_duplicates('dispname')  # 去除名称重复的节点名;

    return graph_bus_unique


def get_lines_with_factorys(full_bus, full_line, graph_bus_unique: pd.DataFrame, grp_vn_kv=500):
    """
    筛选首端/末端场站均在分区厂站列表中的>grp_vn_kv的线路集合
    graph_bus_unique: 分区关联有经纬度及显示dispname的节点;
    """
    # 筛选出不为空的所属分区内的厂站名
    bus_dispname_unique = graph_bus_unique['dispname'].unique().tolist()
    bus_dispname_unique = [i for i in bus_dispname_unique if (not pd.isna(i) and i != '')]

    # 根据 full_bus 筛选出 'dispname' isin(bus_dispname_unique)的所有bus
    dispbus_df = full_bus.loc[full_bus.dispname.isin(bus_dispname_unique) & (full_bus.vn_kv >= grp_vn_kv * 0.95)]
    relay_line_idx = full_line.loc[full_line.from_bus.isin(dispbus_df.index) & full_line.to_bus.isin(dispbus_df.index)]
    return relay_line_idx.index.tolist()
