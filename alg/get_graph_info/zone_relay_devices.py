"""
获取分区名称/分区列表/分区关联设备信息;
充分根据bus表中:
bus: dispname,节点展示名称;
zone:  最小分区 ，增加:zone_name;
owner: 区域1,增加：owner_name;
area: 区域2,增加：area_name;
-- 方法1：
---方法2:
根据拓扑搜索来确定各供电分区的关联母线;然后根据母线的topo_zone进行筛选处理;
"""
import pandas as pd
from pandapower import pandapowerNet


def GetZoneList(bus_df, config: dict):
    """
    获取所有分区dict
    Args:
        bus_df: 节点信息
    Returns:
    """
    zone_dict = dict()  # 分区名: key:分区名，val:包含的最小分区列表;
    # key: 最小供电分区(zone) + 普通二级分区(area,例如苏南苏北,豫西等);
    if 'zone' not in bus_df.columns:
        raise ValueError("节点表中缺少：zone 列;")
    else:
        bus_df['zone'] = bus_df['zone'].fillna('')

    # 检查是否有
    def colfillnan(_df, col):
        """
        fillnan 列;
        """
        _df[col] = _df[col].fillna('') if col in _df.columns else ''
        return _df

    bus_df = colfillnan(bus_df, 'owner')
    bus_df = colfillnan(bus_df, 'area')

    def colfillnan2(_df, col, col2):
        """
        fillnan 列;
        col2: 默认填充列至col
        """
        _df[col] = _df[col].fillna('') if col in _df.columns else _df[col2]
        return _df

    bus_df = colfillnan2(bus_df, 'zone_name', 'zone')
    bus_df = colfillnan2(bus_df, 'owner_name', 'owner')
    bus_df = colfillnan2(bus_df, 'area_name', 'area')

    # 获取分区展示名列表dict
    zone_disp_name = dict()
    for i_b, row in bus_df.iterrows():
        if row['zone'] != '':
            zone_disp_name[row['zone']] = row['zone_name']
        if row['owner'] != '':
            zone_disp_name[row['owner']] = row['owner_name']
        if row['area'] != '':
            zone_disp_name[row['area']] = row['area_name']

    # 先处理zone，提取最小供电分区;
    zone_ = bus_df['zone'].unique().tolist()
    for i_z in zone_:
        if i_z == '':
            continue
        else:
            zn_name = zone_disp_name[i_z]
            zone_dict[zn_name] = [i_z]

    # 处理'onwer'或'area'
    def get_area_zones(col):
        if col in bus_df.columns:
            owners_ = bus_df[col].unique().tolist()
            for i_ow in owners_:
                if i_ow == '':
                    continue

                # 查找每个area关联的最小供电分区(zone)
                bus_owner = bus_df[bus_df[col] == i_ow]
                i_zones_list = bus_owner['zone_name'].unique().tolist()
                i_zones_list = [item for item in i_zones_list if item != '']  # 去除''值
                i_ow_name = zone_disp_name[i_ow]
                zone_dict[i_ow_name] = i_zones_list

    # get_area_zones('owner')  # 处理owner
    get_area_zones('area')  # 处理area

    return zone_dict


def GetNetZoneRelayDeviesInfo(net: pandapowerNet, config: dict, area_zone_map: dict):
    """
    获取所有分区列表及分区关联的所有设备索引;
    Args:
        net:
        config：配置参数
        area_zone_map: 分区关联母线索引;
    Returns:
    """
    # 'line','dcline'-单独考虑
    dev_list = ['bus', 'gen', 'wind', 'solar', 'hydropower', 'feedin', 'stogen', 'trafo', 'load']
    zone_device_dict = dict()
    for i_zn, zn_val in area_zone_map.items():  # area_name: bus_idx_list
        zone_device_dict[i_zn] = dict()
        for dev in dev_list:
            ori_df = net[dev]
            if len(ori_df) == 0:
                zone_device_dict[i_zn][dev] = list()
                continue
            if dev == 'bus':
                zone_device_dict[i_zn][dev] = zn_val
            elif dev == 'line':
                zone_device_dict[i_zn][dev] = ori_df[ori_df['from_bus'].isin(zn_val) & ori_df['to_bus'].isin(
                    zn_val)].index.to_list()  # 线路--两端都在分区内的才算;
            elif dev == 'trafo':
                zone_device_dict[i_zn][dev] = ori_df[ori_df['hv_bus'].isin(zn_val) & ori_df['lv_bus'].isin(
                    zn_val)].index.to_list()
            elif dev == 'load':
                # 江苏特殊处理;去除部分可能是风电/光伏混入的load;
                if config['case_address'] == 'jiangsu':
                    area_load_ori = ori_df.loc[ori_df.bus.isin(zn_val), :]
                    if 'zone' not in area_load_ori.columns:
                        zone_device_dict[i_zn][dev] = area_load_ori.loc[(~area_load_ori.name.str.contains('XF')) &
                                                                        (~area_load_ori.name.str.contains('XG')) &
                                                                        (~area_load_ori.merge(net.bus, left_on='bus',
                                                                                              right_index=True).zone.isin(
                                                                            ['Jc', 'Jf', 'Jg']))].index.to_list()
                    else:
                        zone_device_dict[i_zn][dev] = area_load_ori.loc[(~area_load_ori.name.str.contains('XF')) &
                                                                        (~area_load_ori.name.str.contains('XG')) &
                                                                        (~area_load_ori.zone.isin(
                                                                            ['Jc', 'Jf', 'Jg']))].index.to_list()
                else:
                    zone_device_dict[i_zn][dev] = ori_df[ori_df['bus'].isin(zn_val)].index.to_list()
            else:
                zone_device_dict[i_zn][dev] = ori_df[ori_df['bus'].isin(zn_val)].index.to_list()

    # 考虑分区线路集合:1)连接在分区两个所属节点的线路;2)
    # 2、(追加)连接在分区关联场站(bus,dispname)&from_bus--name/to_bus--name的线路;"line"
    line_copy = net.line.copy()
    line_copy['from_bus_dispname'] = line_copy['from_bus'].map(net.bus['dispname'].to_dict())
    line_copy['to_bus_dispname'] = line_copy['to_bus'].map(net.bus['dispname'].to_dict())
    for i_zn, zn_val in area_zone_map.items():
        # 分区关联的bus对应你的dispname--去nan,""
        zn_i_bus_dispname_list = net.bus[net.bus.index.isin(zn_val)]['dispname'].unique().tolist()
        zn_i_bus_dispname_list = [item for item in zn_i_bus_dispname_list if item != '' and not pd.isna(item)]
        # dispbus_index = net.bus.loc[net.bus.dispname.isin(zn_i_bus_dispname_list)].index  # 分区关联母线的厂站(dispname)包含的所有母线;
        # net_line的 'from_bus'\'to_bus'对应的bus['dispname']都在zn_i_bus_dispname_list里，且两个不相等
        cond1 = line_copy['from_bus_dispname'].isin(zn_i_bus_dispname_list) & line_copy['to_bus_dispname'].isin(
            zn_i_bus_dispname_list) & (line_copy['from_bus_dispname'] != line_copy['to_bus_dispname'])
        cond2 = line_copy['from_bus'].isin(zn_val) & line_copy['to_bus'].isin(zn_val)
        zone_device_dict[i_zn]['line'] = line_copy[cond1 | cond2].index.to_list()  # 线路--两端都在分区内的才算;

    # 处理直流;
    if "dcline" in net.keys():
        dcline_copy = net.dcline.copy()
        dcline_copy['from_bus_dispname'] = dcline_copy['from_bus'].map(net.bus['dispname'].to_dict())
        dcline_copy['to_bus_dispname'] = dcline_copy['to_bus'].map(net.bus['dispname'].to_dict())
        for i_zn, zn_val in area_zone_map.items():
            # 分区关联的bus对应你的dispname--去nan,""
            zn_i_bus_dispname_list = net.bus[net.bus.index.isin(zn_val)]['dispname'].unique().tolist()
            zn_i_bus_dispname_list = [item for item in zn_i_bus_dispname_list if item != '' and not pd.isna(item)]
            cond1 = dcline_copy['from_bus_dispname'].isin(zn_i_bus_dispname_list) | dcline_copy['to_bus_dispname'].isin(
                zn_i_bus_dispname_list) & (dcline_copy['from_bus_dispname'] != dcline_copy['to_bus_dispname'])
            cond2 = dcline_copy['from_bus'].isin(zn_val) | dcline_copy['to_bus'].isin(zn_val)
            zone_device_dict[i_zn]['dcline'] = dcline_copy[cond1 | cond2].index.to_list()

    # 3、生成分区与分区映射;
    all_zone_dict = list(area_zone_map.keys())
    all_zone_dict_new = dict()
    for i_z in all_zone_dict:
        all_zone_dict_new[i_z] = [i_z]

        # all_zone_dict = GetZoneList(net.bus, config)  # 获取所有分区列表
        # zone_device_dict = dict()
        # for i_zn, zn_val in all_zone_dict.items():
        #     zone_device_dict[i_zn] = dict()
        #     for dev in dev_list:
        #         ori_df = net[dev]
        #         if len(ori_df) == 0:
        #             zone_device_dict[i_zn][dev] = list()
        #             continue
        #         if dev == 'line':
        #             zone_device_dict[i_zn][dev] = ori_df[ori_df['from_zone'].isin(zn_val) & ori_df['to_zone'].isin(
        #                 zn_val)].index.to_list()  # 线路需要进一步考虑--处理--处理--两端都在分区内的才算;
        #         else:
        #             zone_device_dict[i_zn][dev] = ori_df[ori_df['zone'].isin(zn_val)].index.to_list()

        # 增加全网:
        # zone_device_dict['全省'] = dict()
        # for dev in dev_list:
        #     ori_df = net[dev]
        #     zone_device_dict['全省'][dev] = ori_df.index.to_list()

    return all_zone_dict_new, zone_device_dict
