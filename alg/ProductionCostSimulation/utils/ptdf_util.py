import numpy as np
import pandas as pd
import pandapower as pp
import json


def interface_rate(rate, line_list):
    """计算断面负载率"""
    n_line = len(line_list)
    _num = 0.0000001
    _sum = np.array([0.00000])
    for i in range(n_line):
        if rate[line_list[i]] == 0:
            pass
        else:
            _num += 1
            _sum += rate[line_list[i]]
    return _sum / _num


def res_load_max_rate(branch_power, _res):
    slices = [np.NINF, 0.3, 0.5, 0.8, 1, np.inf]
    rate_mean_count = [np.count_nonzero((branch_power > slices[ind]) & (branch_power <= slices[ind + 1])) for ind in
                       range(5)]
    _res["mean_load_count"] = rate_mean_count
    return


def get_Pbr_max(net, line_idx, trafo_idx):
    # new_line = net.line.merge(net.bus.loc[:, 'vn_kv'], left_on='from_bus', right_index=True)
    # Pbr_max_line = np.sqrt(3) * new_line.loc[line_idx, 'max_i_ka'].values * new_line.loc[line_idx, 'vn_kv'].values
    # Pbr_max_trafo = net.trafo.loc[trafo_idx, 'sn_mva'].values
    Pbr_max_line = net.line.loc[line_idx, 'stable_limit_mw'].values
    Pbr_max_trafo = net.trafo.loc[trafo_idx, 'stable_limit_mw'].values
    Pbr_max_line[np.isnan(Pbr_max_line)] = 10000  # 999999
    Pbr_max_line[Pbr_max_line == 0] = 10000  # 999999
    Pbr_max_trafo[np.isnan(Pbr_max_trafo)] = 10000  # 999999
    Pbr_max_trafo[Pbr_max_trafo == 0] = 10000  # 999999
    return np.append(Pbr_max_line, Pbr_max_trafo)[:, np.newaxis]


def filter_branch_idx(branch_data, voltageLevel1, voltageLevel2, _type):
    if _type == 'trafo':
        filtered_idx = branch_data[
            (~ branch_data['name'].str.contains('抽|燃|厂|峡|阳城|中星|环保'))
            & (branch_data['vn_lv_kv'].values == 1)
            & (branch_data['vn_hv_kv'].values > 200)
            & (branch_data['vn_hv_kv'] >= voltageLevel1) & (branch_data['vn_hv_kv'] <= voltageLevel2)
            ].index
    else:
        filtered_idx = branch_data[
            (~ branch_data['name'].str.contains('抽|燃|厂|峡|阳城|中星|环保'))
            & (branch_data['vn_kv'] >= voltageLevel1) & (branch_data['vn_kv'] <= voltageLevel2)
            ].index
    return filtered_idx


def add_reactance(net, add_reactance_arg):
    chosen_line = add_reactance_arg['line_index']
    x_bus_pair = net.line.iloc[chosen_line][['from_bus', 'to_bus']]

    target_lines_bool = (
                            net.line['from_bus'].apply(lambda x: any(x == p for p in x_bus_pair))
                        ) & (
                            net.line['to_bus'].apply(lambda x: any(x == p for p in x_bus_pair))
                        )
    target_lines = net.line.loc[target_lines_bool].index
    for ind in target_lines:
        total_ohm = net.line.iloc[ind].loc['length_km'] * net.line.iloc[ind].loc['x_ohm_per_km']
        total_ohm += add_reactance_arg['added_reactance_ohm']
        ohm_per_km = total_ohm / net.line.iloc[ind].loc['length_km']
        net.line.loc[ind, 'x_ohm_per_km'] = ohm_per_km

    return net


def add_phase_shifter(net, phase_shifter_arg):
    # 确定bus_a
    ps_line = phase_shifter_arg['line_index']
    dispname_buses = net.bus[net.bus['dispname'] == phase_shifter_arg['point_name']].index
    ps_bus_pair = net.line.iloc[ps_line][['from_bus', 'to_bus']]
    ps_bus_a = [bus for bus in ps_bus_pair if bus in dispname_buses][0]
    ps_bus_b = [x for x in ps_bus_pair if x != ps_bus_a][0]

    # bus表中添加新母线 A'
    new_bus = net.bus.iloc[ps_bus_a]
    new_bus['name'] += 'PhaseShifter'
    new_bus['dispname'] += 'PhaseShifter'
    net.bus = pd.concat([net.bus, new_bus.to_frame().T], ignore_index=True, axis=0)
    ps_bus_a_prime = net.bus.index[-1]

    # traofo表中添加移相器 A ~ A'
    ps_device = net.trafo.iloc[0]
    ps_device['name'] = 'New_Phase_Shifter'
    ps_device['tap_phase_shifter'] = True
    ps_device['shift_degree'] = phase_shifter_arg['shift_degree']
    ps_device['hv_bus'] = ps_bus_a
    ps_device['lv_bus'] = ps_bus_a_prime
    ps_device['vn_hv_kv'] = net.bus.loc[ps_bus_a, 'vn_kv']
    ps_device['vn_lv_kv'] = net.bus.loc[ps_bus_a_prime, 'vn_kv']
    # 短路电压百分值
    ps_device['vk_percent'] = 0.1
    ps_device['vkr_percent'] = 0.06

    net.trafo = pd.concat([net.trafo, ps_device.to_frame().T], ignore_index=True, axis=0)
    net.trafo['stable_limit_mw'] = net.trafo['stable_limit_mw'].astype(float)

    # line表中 A ~ B 线路都改为 A' ~ B，有可能多于一条
    search_se_1 = net.line.loc[net.line['from_bus'] == ps_bus_a, 'to_bus'] == ps_bus_b
    search_se_2 = net.line.loc[net.line['from_bus'] == ps_bus_b, 'to_bus'] == ps_bus_a

    search_index_1 = search_se_1[search_se_1 == True].index.tolist()
    search_index_2 = search_se_2[search_se_2 == True].index.tolist()
    ab_lines = search_index_1 + search_index_2

    for ab_line in ab_lines:
        if net.line.loc[ab_line, 'from_bus'] == ps_bus_a:
            net.line.loc[ab_line, 'from_bus'] = ps_bus_a_prime
        elif net.line.loc[ab_line, 'to_bus'] == ps_bus_a:
            net.line.loc[ab_line, 'to_bus'] = ps_bus_a_prime
    return net


def prepare_net_with_new_device(net_init, res, timestep, phase_shifter_arg={}, add_reactance_arg={}):
    """
    准备带理想移相器或者加装串抗的pp net
    """

    # 先添加串抗，以防移相器引起线路编号变化
    if add_reactance_arg != {}:
        net_init = add_reactance(net_init, add_reactance_arg)

    ## 移相器参数添加
    if phase_shifter_arg != {}:
        net_init = add_phase_shifter(net_init, phase_shifter_arg)

    ## 把wind,solar,feedin,stogen全都等值为负荷
    neg_load = np.vstack((res['wind_output'],
                          res['solar_output'],
                          res['feedin_output'],
                          res['stogen_output']))
    load_p_mw = np.vstack((res['load'], -1 * neg_load))[:, timestep]

    neg_load_bus = np.concatenate((net_init.wind.bus.values,
                                   net_init.solar.bus.values,
                                   net_init.feedin.bus.values,
                                   net_init.stogen.bus.values))
    load_bus = np.concatenate((net_init.load.bus.values, neg_load_bus))

    neg_load_name = np.concatenate((net_init.wind.name.values,
                                    net_init.solar.name.values,
                                    net_init.feedin.name.values,
                                    net_init.stogen.name.values))
    load_name = np.concatenate((net_init.load.name.values, neg_load_name))

    ## 准备pp net
    net = pp.create_empty_network()

    pp.create_buses(net, len(net_init.bus), net_init.bus.vn_kv.values,
                    name=net_init.bus.name.values,
                    zone=net_init.bus.zone.values,
                    max_vm_pu=np.array([3.] * len(net_init.bus)),
                    min_vm_pu=np.array([0.] * len(net_init.bus)))

    # wind,solar,feedin,stogen全都等值为负荷
    pp.create_loads(net, load_bus,
                    p_mw=load_p_mw,
                    name=load_name,
                    q_mvar=np.array(len(load_p_mw) * [0.]))

    pp.create_gens(net, net_init.gen.bus.values,
                   p_mw=res['gen_output'][:, timestep],
                   vm_pu=np.array([1.] * len(net_init.gen)),
                   max_q_mvar=np.array([0.] * len(net_init.gen)),
                   min_q_mvar=np.array([0.] * len(net_init.gen)),
                   max_p_mw=net_init.gen.max_p_mw.values,
                   min_p_mw=net_init.gen.min_p_mw.values,
                   vn_kv=np.array([net.bus.loc[net.bus.index == bus_index, 'vn_kv'].values[0] for bus_index in
                                   list(net_init.gen.bus.values)]),
                   in_service=net_init.gen.in_service.values,
                   name=net_init.gen.name.values,
                   slack=np.array([False] * len(net_init.gen)))

    pp.create_lines_from_parameters(net,
                                    from_buses=net_init.line.from_bus.values,
                                    to_buses=net_init.line.to_bus.values,
                                    length_km=net_init.line.length_km.values,
                                    r_ohm_per_km=net_init.line.r_ohm_per_km.values,
                                    x_ohm_per_km=net_init.line.x_ohm_per_km.values,
                                    c_nf_per_km=net_init.line.c_nf_per_km.values,
                                    max_i_ka=net_init.line.max_i_ka.values,
                                    g_us_per_km=net_init.line.g_us_per_km.values,
                                    name=net_init.line.name.values,
                                    max_loading_percent=net_init.line.max_loading_percent.values)

    net_init.trafo.shift_degree.fillna(0, inplace=True)
    net_init.trafo.tap_phase_shifter.fillna(False, inplace=True)
    pp.create_transformers_from_parameters(net,
                                           hv_buses=net_init.trafo.hv_bus.values,
                                           lv_buses=net_init.trafo.lv_bus.values,
                                           sn_mva=net_init.trafo.sn_mva.values,
                                           vn_hv_kv=net_init.trafo.vn_hv_kv.values,
                                           vn_lv_kv=net_init.trafo.vn_lv_kv.values,
                                           vkr_percent=net_init.trafo.vkr_percent.values,
                                           vk_percent=net_init.trafo.vk_percent.values,
                                           pfe_kw=net_init.trafo.pfe_kw.values,
                                           i0_percent=net_init.trafo.i0_percent.values,
                                           shift_degree=net_init.trafo.shift_degree.values,
                                           tap_phase_shifter=net_init.trafo.tap_phase_shifter.values,
                                           tap_side=net_init.trafo.tap_side.values,
                                           tap_pos=net_init.trafo.tap_pos.values,
                                           tap_neutral=net_init.trafo.tap_neutral.values,
                                           tap_max=net_init.trafo.tap_max.values,
                                           tap_min=net_init.trafo.tap_min.values,
                                           tap_step_percent=net_init.trafo.tap_step_percent.values,
                                           name=net_init.trafo.name.values,
                                           max_loading_percent=net_init.trafo.max_loading_percent.values)

    # coal_above_500kV_index = net_init.gen.loc[net_init.gen['500kV_coal'] == True].index.tolist()

    # 第一个机组为slack才会算出与teap结果相同的潮流
    net.gen.loc[0, 'slack'] = True
    net.gen.loc[0, 'in_service'] = True

    return net


def cal_branch_rate(net, config, branch_power, Pbr_max):
    """
    计算设备负载率推演的饼图和柱图
    Args:
        net:
        config:
        branch_power:
        Pbr_max:

    Returns:
    """
    rate = np.absolute(branch_power) / Pbr_max
    res_line = dict()
    res_trafo = dict()

    n_line_output = len(net.line.index)

    line_power = rate[0:n_line_output, :]
    trafo_power = rate[n_line_output:, :]

    # 筛选目标需要的电压等级以上的设备
    select_vn_kv = config['device_dis_vn_kv'] if 'device_dis_vn_kv' in config else 500

    net.line["line_index"] = net.line.index
    bus_kv = net.bus.loc[:, ['vn_kv']]
    bus_kv["bus_index"] = bus_kv.index
    line_kv = net.line
    line_500_kv_index = filter_branch_idx(line_kv, select_vn_kv, 600, 'line')
    net.trafo["trafo_index"] = net.trafo.index
    trafo_500_kv_index = filter_branch_idx(net.trafo, select_vn_kv, 600, 'trafo')

    ret_inter = dict()
    inters = net.line.loc[:, ['interface_direction', 'interface_name', 'interface_collection_name']].dropna()
    inters_collections = inters['interface_collection_name'].unique()
    for coll in inters_collections:
        ret_inter[coll] = dict()
        inter_name_all = inters[inters['interface_collection_name'] == coll].loc[:, 'interface_name']
        inter_name_unique = inter_name_all.unique()
        for inter_name in inter_name_unique:
            inter_indices = inter_name_all.loc[inter_name_all == inter_name].index.tolist()
            ret_inter[coll][inter_name] = list(interface_rate(rate, inter_indices))

    for coll in inters_collections:
        inter_direction = inters[inters['interface_collection_name'] == coll].loc[:, 'interface_direction']
        inter_name_all = inters[inters['interface_collection_name'] == coll].loc[:, 'interface_name']
        inter_power_sum = (inter_direction.values.reshape(-1, 1) * branch_power[inter_name_all.index, :]).sum() * 0.1
        # print(inter_power_sum)
        inter_rate_max = max(ret_inter[coll].values())[0]
        # print(inter_rate_max)
        ret_inter[coll]['输送能力'] = inter_power_sum / inter_rate_max

    ### 苏州南部供电能力计算

    coll = '苏南受电断面'
    new_coll = '新苏州南部'
    ret_inter[new_coll] = {}

    # 苏南受电断面包含线路的功率总和 除以 max(原包含线路限额率, 500kV石牌K-玉山线限额率, 1000kV东吴主变限额率) + 一些机组装机 + 一些直流功率
    # 其中1000kV东吴主变限额率 = （东吴-全福双线功率 + 东吴-笠泽双线功率）/ 4400MW
    # 4400MW 为 1000kV东吴主变限额

    # 先算出石牌K-玉山，1000kV东吴主变限额率
    shipai_yushan = ['500kV石牌K-玉山线', ]
    dongwu_lines = ['500kV东吴2-吴南501线', '500kV东吴2-吴南502线',  # 吴南 就是 笠泽
                    '500kV东吴2-全福1线', '500kV东吴2-全福2线', ]
    shipai_yushan_limit = 1400  # MW
    dongwu_limit = 4400  # MW 1000kV东吴主变限额

    shipai_yushan_ind = net.line[net.line['name'].isin(shipai_yushan)].index
    dongwu_lines_ind = net.line[net.line['name'].isin(dongwu_lines)].index

    # 往玉山为正，所以系数为1
    shipai_yushan_rate = (1. * branch_power[shipai_yushan_ind, :]).sum() / shipai_yushan_limit

    # 他们的潮流方向全为正，所以系数全为1，没有反方向（-1）
    dongwu_lines_rate = (1. * branch_power[dongwu_lines_ind, :]).sum() / dongwu_limit

    # 原断面包含的线路的限额率
    inter_name_all = inters[inters['interface_collection_name'] == coll].loc[:, 'interface_name']

    inter_name_unique = inter_name_all.unique()
    for inter_name in inter_name_unique:
        inter_indices = inter_name_all.loc[inter_name_all == inter_name].index.tolist()
        ret_inter[new_coll][inter_name] = list(interface_rate(rate, inter_indices))

    # 500kV石牌K-玉山线限额率, 1000kV东吴主变限额率
    ret_inter[new_coll]['石牌_K-玉山'] = [shipai_yushan_rate]
    ret_inter[new_coll]['1000kV东吴主变'] = [dongwu_lines_rate]

    # 功率总和只包含 原苏南受电断面线路的功率
    inter_direction = inters[inters['interface_collection_name'] == coll].loc[:, 'interface_direction']
    inter_power_sum = (inter_direction.values.reshape(-1, 1) * branch_power[inter_name_all.index, :]).sum() * 0.1  # 万千瓦

    ## 变量 ret_inter[new_coll].values()如下：
    # dict_values([[0.5], [0.4], [0.5], [0.3], [0.3], [0.64]])
    inter_rate_max = max(ret_inter[new_coll].values())[0]

    ## 功率总和只包含 原苏南受电断面线路的功率，
    ## 限额率还有500kV石牌K-玉山线, 1000kV东吴主变
    ret_inter[new_coll]['输送能力（含装机但缺直流）'] = inter_power_sum / inter_rate_max  # 万千瓦

    ## 要加的机组装机为744，详情如下：
    # 7、500kV常熟二厂-195万
    # 8、500kV华苏电厂-120万
    # 9、苏州西-163万、车坊-83万、玉山-90万、石牌-0万、吴江东-93万、吴江西-0万
    ret_inter[new_coll]['输送能力（含装机但缺直流）'] += 744

    ### 结束 苏州南部供电能力计算

    res_line['关键断面额利用率'] = ret_inter

    ret_dict = dict()

    res_load_max_rate(line_power[line_500_kv_index], res_line)
    res_load_max_rate(trafo_power[trafo_500_kv_index], res_trafo)

    ret_dict['line'] = res_line
    ret_dict['trafo'] = res_trafo
    ret_dict['num_lines_total'] = n_line_output

    # 计算线路和主变负载率排序，各自8条
    line_kv['rate'] = line_power
    net.trafo['rate'] = trafo_power

    line_kv_500_rate = line_kv.loc[line_500_kv_index, ['name', 'rate']]
    trafo_500_rate = net.trafo.loc[trafo_500_kv_index, ['name', 'rate']]
    line_rate_sort = line_kv_500_rate.sort_values(by='rate', inplace=False, ascending=False)
    trafo_rate_sort = trafo_500_rate.sort_values(by='rate', inplace=False, ascending=False)

    output_line_list = [
        num for num in range(ret_dict['line']['mean_load_count'][3] + ret_dict['line']['mean_load_count'][4])
    ]
    output_trafo_list = [
        num for num in range(ret_dict['trafo']['mean_load_count'][3] + ret_dict['trafo']['mean_load_count'][4])
    ]
    ret_dict['line_rate_show'] = line_rate_sort.iloc[output_line_list, :].values.tolist()
    ret_dict['trafo_rate_show'] = trafo_rate_sort.iloc[output_trafo_list, :].values.tolist()

    ## 返回重载设备清单 主变和线路
    Pbr_max = get_Pbr_max(net, net.line.index, net.trafo.index)

    types = ['trafo', 'line']
    d = dict()
    for _type in types:
        if _type == 'line':
            line_kv_500_above = filter_branch_idx(line_kv, 500, 2000, 'line')
            df = pd.DataFrame({
                '设备名称': net.line.loc[line_kv_500_above, 'name'].values,
                '潮流功率': np.abs(branch_power[line_kv_500_above, 0]) / 10,
                '运行限额': Pbr_max[line_kv_500_above, 0] / 10,
                '限额利用率': np.abs(rate[line_kv_500_above, 0]),
            })

        elif _type == 'trafo':
            trafo_kv_500_above = filter_branch_idx(net.trafo, 500, 2000, 'trafo')
            df = pd.DataFrame({
                '设备名称': net.trafo.loc[trafo_kv_500_above, 'name'].values,
                '潮流功率': np.abs(branch_power[n_line_output + trafo_kv_500_above, 0]) / 10,
                '运行限额': Pbr_max[n_line_output + trafo_kv_500_above, 0] / 10,
                '限额利用率': np.abs(rate[n_line_output + trafo_kv_500_above, 0]),
            })
        df = df.sort_values(by=['限额利用率'], ascending=False)
        df = df[df['限额利用率'] >= 0.8]
        d[_type] = df_to_json(df, with_index=False)
    ret_dict['重载设备详细信息'] = d
    return ret_dict


def get_inf_unit_idxes(net):
    """
    获取断面的设备组成索引--线路/主变;
    """
    inf_dict = dict()  # 断面dict
    line_inters = net.line.loc[:, ['interface_direction', 'interface_name', 'interface_collection_name']].dropna()
    trafo_inters = net.trafo.loc[:, ['interface_direction', 'interface_name', 'interface_collection_name']].dropna()
    line_inters_unique = list(line_inters['interface_collection_name'].unique())
    trafo_inters_unique = list(trafo_inters['interface_collection_name'].unique())
    inters_collections = line_inters_unique + trafo_inters_unique
    for coll in inters_collections:
        inf_dict[coll] = dict()
        # line
        inf_dict[coll]['line'] = line_inters[line_inters['interface_collection_name'] == coll].index.tolist()
        # trafo
        inf_dict[coll]['trafo'] = trafo_inters[trafo_inters['interface_collection_name'] == coll].index.tolist()
    return inf_dict


def cal_branch_rate_simple(net, config, branch_power, Pbr_max, area_name=None, area_details: dict = dict()):
    """
    计算设备负载率及负载率变化率;
    Args:
        net:
        config:
        branch_power:
        Pbr_max:
        area_name: 分区名
        area_details: 分区设备索引
    Returns:
    """
    rate = np.absolute(branch_power) / Pbr_max

    n_line_output = len(net.line.index)

    # 筛选目标需要的电压等级以上的设备
    select_vn_kv = config['device_dis_vn_kv'] if 'device_dis_vn_kv' in config else 500

    net.line["line_index"] = net.line.index
    line_kv = net.line
    net.trafo["trafo_index"] = net.trafo.index

    ## 进一步考虑通道断面(由一级小断面组成的通道,例如江苏过江通道)
    # 将net.interface的'name':'max_p_mw'形成dict
    if not net.interface.empty:
        inf_limit_map = {name: val for name, val in zip(net.interface['name'], net.interface['max_p_mw'])}
    else:
        inf_limit_map = dict()

    ## 关键断面限额利用率 功率=支路有功和/inf表中的'max_p_mw'
    ## 关键断面限额利用率--断面有暂时考虑: 线路 + 主变两种设备;
    ret_inter = dict()  # 断面dict
    # 先从line和trafo中筛选出有效断面名称
    line_inters = net.line.loc[:, ['interface_direction', 'interface_name', 'interface_collection_name']].dropna()
    trafo_inters = net.trafo.loc[:, ['interface_direction', 'interface_name', 'interface_collection_name']].dropna()
    line_inters_unique = list(line_inters['interface_collection_name'].unique())
    trafo_inters_unique = list(trafo_inters['interface_collection_name'].unique())
    inters_collections = line_inters_unique + trafo_inters_unique
    for coll in inters_collections:
        ret_inter[coll] = dict()
        # line
        line_inter_name_all = line_inters[line_inters['interface_collection_name'] == coll].loc[:, 'interface_name']
        # if line_inter_name_all 数目不为0:
        if len(line_inter_name_all) > 0:
            line_inter_name_unique = line_inter_name_all.unique()
            for inter_name in line_inter_name_unique:
                line_inter_indices = line_inter_name_all.loc[line_inter_name_all == inter_name].index.tolist()
                ret_inter[coll][inter_name] = list(interface_rate(rate, line_inter_indices))
        # trafo
        trafo_inter_name_all = trafo_inters[trafo_inters['interface_collection_name'] == coll].loc[:, 'interface_name']
        if len(trafo_inter_name_all) > 0:
            trafo_inter_name_unique = trafo_inter_name_all.unique()
            for trafo_inter_name in trafo_inter_name_unique:
                trafo_inter_indices = trafo_inter_name_all.loc[trafo_inter_name_all == trafo_inter_name].index.tolist()
                ret_inter[coll][trafo_inter_name] = list(interface_rate(rate, trafo_inter_indices))

    inf_num = len(inters_collections)  # 断面数目
    inf_rate = [0] * inf_num
    inf_power = [0] * inf_num
    inf_limit = [0] * inf_num
    i_no = 0
    for coll in inters_collections:
        # line
        inter_power_sum = 0.0
        line_inter_direction = line_inters[line_inters['interface_collection_name'] == coll].loc[:,
                               'interface_direction']
        line_inter_name_all = line_inters[line_inters['interface_collection_name'] == coll].loc[:, 'interface_name']
        if len(line_inter_name_all) > 0:
            inter_power_sum += (line_inter_direction.values.reshape(-1, 1) * branch_power[line_inter_name_all.index,
                                                                             :]).sum() * 0.1
        # trafo
        trafo_inter_direction = trafo_inters[trafo_inters['interface_collection_name'] == coll].loc[:,
                                'interface_direction']
        trafo_inter_name_all = trafo_inters[trafo_inters['interface_collection_name'] == coll].loc[:, 'interface_name']
        if len(trafo_inter_name_all) > 0:
            inter_power_sum += (trafo_inter_direction.values.reshape(-1, 1) * branch_power[trafo_inter_name_all.index,
                                                                              :]).sum() * 0.1
        if coll in inf_limit_map:
            inf_maxp_mw = inf_limit_map[coll]  # 原始限额
        else:
            inf_maxp_mw = 2000

        inter_rate_max = max(0.00001, abs(max(ret_inter[coll].values())[0]))
        if abs(inter_power_sum) < 1:
            inf_ability = inf_maxp_mw
        else:
            inf_ability = min(abs(inter_power_sum) / inter_rate_max, inf_maxp_mw)

        ret_inter[coll]['输送能力'] = inf_ability

        inf_power[i_no] = round(inter_power_sum, 2)
        inf_limit[i_no] = round(inf_ability, 1)
        inf_rate[i_no] = round(inter_rate_max, 4)
        i_no += 1

    ret_inf = dict()
    ret_inf['name'] = list(inters_collections)
    ret_inf['power'] = inf_power
    ret_inf['p_rate'] = inf_rate
    ret_inf['limit'] = inf_limit
    # ret_inf['rate_change'] = [0] * inf_num     #负载率变化率

    ret_dict = dict()
    ret_dict['interface'] = ret_inf

    ## 返回重载设备清单 主变和线路
    types = ['trafo', 'line']
    for _type in types:
        if _type == 'line':
            line_kv_selected = filter_branch_idx(line_kv, select_vn_kv, 2000, 'line')
            if area_name is not None and area_name in area_details.keys():
                area_line_idx = area_details[area_name]['line']  # 筛选属于分区的设备
                mask = np.isin(line_kv_selected, np.array(area_line_idx))  # 找出array1中在array2中的元素
                line_kv_selected = line_kv_selected[mask]
            else:
                pass
            df = pd.DataFrame({
                "index": line_kv_selected,
                "name": net.line.loc[line_kv_selected, 'name'].values,  # '设备名称'
                "power": np.abs(branch_power[line_kv_selected, 0] * 0.1).round(2),  # '潮流功率'
                "limit": (Pbr_max[line_kv_selected, 0] * 0.1).round(1),  # '运行限额'
                "p_rate": np.abs(rate[line_kv_selected, 0]).round(4),  # '限额利用率',
            })
        elif _type == 'trafo':
            trafo_kv_selected = filter_branch_idx(net.trafo, select_vn_kv, 2000, 'trafo')
            if (area_name is not None and area_name != '全省') and area_name in area_details:
                area_trafo_idx = area_details[area_name]['trafo']  # 筛选属于分区的设备
                mask = np.isin(trafo_kv_selected, np.array(area_trafo_idx))  # 找出array1中在array2中的元素
                trafo_kv_selected = trafo_kv_selected[mask]
            else:
                pass
            df = pd.DataFrame({
                'index': trafo_kv_selected,
                'name': net.trafo.loc[trafo_kv_selected, 'name'].values,
                'power': np.abs(branch_power[n_line_output + trafo_kv_selected, 0] * 0.1).round(2),
                'limit': (Pbr_max[n_line_output + trafo_kv_selected, 0] * 0.1).round(1),
                'p_rate': np.abs(rate[n_line_output + trafo_kv_selected, 0]).round(4),
            })
        else:
            raise ValueError(f"{_type} not supported!")
        ret_dict[_type] = df.to_dict(orient='list')
    return ret_dict


def cal_branch_power_add(output_add, output_init, device_p_max, _ptdf):
    _sum = output_init.sum()
    # 防止出现除数为0的情况, 如果和为0，按照装机容量进行分配
    if abs(_sum) < 0.01:
        device_output_add = output_add * (device_p_max / device_p_max.sum())
    else:
        device_output_add = output_add * (output_init / _sum)
    device_branch_power_add = _ptdf @ device_output_add
    # print(device_branch_power_add)
    # print(np.shape(device_branch_power_add))
    return device_branch_power_add


def df_to_json(df, with_index=False):
    json_str = df.to_json(orient="split", force_ascii=False)
    dict_like = json.loads(json_str)
    if isinstance(df, (pd.Series)):
        with_index = True
    if with_index:
        res = dict_like
    else:
        new_dict_like = dict()
        for key, value in dict_like.items():
            if key in ['data', 'columns']:
                new_dict_like[key] = value
        res = new_dict_like

    return res
