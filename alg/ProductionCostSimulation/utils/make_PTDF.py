import pandapower as pp
import pandapower.converter as pc
from pandapower.pypower.makePTDF import makePTDF
from pandapower.pypower.makeLODF import makeLODF
import numpy as np
import pandas as pd


def make_PTDF_dict(net_init, select_idx=[], config: dict = dict(), distributed_slack=False):
    net = pp.create_empty_network()

    pp.create_buses(net, len(net_init.bus), net_init.bus.vn_kv.values, name=net_init.bus.name.values,
                    zone=net_init.bus.zone.values, max_vm_pu=np.array([3.] * len(net_init.bus)),
                    min_vm_pu=np.array([0.] * len(net_init.bus)))

    pp.create_loads(net, net_init.load.bus.values, p_mw=net_init.load.max_p_mw.values,
                    name=net_init.load.name.values, q_mvar=np.array(len(net_init.load) * [0.]))

    pp.create_gens(net, net_init.gen.bus.values, p_mw=net_init.gen.ini_p_mw.values,
                   vm_pu=np.array([1.] * len(net_init.gen)), max_q_mvar=np.array([0.] * len(net_init.gen)),
                   min_q_mvar=np.array([0.] * len(net_init.gen)), max_p_mw=net_init.gen.max_p_mw.values,
                   min_p_mw=net_init.gen.min_p_mw.values,
                   vn_kv=np.array([net.bus.loc[net.bus.index == bus_index, 'vn_kv'].values[0] for bus_index in
                                   list(net_init.gen.bus.values)]),
                   in_service=net_init.gen.in_service.values, name=net_init.gen.name.values,
                   slack=np.array([False] * len(net_init.gen)))

    pp.create_lines_from_parameters(net,
                                    from_buses=net_init.line.from_bus.values,
                                    to_buses=net_init.line.to_bus.values,
                                    length_km=net_init.line.length_km.values,
                                    r_ohm_per_km=net_init.line.r_ohm_per_km.values,
                                    x_ohm_per_km=net_init.line.x_ohm_per_km.values,
                                    c_nf_per_km=net_init.line.c_nf_per_km.values,
                                    max_i_ka=net_init.line.max_i_ka.values,
                                    g_us_per_km=net_init.line.g_us_per_km.values,
                                    name=net_init.line.name.values,
                                    max_loading_percent=net_init.line.max_loading_percent.values)

    pp.create_transformers_from_parameters(net,
                                           hv_buses=net_init.trafo.hv_bus.values,
                                           lv_buses=net_init.trafo.lv_bus.values,
                                           sn_mva=net_init.trafo.sn_mva.values,
                                           vn_hv_kv=net_init.trafo.vn_hv_kv.values,
                                           vn_lv_kv=net_init.trafo.vn_lv_kv.values,
                                           vkr_percent=net_init.trafo.vkr_percent.values,
                                           vk_percent=net_init.trafo.vk_percent.values,
                                           pfe_kw=net_init.trafo.pfe_kw.values,
                                           i0_percent=net_init.trafo.i0_percent.values,
                                           tap_side=net_init.trafo.tap_side.values,
                                           tap_pos=net_init.trafo.tap_pos.values,
                                           tap_neutral=net_init.trafo.tap_neutral.values,
                                           tap_max=net_init.trafo.tap_max.values,
                                           tap_min=net_init.trafo.tap_min.values,
                                           tap_step_percent=net_init.trafo.tap_step_percent.values,
                                           name=net_init.trafo.name.values,
                                           max_loading_percent=net_init.trafo.max_loading_percent.values)

    net.gen.loc[0, 'slack'] = True
    net.gen.loc[0, 'in_service'] = True

    ppc = pc.to_ppc(net, init='flat')

    ptdf_sparse = makePTDF(ppc["baseMVA"], ppc["bus"], ppc["branch"],
                           using_sparse_solver=True)
    ptdf_sparse[np.abs(ptdf_sparse) < 1e-4] = 0

    ptdf_dict = {
        'ptdf': ptdf_sparse[select_idx, :],
        'ptdf_gen': ptdf_sparse[np.ix_(select_idx, net_init.gen.bus.values)],
        'ptdf_load': ptdf_sparse[np.ix_(select_idx, net_init.load.bus.values)],
    }

    if 'feedin' in net_init.keys() and not (net_init.feedin).empty:
        ptdf_dict['ptdf_feedin'] = ptdf_sparse[np.ix_(select_idx, net_init.feedin.bus.values)]

    # if config['n_element']['stogen'] > 0 and config['n_element']['storage'] > 0:
    if ('stogen' in net_init.keys() and not (net_init.stogen).empty) and (
            'storage' in net_init.keys() and not (net_init.storage).empty
    ):
        ptdf_dict['ptdf_stogen'] = ptdf_sparse[np.ix_(select_idx, net_init.stogen.bus.values)]

    # if config['n_element']['solar'] > 0:
    if 'solar' in net_init.keys() and not (net_init.solar).empty:
        ptdf_dict['ptdf_solar'] = ptdf_sparse[np.ix_(select_idx, net_init.solar.bus.values)]

    # if config['n_element']['wind'] > 0:
    if 'wind' in net_init.keys() and not (net_init.wind).empty:
        ptdf_dict['ptdf_wind'] = ptdf_sparse[np.ix_(select_idx, net_init.wind.bus.values)]

    # if config['n_element']['hydropower'] > 0 and config['n_element']['reservoir'] > 0:
    if ('hydropower' in net_init.keys() and not (net_init.hydropower).empty) and (
            'reservoir' in net_init.keys() and not (net_init.reservoir).empty
    ):
        ptdf_dict['ptdf_hydropower'] = ptdf_sparse[np.ix_(select_idx, net_init.hydropower.bus.values)]

    return ptdf_dict


def make_ppc(net_init, select_idx=[], config=None):
    net = pp.create_empty_network()

    pp.create_buses(net, len(net_init.bus), net_init.bus.vn_kv.values, name=net_init.bus.name.values,
                    zone=net_init.bus.zone.values, max_vm_pu=np.array([3.] * len(net_init.bus)),
                    min_vm_pu=np.array([0.] * len(net_init.bus)))

    pp.create_loads(net, net_init.load.bus.values, p_mw=net_init.load.max_p_mw.values,
                    name=net_init.load.name.values, q_mvar=np.array(len(net_init.load) * [0.]))

    pp.create_gens(net, net_init.gen.bus.values, p_mw=net_init.gen.ini_p_mw.values,
                   vm_pu=np.array([1.] * len(net_init.gen)), max_q_mvar=np.array([0.] * len(net_init.gen)),
                   min_q_mvar=np.array([0.] * len(net_init.gen)), max_p_mw=net_init.gen.max_p_mw.values,
                   min_p_mw=net_init.gen.min_p_mw.values,
                   vn_kv=np.array([net.bus.loc[net.bus.index == bus_index, 'vn_kv'].values[0] for bus_index in
                                   list(net_init.gen.bus.values)]),
                   in_service=net_init.gen.in_service.values, name=net_init.gen.name.values,
                   slack=np.array([False] * len(net_init.gen)))

    pp.create_lines_from_parameters(net,
                                    from_buses=net_init.line.from_bus.values,
                                    to_buses=net_init.line.to_bus.values,
                                    length_km=net_init.line.length_km.values,
                                    r_ohm_per_km=net_init.line.r_ohm_per_km.values,
                                    x_ohm_per_km=net_init.line.x_ohm_per_km.values,
                                    c_nf_per_km=net_init.line.c_nf_per_km.values,
                                    max_i_ka=net_init.line.max_i_ka.values,
                                    g_us_per_km=net_init.line.g_us_per_km.values,
                                    name=net_init.line.name.values,
                                    max_loading_percent=net_init.line.max_loading_percent.values)

    pp.create_transformers_from_parameters(net,
                                           hv_buses=net_init.trafo.hv_bus.values,
                                           lv_buses=net_init.trafo.lv_bus.values,
                                           sn_mva=net_init.trafo.sn_mva.values,
                                           vn_hv_kv=net_init.trafo.vn_hv_kv.values,
                                           vn_lv_kv=net_init.trafo.vn_lv_kv.values,
                                           vkr_percent=net_init.trafo.vkr_percent.values,
                                           vk_percent=net_init.trafo.vk_percent.values,
                                           pfe_kw=net_init.trafo.pfe_kw.values,
                                           i0_percent=net_init.trafo.i0_percent.values,
                                           tap_side=net_init.trafo.tap_side.values,
                                           tap_pos=net_init.trafo.tap_pos.values,
                                           tap_neutral=net_init.trafo.tap_neutral.values,
                                           tap_max=net_init.trafo.tap_max.values,
                                           tap_min=net_init.trafo.tap_min.values,
                                           tap_step_percent=net_init.trafo.tap_step_percent.values,
                                           name=net_init.trafo.name.values,
                                           max_loading_percent=net_init.trafo.max_loading_percent.values)

    net.gen.loc[0, 'slack'] = True
    net.gen.loc[0, 'in_service'] = True

    ppc = pc.to_ppc(net, init='flat')

    return ppc


def make_PTDF_slack(net_init, select_idx=[], config=None, distributed_slack=False):
    ppc = make_ppc(net_init, select_idx=select_idx, config=config)

    if distributed_slack:
        # 尽量按照500kV煤机的容量最大的分配平衡机
        slack = get_gen_slack_bus(net_init)
    else:
        slack = None

    ptdf_sparse = makePTDF(ppc["baseMVA"], ppc["bus"], ppc["branch"],
                           using_sparse_solver=True,
                           slack=slack)

    ptdf_sparse[np.abs(ptdf_sparse) < 1e-4] = 0

    return ptdf_sparse[select_idx, :]


def get_slack_gen(net, only_coal: bool = False):
    """
    按照500kV煤机的容量分配平衡机
    :param net:
    :return:
    """
    gen_df = net.gen[net.gen['in_service']]

    if only_coal:
        gen_df = gen_df[gen_df['type'] == 'coal']

    if '500kV_coal' in gen_df.columns:
        slack_gen = gen_df[gen_df['500kV_coal']]
    else:
        slack_gen = gen_df

    if len(slack_gen) == 0:
        # gen_df_coal = gen_df[gen_df['type'] == 'coal']
        gen_df_coal = gen_df
        gen_df_coal["grid_vn_kv"] = net.bus.loc[gen_df_coal['bus'], 'vn_kv']  # 并网主变高压侧电压等级;
        # 根据主变查找煤电的并网电压等级;
        gen_trafo_df = net.trafo[net.trafo['lv_bus'].isin(gen_df_coal['bus'])]
        for g_idx, g_row_i in gen_df_coal.iterrows():
            for t_idx, t_row_i in gen_trafo_df.iterrows():
                if t_row_i['lv_bus'] == g_row_i['bus']:
                    gen_df_coal.loc[g_idx, 'grid_vn_kv'] = net.bus.loc[t_row_i['hv_bus'], 'vn_kv']
                    break
        # 选择500kV及以上的煤机
        slack_gen = gen_df_coal[gen_df_coal['grid_vn_kv'] >= 500 * 0.9]

        if len(slack_gen) == 0:
            # 没有找到500kV及以上的煤机，节点,按容量从大到小选取
            gen_df_sorted = gen_df_coal.sort_values(by="max_p_mw", ascending=False)
            if len(gen_df_coal) > 10:
                coal_bus_above_500 = gen_df_sorted.index.tolist()[:10]
            elif len(gen_df_coal) > 5:
                coal_bus_above_500 = gen_df_sorted.index.tolist()[:5]
            else:
                coal_bus_above_500 = gen_df_sorted.index.tolist()[:1]
            slack_gen = gen_df_coal[coal_bus_above_500]
            if len(slack_gen) == 0:
                slack_gen = gen_df.copy()
    return slack_gen


def get_gen_slack_bus(net):
    # 选择的平衡节点;
    slack_gen = get_slack_gen(net)
    slack_gen_bus = slack_gen['bus'].values.tolist()

    # 选取平衡机的节点索引结束;
    slack_array = pd.Series(data=0, index=net.bus.index).to_frame()

    max_p_mw_500 = net.gen[np.isin(net.gen['bus'], slack_gen_bus)].loc[:, ['bus', 'max_p_mw']].set_index('bus')
    # 去除索引相同的行，只保留最后一行
    max_p_mw_500 = max_p_mw_500.loc[~max_p_mw_500.index.duplicated(keep='last')]

    slack_array = slack_array.merge(max_p_mw_500, left_index=True, right_index=True, how='left').sum(axis=1).values

    return slack_array


def make_PTDF(net_init, select_idx=[], config=None, distributed_slack=False):
    ppc = make_ppc(net_init, select_idx=select_idx, config=config)

    if distributed_slack:
        # 按照500kV煤机的容量分配平衡机
        slack = get_gen_slack_bus(net_init)
    else:
        slack = None

    ptdf_sparse = makePTDF(ppc["baseMVA"], ppc["bus"], ppc["branch"],
                           using_sparse_solver=True,
                           slack=slack)

    ptdf_sparse[np.abs(ptdf_sparse) < 1e-4] = 0

    return ptdf_sparse[select_idx, :]


def make_PTDF_LODF(net_init, select_idx=[], config=None, distributed_slack=False):
    ppc = make_ppc(net_init, select_idx=select_idx, config=config)

    if distributed_slack:
        # 按照500kV煤机的容量分配平衡机
        slack = get_gen_slack_bus(net_init)
    else:
        slack = None

    ptdf_sparse = makePTDF(ppc["baseMVA"], ppc["bus"], ppc["branch"],
                           using_sparse_solver=True,
                           slack=slack)

    ptdf_sparse[np.abs(ptdf_sparse) < 1e-4] = 0

    lodf = makeLODF(ppc["branch"], ptdf_sparse)

    return ptdf_sparse[select_idx, :], lodf[select_idx, :]


if __name__ == '__main__':
    net = pp.from_json('../data/2021XY-JS.json')

    new_line = net.line.merge(net.bus.loc[:, 'vn_kv'], left_on='from_bus', right_index=True)
    selected_line_idx = new_line[new_line.vn_kv > 200].index
    selected_trafo_idx = net.trafo[net.trafo.vn_hv_kv > 500].index
    selected_branch_idx = list(np.append(selected_line_idx, selected_trafo_idx + len(net.line)))

    ptdf = make_PTDF_dict(net, selected_branch_idx)

    print()
