"""
推演分析计算功能
"""
import numpy as np
import pandapower as pp
import alg.cal_ssmetrics.base_func as basef
from alg.ProductionCostSimulation.run_calculator_utils.ss_calculator_base import (cal_simu_boundary_delta,
                                                                                  cal_device_rate,
                                                                                  calc_area_level_psm,
                                                                                  simu_indicators_cal,
                                                                                  get_trafo_details,
                                                                                  ana_power_supply_strategy)
from alg.get_graph_info.get_net_topo import get_final_format_data, get_final_format_data_keydev
from alg.common_utils.data_utils import get_simulation_power_boundry


def ss_calculator(net: pp.pandapowerNet,
                  result_dict: dict,
                  config: dict,
                  user_input, area_name='全省', area_details: dict = dict(),
                  indicators_rlt_dict: dict = dict()
                  ):
    """
    进行实时的分区/全网的推演计算分析--突破机组装机的限制;但是线路和主变的限额约束限制仍然存在:
    1、先进行用户设定的基础出力分配和处理；
    2、生成设备等基础出力后,
    3、重新计算6个维度的指标/指标结果/分区供电裕度;如果是全网要给出所有分区的裕度指标;否则无需计算;
    4、重新计算直流潮流;
    5、计算设备(线路、主变、断面)的重载、过载负载率
    如果是全省推演，分区的指标会重新计算; 对分区只需要重新计算除了conf['metrics_all_to_zone']以外的数据,目前只有供电裕度和外来电依存度;
    Args:
        net:网络;
        result_dict: 计算结果arrs
        timestep: 推演分析的时刻点(0-8760)
        user_input: 用户输入的机组/负荷目标出力数据;load/gas_output/feedin/wind_output/solar_output // add:trafo_limit_ratio
        user_input['gen_output']['area_or_region_choice']: str or None
        此处feedin里只有直流信息;
        area_name: 全省 or 分区名--zone_name;
        area_details: 分区关联设备索引信息: data['case_info']['zone']['device_relay']
        # boundary_original: 算例-分区该时刻的初始机组/负荷出力时序数据;
        # metric_comp: 该算例该时刻的初始保供6维指标计算过程数据;
        # thresholds: 指标计算的分级门槛值信息;
        # indicators_nominal_ori: 6维指标结果初始值(旧程序中为:baogongliuwei_nominal_chushizhi);
        indicators_rlt_dict:dict(),指标计算结果-包含上面四类数据;

    Returns:
        dict()

        'thresholds': 裕度分级门槛值信息,
        'baogongliuwei_nominal_chushizhi': dict() 分区/全网的指标结果初始值,
        'baogongliuwei_nominal': dict() 分区/全网的指标结果新推演结果值,
        'original': boundary_original_t,  # 边界总出力数据
        'qs_fenqu_psm_value': dict , # 全网/分区的供电裕度; 分区时,空
        'qs_fenqu_psm_color': dict , # 全网/分区的供电裕度着色,空,
        'trafo_details': trafo_details
        '辅助决策源侧': dict_gen_output,
        '辅助决策网侧': load_transfer,
        '辅助决策荷侧': demand_man,

        #地图潮流信息;
        'graph_data':
        {
            'point_data': list(),
            'line_data': list()
        }

        #设备负载率信息;
        'chart_dict':
        {
        "line": dict(),key:'name', 'power', 'p_rate', 'limit', 'rate_change'
        "trafo": dict(),
        "interface": dict()
        },  根据:电压等级、负载率,变化率排序;
        'added_device_info': added_device_info   # 加装的移相器和串抗效果信息；
    """
    timestep = user_input["timestep"]

    # step1: 提取用户输入-并计算推演分析的边界变化：delta = 用户目标值 - 初始值
    # 电网边界数据变化; 全网就在全网内分配;如果分区推演就只在分区内进行分配;
    boundary_original = indicators_rlt_dict['boundary_data']  # 边界数据;
    power_delta, gen_output_init = cal_simu_boundary_delta(net, config, result_dict, user_input, boundary_original,
                                                           area_name, area_details)

    """
    step2：重新计算进行潮流计算:输入（万千瓦）,将调整量分配到所有的机组/设备上,然后rundcpp,获取线路主变潮流有功和节点有功及潮流分布;
    基于net\result_dict\power_delta(user_input_gen)生成潮流计算的时刻断面潮流文件
    分摊用户输出的边界差值到各个设备;
    不管分区还是全网,需要输出线路/主变/断面的新负载率,都需要重新计算潮流;
    需要全面考虑: 1) 输入调整细节；2）区内直流调整(等值为负荷调整;);3、推演时刻gen_on_off的设备状态置为off；4、N-1的故障设备；5）添加的移相器、串抗设备
    计算全网新的潮流分布,返回： 新的ptdf,地图topo数据,支路设备负载率信息，支路潮流有功
    """
    # logger.info('start cal_device _rate in get_trafo_details')
    ptdf, graph_data, branch_rate, branch_power = cal_device_rate(net, result_dict, config,
                                                                  user_input_init=user_input,
                                                                  target_time=timestep,
                                                                  power_delta=power_delta,
                                                                  area_name=area_name,
                                                                  area_details=area_details
                                                                  )
    # logger.info('branch power recalculated with cal_device _rate')

    # step3: 计算当前计算分区的指标
    metric_comp = indicators_rlt_dict['indicator_base_data']  # 指标计算基础数据
    metric_comp_area = metric_comp[area_name]  # dict,用于计算指标的基础数据提取
    # a.该时刻的指标初始值提取
    ori_ind_timestep, thresholds_timestep = get_boundary_original_timestep(indicators_rlt_dict, timestep,
                                                                           area_name=area_name)
    ori_index_dict, output_nomimal = simu_indicators_cal(ori_ind_timestep, area_name, power_delta, metric_comp_area,
                                                         timestep, config)  # 指标计算初始基础数据、新的指标计算结果

    # step4: 6个维度的指标 + 设备(线路/主变/断面的新潮流及负载率)
    if area_name == '全省' or area_name is None:
        # 4-1 如果是全网需要重新计算各个分区的供电裕度;--目前全网时:重新推演时只计算了其余分区的供电裕度指标;
        # graph_data 地图潮流数据,如果是分区,会在后面的分区主变下送能力计算中更新此数据
        # branch_rate 断面/主变/线路的潮流负载率信息,同上
        thresholds = indicators_rlt_dict['indicator_thresholds']  # 指标分级阈值
        area_psm_value, area_psm_color, _, _ = calc_area_level_psm(
            net,
            result_dict,
            config,
            user_input=user_input,  # 用户输入;
            power_delta_qs=power_delta,
            metric_comp_areas=metric_comp,
            b_original_areas=boundary_original,
            thresholds_areas=thresholds,
            area_details=area_details,  # 分区关联设备索引;
            gen_list_dict=gen_output_init['gen_list'] if ('gen_output' in user_input.keys()) else None,
            branch_power=branch_power,  # new_power
            ptdf=ptdf,  # ptdf
        )
        trafo_details = dict()
    else:
        area_psm_value = dict()  # 存放全网模式下的各分区的供电裕度,当只是分区推演的时候不需要此变量为dict()
        area_psm_color = dict()

        # 重新计算分区设备负载(线路/主变/断面的新潮流及负载率)
        # 分子：负荷 - 风电 - 光伏 - 可调出力 / 分母：原负荷 - 原可调出力
        trafo_details = get_trafo_details(net,config, result_dict, area_details,
                                          area_name=area_name,
                                          timestep=timestep,
                                          user_input_area=user_input,
                                          branch_power=branch_power, ptdf=ptdf)
        psm_comp = ori_index_dict['power_supply_margin']  # 原始的指标值;
        psm_ = output_nomimal['power_supply_margin']  # 新的计算的供电裕度值--后面--针对分区需要根据主变的容量及潮流重新进行修正
        factor = ((psm_comp[3] + power_delta['load']) - (
                psm_comp[1] + power_delta['gen_output'] + psm_comp[2] + power_delta['wind_output'] + power_delta[
            'solar_output']
        )) / (psm_comp[3] - psm_comp[2] - psm_comp[1])
        for k, v in trafo_details.items():
            if k == 'power_recev_cap':
                continue
            trafo_details[k]['load_ratio'] = np.clip(factor * np.array(v['load_ratio']), 0, None).round(4).tolist()
            trafo_details[k]['total_power'] = round(float(factor * v['total_power']),4)
            trafo_details[k]['distri_power'] = (factor * np.array(v['distri_power'])).round(4).tolist()
            trafo_details[k]['total_limit_usage'] = round(float(trafo_details[k]['total_power'] / trafo_details[k]['total_limit']),4)
        # 更新供电裕度数值
        psm_ += trafo_details['power_recev_cap'] - psm_comp[0]
        output_nomimal['power_supply_margin'] = round(float(psm_),4)
        # print('供电裕度（主变推演前）：', psm[0], '供电裕度（主变推演后）：', psm_)

    # 最终统计新的机组output出力;--例如调整负荷时，煤电机组出力会发生变化
    rlt_output = dict()

    # 辅助决策,三级保供策略: 机组调整、负荷转移、负荷需求响应;
    dict_gen_output, load_transfer, demand_man = ana_power_supply_strategy()

    # 最后处理输出数据格式
    point_data, line_data, dcline_data = get_final_format_data(graph_data)
    # point_data_n1, line_data_n1 = get_final_format_data_2(graph_data)  # N-1设备前后信息;
    point_data_n1, line_data_n1 = get_final_format_data_keydev(graph_data['N-1flag'], point_data,
                                                               line_data)
    if len(point_data_n1) > 0:
        point_name_list_2 = [v['name'] for v in point_data_n1]
        _point_data = [v for v in point_data if (v['name'] not in point_name_list_2)]
    else:
        _point_data = point_data
    if len(line_data_n1) > 0:
        line_name_list_2 = [f"{v['fromName']}-{v['toName']}" for v in line_data_n1]
        _line_data = [v for v in line_data if (f"{v['fromName']}-{v['toName']}" not in line_name_list_2)]
    else:
        _line_data = line_data
    topo_graph_dict = {
        'point_data': _point_data,
        'line_data': _line_data,
        'dcline_data': dcline_data,
        'point_data_n1': point_data_n1,
        'line_data_n1': line_data_n1,
    }

    # 添加的移相器和串抗相关结果信息
    added_device_info = dict()
    added_device_types = ['phase_shifter_arg', 'add_reactance_arg']
    for type_ in added_device_types:
        if type_ in user_input.keys():
            line_indices = []
            for dic in line_data:
                for line_name, line_ind in dic['info'].items():
                    if 'line_index' in user_input[type_].keys():
                        if line_ind == user_input[type_]['line_index']:
                            line_indices = dic['info'].values()

            if 'line_index' in user_input[type_].keys():
                user_input[type_]['line_index'] = [int(ind) for ind in line_indices]
            added_device_info[type_] = user_input[type_]

    # 修正topo中的point_data的name
    for index, item in enumerate(topo_graph_dict['point_data']):
        topo_graph_dict['point_data'][index]['name'] = "" if item['name'].startswith('T站') else item['name']
    for index, item in enumerate(topo_graph_dict['point_data_n1']):
        topo_graph_dict['point_data_n1'][index]['name'] = "" if item['name'].startswith('T站') else item['name']
    # for index, item in enumerate(topo_graph_dict['line_data_n1']):
    #     topo_graph_dict['line_data_n1'][index]['value'] = 0.0
    # output del: 'input_range': boundary_range,
    # baogongliuwei_nominal_chushizhi： 全网所有分区的初始指标值;
    # baogongliuwei_nominal：新的计算的计算分区/全网的指标结果
    # output_normalized: 指标标幺化结果,去除
    # qs_fenqu_psm_value\qs_fenqu_psm_color: 全网推演模式下的各分区的供电裕度和分级结果非全网是,为空dict
    # graph_data: 地图潮流数据, point_data/line_data,
    # chart_dict: 主变/断面/线路设备有功及负载率,---需要和获取的过滤方法保持一致，保持设备的顺序一致,方便前端进行变化幅度的计算和处理
    # decision_source: 辅助决策源侧
    # decision_grid: 辅助决策网侧
    # decision_load: 辅助决策负荷侧

    output = {
        'thresholds': thresholds_timestep,
        'baogongliuwei_nominal_chushizhi': ori_ind_timestep,
        'original': get_simulation_power_boundry(
            boundary_original=boundary_original, area_name=area_name, timestep=timestep)["original"],
        'rlt_output': rlt_output,
        'graph_data': topo_graph_dict,
        'chart_dict': branch_rate,
        'added_device_info': added_device_info,
        'decision_source': dict_gen_output,
        'decision_grid': load_transfer,
        'decision_load': demand_man,
        'baogongliuwei_nominal': output_nomimal,  # 分区6维指标;
        'qs_fenqu_psm_value': area_psm_value,
        'qs_fenqu_psm_color': area_psm_color,
        'trafo_details': trafo_details}

    return output


def get_boundary_original_timestep(indicators_rlt_dict, timestep, area_name: str = basef.GLOBAL_QW_NAME):
    """
    获取
    :return:
    """
    boundary_original = indicators_rlt_dict['boundary_data']  # 边界数据;
    metric_comp = indicators_rlt_dict['indicator_base_data']  # 指标计算基础数据
    indicators_nominal_ori = indicators_rlt_dict['indicator_data']  # 指标结果数据
    thresholds = indicators_rlt_dict['indicator_thresholds']  # 指标分级阈值

    ori_indicator_timestep = dict()
    for area in indicators_nominal_ori.keys():
        ori_indicator_timestep[area] = dict()
        for index_name in indicators_nominal_ori[area].keys():
            ori_indicator_timestep[area][index_name] = indicators_nominal_ori[area][index_name][timestep]

    # b.该时刻的指标分级临界值提取
    thresholds_timestep = dict()
    for zhibiao_name in thresholds[area_name].keys():
        thresholds_timestep[zhibiao_name] = thresholds[area_name][zhibiao_name][timestep]

    return ori_indicator_timestep, thresholds_timestep
