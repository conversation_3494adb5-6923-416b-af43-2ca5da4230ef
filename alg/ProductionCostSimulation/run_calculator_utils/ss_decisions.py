"""
辅助决策分析
"""
import os
import math
import numpy as np
import pandas as pd
from alg.ProductionCostSimulation.run_calculator_utils.ss_calculator_base import (df_to_json)

def ss_decisions(net, res, area_details, area_names_list=None,
                 area_psm_value=None,
                 timestep=None,
                 upload_dir=None):
    # 辅助决策分析,源网荷侧
    # 源侧
    # 建议机组出力

    # 找出分区内开机机组，建议出力水平
    # res, net, _ = importlib.import_module("res_prob").res_tuple
    res_load = pd.DataFrame(res['load']).loc[:, timestep]
    res = pd.DataFrame(res['gen_output'])
    # area_details = RedisJson.get('area_details_' + time_result)
    area_gen_qs = net.gen

    dict_gen_output = dict()
    trans_demand = dict()
    for area in area_names_list:

        # 目标裕度5万
        psm_neg = area_psm_value[area] - 5
        area_gen = pd.DataFrame(**area_details[area]['area_gen'])
        area_gen = area_gen[np.isin(area_gen.index, area_gen_qs.index)]

        df_gen_output = pd.DataFrame(index=['开机机组名称', '装机容量', '当前出力'],
                                     data=[area_gen['fullname'].values,
                                           area_gen['max_p_mw'].values,
                                           res.loc[area_gen.index, timestep].values]).T
        # print(area, 'df_gen_output', df_gen_output)
        # 筛选出开机机组，并按容量降序排列
        df_gen_output = df_gen_output[df_gen_output['当前出力'] > 0]
        df_gen_output['剩余容量'] = df_gen_output['装机容量'] - df_gen_output['当前出力']
        df_gen_output['新增出力需求总量'] = (-10 * psm_neg)
        df_gen_output = df_gen_output.sort_values(by=['装机容量'], ascending=False)
        df_gen_output['累计可增加出力'] = df_gen_output['剩余容量'].cumsum(skipna=True)
        # print(area, 'df_gen_output', df_gen_output)
        df_gen_output = df_gen_output[df_gen_output['新增出力需求总量'] >= df_gen_output['累计可增加出力']]
        # print(area, 'df_gen_output', df_gen_output)
        df_gen_output['建议出力'] = df_gen_output['当前出力'] + df_gen_output['剩余容量']
        df_gen_output['增加出力'] = df_gen_output['建议出力'] - df_gen_output['当前出力']
        # print(area, area_gen)
        # print(area, 'df_gen_output', df_gen_output[['新增出力需求总量','累计可增加出力']])
        trans_demand[area] = (df_gen_output['新增出力需求总量'] - df_gen_output['累计可增加出力']).min() / 10
        # 如果无开机机组，造成df为空，则用psm_neg作为转供需求
        if math.isnan(trans_demand[area]):
            trans_demand[area] = (-10 * psm_neg) / 10

        df_gen_output_ = df_gen_output.loc[:, ['开机机组名称', '装机容量', '当前出力', '增加出力', '建议出力']].groupby(
            ['开机机组名称']).sum() / 10
        df_gen_output_ = df_gen_output_.reset_index()

        dict_gen_output[area] = df_to_json(df_gen_output_, with_index=False)

    #######################

    # 网侧
    # 转供
    load_transfer = dict()
    trans_mapping = pd.read_csv(os.path.join(upload_dir, "area_load_transfer_2023.csv"), index_col=0).reset_index(
        drop=True).ffill()
    # 当作分区盐城中不存在
    trans_mapping = trans_mapping[trans_mapping['转供分区'] != '盐城中']
    trans_mapping = trans_mapping[trans_mapping['受供分区'] != '盐城中']
    trans_mapping = trans_mapping.reset_index(drop=True)

    for area in area_names_list:
        trans_export, trans_demand_meet = area_load_transfer(area=area,
                                                             trans=trans_mapping,
                                                             trans_demand=trans_demand[area],
                                                             area_details=area_details,
                                                             area_psm_value=area_psm_value,
                                                             res_load=res_load)
        trans_export = df_to_json(trans_export)

        load_transfer[area] = {'转供映射': trans_export,
                               '转供供需': trans_demand_meet}
    #######################

    # 荷侧;需求侧响应
    demand_man = {}
    for area in area_names_list:
        load_cut = load_transfer[area]['转供供需']['剩余缺口']
        load_cut_round_up = int(5 * math.ceil(load_cut / 5))
        demand_man[area] = {'需求侧响应': load_cut_round_up,
                            '采取保供措施后分区裕度': round(5 + load_cut_round_up - load_cut)}
    return dict_gen_output, load_transfer, demand_man


def area_load_transfer(area=None,
                       trans=None,
                       trans_demand=None,
                       area_details=None,
                       area_psm_value=None,
                       res_load=None):
    # 转供
    # print('转供分区', area)jsbg_rate
    # print('转供需求', trans_demand)
    trans = trans[trans['转供分区'] == area]

    # 先检查转供站BPA节点是否带负荷
    area_load = pd.DataFrame(**area_details[area]['area_load'])
    bus_with_load_ind = []
    bus_with_load_original_ind = []
    for ind_int, bus_name in enumerate(trans['转供站BPA节点'].tolist()):
        bus_with_load = area_load.reset_index().set_index('name').filter(like=bus_name, axis=0)
        if len(bus_with_load) > 0:
            bus_with_load_ind.append(ind_int)
            bus_with_load_original_ind.append(bus_with_load['index'].values[0])
    trans = trans.iloc[bus_with_load_ind, :]

    # 将剩余分区按照裕度降序排列
    trans['受供分区裕度'] = pd.Series(area_psm_value)[trans['受供分区']].values
    trans = trans.sort_values(by=['受供分区裕度'], ascending=False)

    # print('转供df', trans)

    # 按照负荷转移比例计算允许的转移量
    trans['转供总需求'] = trans_demand
    trans['节点负荷'] = res_load.iloc[bus_with_load_original_ind].values / 10
    trans['节点负荷转移需求'] = trans['转移比例'] * trans['节点负荷']
    trans['裕度允许的节点负荷转移量'] = (trans[['受供分区裕度', '节点负荷转移需求']]).min(axis=1)

    # print('转供df', trans)
    # trans

    # 剔除有缺口分区；按需求测算应转移量；剔除超过需求部分
    trans = trans[trans['受供分区裕度'] > 0]
    trans['累计允许的负荷转移量'] = trans['裕度允许的节点负荷转移量'].cumsum(axis=0)
    trans['累计剩余转移需求'] = trans['转供总需求'] - trans['累计允许的负荷转移量']
    # trans

    trans['节点负荷应转移量'] = trans['裕度允许的节点负荷转移量'] + trans['累计剩余转移需求'].clip(lower=None, upper=0)
    # trans

    # print('转供df', trans)

    trans = trans[trans['节点负荷应转移量'] >= 0]

    # print('转供df', trans)
    # trans

    # 导出对应关系
    trans_export = trans[['转供分区', '受供分区', '转供站', '受供站', '节点负荷应转移量']]

    # 如果没有可转移量
    transfer_v = trans['节点负荷应转移量'].sum()
    try:
        transfer_v = transfer_v / 1
    except ValueError:
        transfer_v = 0.
    # print('transfer_v', transfer_v)

    trans_demand_meet = {'转供总需求': trans_demand,
                         '负荷转移总量': transfer_v,
                         '剩余缺口': trans_demand - transfer_v}

    return trans_export, trans_demand_meet
