
# 第一阶段编译前端代码
FROM registry.tode.ltd/node:16.14 AS builder

COPY --chown=root:root ./baogong-web  /app/
WORKDIR /app/
RUN npm i --registry=https://registry.npmmirror.com
RUN npm run build

COPY --chown=root:root ./baogong-admin  /admin/
WORKDIR /admin/
RUN npm i --registry=https://registry.npmmirror.com
RUN npm run build:prod

# 第二阶段，打包后端代码
FROM registry.tode.ltd/baogong-base:bin

# 拷贝所有代码进入容器
COPY --from=builder /app/dist /dist/
COPY --from=builder /admin/dist /admin/
COPY --chown=root:root ./  /app/
# 工作目录
WORKDIR /app/
RUN rm -rf baogong-admin
RUN rm -rf baogong-web
RUN mkdir -p alg/logs


# service dir
EXPOSE 8000/tcp

CMD [ "./start2.sh"]