"""empty message

Revision ID: b46b50d2e2fa
Revises: 
Create Date: 2024-12-09 22:01:35.607647

"""
from typing import Sequence
from typing import Union

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = 'b46b50d2e2fa'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        'sys_dev_id_map_tab', sa.Column('dev_id', sa.String(length=128), nullable=True, comment='测点id'),
        sa.Column('area_name', sa.String(length=256), nullable=True, comment='区域名称'),
        sa.Column('area_id', sa.String(length=64), nullable=True, comment='区域id'),
        sa.Column('field_key', sa.String(length=16), nullable=True, comment='数据类型'),
        sa.Column('data_desc', sa.String(length=64), nullable=True, comment='数据描述'),
        sa.Column('display_desc', sa.String(length=64), nullable=True, comment='数据描述'),
        sa.Column('real_func_need', sa.Boolean(), nullable=True, comment='是否实时态需求'),
        sa.Column('his_func_need', sa.Boolean(), nullable=True, comment='是否历史态需求'),
        sa.Column('id', sa.String(length=64), nullable=False),
        sa.Column('create_time', sa.DateTime(), nullable=True, comment='创建时间'),
        sa.Column('update_time', sa.DateTime(), nullable=True, comment='更新时间'),
        sa.Column('is_delete', sa.SmallInteger(), nullable=True, comment='逻辑删除,0:未删除,1:已删除'),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_sys_dev_id_map_tab_area_id'), 'sys_dev_id_map_tab', ['area_id'], unique=False)
    op.create_index(op.f('ix_sys_dev_id_map_tab_area_name'), 'sys_dev_id_map_tab', ['area_name'], unique=False)
    op.create_index(op.f('ix_sys_dev_id_map_tab_dev_id'), 'sys_dev_id_map_tab', ['dev_id'], unique=False)
    op.create_index(op.f('ix_sys_dev_id_map_tab_field_key'), 'sys_dev_id_map_tab', ['field_key'], unique=False)
    op.create_table(
        'sys_real_day_data_tab', sa.Column('power_id', sa.String(length=64), nullable=True, comment='关联的主表id'),
        sa.Column('time', sa.DateTime(), nullable=True, comment='时刻'),
        sa.Column('value', sa.FLOAT(), nullable=True, comment='数据值'),
        sa.Column('id', sa.String(length=64), nullable=False),
        sa.Column('create_time', sa.DateTime(), nullable=True, comment='创建时间'),
        sa.Column('update_time', sa.DateTime(), nullable=True, comment='更新时间'),
        sa.Column('is_delete', sa.SmallInteger(), nullable=True, comment='逻辑删除,0:未删除,1:已删除'),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(
        op.f('ix_sys_real_day_data_tab_power_id'), 'sys_real_day_data_tab', ['power_id'], unique=False
    )
    op.create_index(op.f('ix_sys_real_day_data_tab_time'), 'sys_real_day_data_tab', ['time'], unique=False)
    op.create_table(
        'sys_real_day_index_tab', sa.Column('area_name', sa.String(length=256), nullable=True, comment='区域名称'),
        sa.Column('field_key', sa.String(length=128), nullable=True, comment='字段key'),
        sa.Column('day_time', sa.Date(), nullable=True, comment='日期'),
        sa.Column('dev_id', sa.String(length=128), nullable=True, comment='测点id'),
        sa.Column('display_desc', sa.String(length=128), nullable=True, comment='数据描述'),
        sa.Column('id', sa.String(length=64), nullable=False),
        sa.Column('create_time', sa.DateTime(), nullable=True, comment='创建时间'),
        sa.Column('update_time', sa.DateTime(), nullable=True, comment='更新时间'),
        sa.Column('is_delete', sa.SmallInteger(), nullable=True, comment='逻辑删除,0:未删除,1:已删除'),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(
        op.f('ix_sys_real_day_index_tab_area_name'), 'sys_real_day_index_tab', ['area_name'], unique=False
    )
    op.create_index(
        op.f('ix_sys_real_day_index_tab_day_time'), 'sys_real_day_index_tab', ['day_time'], unique=False
    )
    op.create_index(op.f('ix_sys_real_day_index_tab_dev_id'), 'sys_real_day_index_tab', ['dev_id'], unique=False)
    op.create_index(
        op.f('ix_sys_real_day_index_tab_field_key'), 'sys_real_day_index_tab', ['field_key'], unique=False
    )
    op.create_table(
        'sys_short_qs_per_day', sa.Column('day', sa.Date(), nullable=True, comment='日期'),
        sa.Column('is_exist', sa.SmallInteger(), nullable=True, comment='1: 已存在，其他值：为不存在'),
        sa.Column('id', sa.String(length=64), nullable=False),
        sa.Column('create_time', sa.DateTime(), nullable=True, comment='创建时间'),
        sa.Column('update_time', sa.DateTime(), nullable=True, comment='更新时间'),
        sa.Column('is_delete', sa.SmallInteger(), nullable=True, comment='逻辑删除,0:未删除,1:已删除'),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_sys_short_qs_per_day_day'), 'sys_short_qs_per_day', ['day'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_sys_short_qs_per_day_day'), table_name='sys_short_qs_per_day')
    op.drop_table('sys_short_qs_per_day')
    op.drop_index(op.f('ix_sys_real_day_index_tab_field_key'), table_name='sys_real_day_index_tab')
    op.drop_index(op.f('ix_sys_real_day_index_tab_dev_id'), table_name='sys_real_day_index_tab')
    op.drop_index(op.f('ix_sys_real_day_index_tab_day_time'), table_name='sys_real_day_index_tab')
    op.drop_index(op.f('ix_sys_real_day_index_tab_area_name'), table_name='sys_real_day_index_tab')
    op.drop_table('sys_real_day_index_tab')
    op.drop_index(op.f('ix_sys_real_day_data_tab_time'), table_name='sys_real_day_data_tab')
    op.drop_index(op.f('ix_sys_real_day_data_tab_power_id'), table_name='sys_real_day_data_tab')
    op.drop_table('sys_real_day_data_tab')
    op.drop_index(op.f('ix_sys_dev_id_map_tab_field_key'), table_name='sys_dev_id_map_tab')
    op.drop_index(op.f('ix_sys_dev_id_map_tab_dev_id'), table_name='sys_dev_id_map_tab')
    op.drop_index(op.f('ix_sys_dev_id_map_tab_area_name'), table_name='sys_dev_id_map_tab')
    op.drop_index(op.f('ix_sys_dev_id_map_tab_area_id'), table_name='sys_dev_id_map_tab')
    op.drop_table('sys_dev_id_map_tab')
    # ### end Alembic commands ###
