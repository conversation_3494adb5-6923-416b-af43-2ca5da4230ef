"""empty message

Revision ID: 3ce52e764cb2
Revises: b46b50d2e2fa
Create Date: 2024-12-19 17:04:32.725718

"""
from typing import Sequence
from typing import Union

import sqlalchemy as sa
from sqlalchemy.dialects import mysql

from alembic import op

# revision identifiers, used by Alembic.
revision: str = '3ce52e764cb2'
down_revision: Union[str, None] = 'b46b50d2e2fa'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        'sys_short_qs', sa.Column('value', sa.SmallInteger(), nullable=True, comment='0:不需要立刻生成, 1:需要生成'),
        sa.Column('id', sa.String(length=64), nullable=False),
        sa.Column('create_time', sa.DateTime(), nullable=True, comment='创建时间'),
        sa.Column('update_time', sa.DateTime(), nullable=True, comment='更新时间'),
        sa.Column('is_delete', sa.SmallInteger(), nullable=True, comment='逻辑删除,0:未删除,1:已删除'),
        sa.PrimaryKeyConstraint('id')
    )
    op.drop_index('ix_sys_short_qs_per_day_day', table_name='sys_short_qs_per_day')
    op.drop_table('sys_short_qs_per_day')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        'sys_short_qs_per_day',
        sa.Column('day', sa.DATE(), nullable=True, comment='日期'),
        sa.Column(
            'is_exist',
            mysql.SMALLINT(display_width=6),
            autoincrement=False,
            nullable=True,
            comment='1: 已存在，其他值：为不存在'
        ),
        sa.Column('id', mysql.VARCHAR(length=64), nullable=False),
        sa.Column('create_time', mysql.DATETIME(), nullable=True, comment='创建时间'),
        sa.Column('update_time', mysql.DATETIME(), nullable=True, comment='更新时间'),
        sa.Column(
            'is_delete',
            mysql.SMALLINT(display_width=6),
            autoincrement=False,
            nullable=True,
            comment='逻辑删除,0:未删除,1:已删除'
        ),
        sa.PrimaryKeyConstraint('id'),
        mysql_default_charset='utf8mb4',
        mysql_engine='InnoDB'
    )
    op.create_index('ix_sys_short_qs_per_day_day', 'sys_short_qs_per_day', ['day'], unique=False)
    op.drop_table('sys_short_qs')
    # ### end Alembic commands ###
