import os
import sys
from logging.config import fileConfig

from sqlalchemy import engine_from_config
from sqlalchemy import pool

from alembic import context

sys.path.append(os.path.dirname(os.path.dirname(__file__)))
from database import Base

# this is the Alembic Config object, which provides
# access to the values within the .ini file in use.
config = context.config

# Interpret the config file for Python logging.
# This line sets up loggers basically.
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

# add your model's MetaData object here
# for 'autogenerate' support
# from myapp import mymodel
# target_metadata = mymodel.Base.metadata
target_metadata = Base.metadata

# other values from the config, defined by the needs of env.py,
# can be acquired:
# my_important_option = config.get_main_option("my_important_option")
# ... etc.


def run_migrations_offline() -> None:
    """Run migrations in 'offline' mode.

    This configures the context with just a URL
    and not an Engine, though an Engine is acceptable
    here as well.  By skipping the Engine creation
    we don't even need a DBAPI to be available.

    Calls to context.execute() here emit the given string to the
    script output.

    """
    database_pwd = os.environ.get('db_pwd', "tode20200426")
    database_host = os.getenv("db_host", "*************")
    database_port = os.getenv("db_port", 35676)
    database_name = os.getenv("db_name", "baogong")
    database_user = os.getenv("db_user", "root")
    url = f"mysql+pymysql://{database_user}:{database_pwd}@{database_host}:{database_port}/{database_name}?charset=utf8mb4"
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
    )

    with context.begin_transaction():
        context.run_migrations()


def run_migrations_online() -> None:
    """Run migrations in 'online' mode.

    In this scenario we need to create an Engine
    and associate a connection with the context.

    """
    conf = config.get_section(config.config_ini_section, {})
    database_pwd = os.environ.get('db_pwd', "tode20200426")
    database_host = os.getenv("db_host", "*************")
    database_port = os.getenv("db_port", 35676)
    database_name = os.getenv("db_name", "baogong")
    database_user = os.getenv("db_user", "root")
    url = f"mysql+pymysql://{database_user}:{database_pwd}@{database_host}:{database_port}/{database_name}?charset=utf8mb4"
    conf['sqlalchemy.url'] = url
    connectable = engine_from_config(
        conf,
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,
    )

    with connectable.connect() as connection:
        context.configure(connection=connection, target_metadata=target_metadata)

        with context.begin_transaction():
            context.run_migrations()


if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
